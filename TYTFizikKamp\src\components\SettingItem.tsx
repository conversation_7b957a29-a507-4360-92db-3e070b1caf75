import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import { Theme } from '../contexts/ThemeContext';

interface SettingItemProps {
  icon: string;
  title: string;
  subtitle?: string;
  rightComponent?: React.ReactNode;
  onPress?: () => void;
  theme: Theme;
  showArrow?: boolean;
  isDestructive?: boolean;
}

const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  subtitle,
  rightComponent,
  onPress,
  theme,
  showArrow = false,
  isDestructive = false,
}) => {
  const handlePress = () => {
    if (onPress) {
      onPress();
    }
  };

  const getIconColor = () => {
    if (isDestructive) return theme.colors.error;
    return theme.colors.primary;
  };

  const getTitleColor = () => {
    if (isDestructive) return theme.colors.error;
    return theme.colors.text;
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { borderBottomColor: theme.colors.border },
      ]}
      onPress={handlePress}
      disabled={!onPress}
      activeOpacity={onPress ? 0.7 : 1}
    >
      <View style={styles.leftSection}>
        <View style={[styles.iconContainer, { backgroundColor: `${getIconColor()}20` }]}>
          <MaterialIcons name={icon} size={20} color={getIconColor()} />
        </View>
        
        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: getTitleColor() }]}>
            {title}
          </Text>
          {subtitle && (
            <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
              {subtitle}
            </Text>
          )}
        </View>
      </View>

      <View style={styles.rightSection}>
        {rightComponent}
        {showArrow && (
          <Icon
            name="chevron-right"
            size={20}
            color={theme.colors.textSecondary}
            style={styles.arrow}
          />
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 0.5,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 13,
    lineHeight: 18,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrow: {
    marginLeft: 8,
  },
});

export default SettingItem;
