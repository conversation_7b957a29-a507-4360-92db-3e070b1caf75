import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Switch,
  Alert,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import { useTheme } from '../contexts/ThemeContext';
import SettingItem from '../components/SettingItem';
import StorageService from '../services/StorageService';
import NotificationService, { NotificationSettings } from '../services/NotificationService';

const { width } = Dimensions.get('window');

interface SettingsScreenProps {
  navigation: any;
}

const SettingsScreen: React.FC<SettingsScreenProps> = ({ navigation }) => {
  const { theme, isDark, toggleTheme } = useTheme();
  const [notifications, setNotifications] = useState(true);
  const [soundEffects, setSoundEffects] = useState(true);
  const [hapticFeedback, setHapticFeedback] = useState(true);
  const [autoSave, setAutoSave] = useState(true);
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    enabled: true,
    pomodoroEnabled: true,
    studyRemindersEnabled: true,
    dailyGoalReminders: true,
    reminderTime: '20:00',
  });
  const [userStats, setUserStats] = useState({
    totalQuestions: 0,
    correctAnswers: 0,
    streak: 0,
    totalPoints: 0,
  });

  useEffect(() => {
    loadSettings();
    loadUserStats();
    loadNotificationSettings();
  }, []);

  const loadSettings = async () => {
    try {
      // Ayarları AsyncStorage'dan yükle
      // Bu örnekte varsayılan değerler kullanılıyor
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const loadNotificationSettings = async () => {
    try {
      const settings = await NotificationService.getSettings();
      setNotificationSettings(settings);
      setNotifications(settings.enabled);
    } catch (error) {
      console.error('Error loading notification settings:', error);
    }
  };

  const updateNotificationSettings = async (newSettings: Partial<NotificationSettings>) => {
    try {
      const updatedSettings = { ...notificationSettings, ...newSettings };
      await NotificationService.saveSettings(updatedSettings);
      setNotificationSettings(updatedSettings);

      // Haptic feedback
      NotificationService.triggerHapticFeedback('impactLight');
    } catch (error) {
      console.error('Error updating notification settings:', error);
    }
  };

  const loadUserStats = async () => {
    try {
      const userProgress = await StorageService.getUserProgress();
      setUserStats({
        totalQuestions: userProgress.total_questions_answered,
        correctAnswers: userProgress.total_correct_answers,
        streak: userProgress.current_streak,
        totalPoints: userProgress.total_points,
      });
    } catch (error) {
      console.error('Error loading user stats:', error);
    }
  };

  const handleResetProgress = () => {
    Alert.alert(
      'İlerlemeyi Sıfırla',
      'Tüm ilerlemeniz silinecek. Bu işlem geri alınamaz. Emin misiniz?',
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Sıfırla',
          style: 'destructive',
          onPress: async () => {
            try {
              await StorageService.resetProgress();
              Alert.alert('Başarılı', 'İlerlemeniz sıfırlandı.');
              loadUserStats();
            } catch (error) {
              Alert.alert('Hata', 'İlerleme sıfırlanırken bir hata oluştu.');
            }
          },
        },
      ]
    );
  };

  const handleExportData = () => {
    Alert.alert(
      'Veri Dışa Aktarma',
      'Bu özellik yakında eklenecek!',
      [{ text: 'Tamam' }]
    );
  };

  const handleAbout = () => {
    Alert.alert(
      'TYT Fizik Kampı',
      'Versiyon: 1.0.0\n\n14 günlük yoğun fizik eğitimi ile TYT\'ye hazırlan!\n\nGeliştirici: Augment Agent',
      [{ text: 'Tamam' }]
    );
  };

  return (
    <LinearGradient
      colors={theme.colors.gradient.background}
      style={styles.container}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: 'rgba(255,255,255,0.2)' }]}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Ayarlar</Text>
          <Text style={styles.headerSubtitle}>Uygulamayı özelleştir</Text>
        </View>

        <View style={styles.headerRight}>
          <MaterialIcons name="settings" size={24} color="white" />
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* User Stats Card */}
        <View style={[styles.statsCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.statsTitle, { color: theme.colors.text }]}>
            İstatistiklerim
          </Text>
          
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.primary }]}>
                {userStats.totalQuestions}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                Toplam Soru
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.success }]}>
                {userStats.correctAnswers}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                Doğru Cevap
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.warning }]}>
                {userStats.streak}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                Günlük Seri
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.info }]}>
                {userStats.totalPoints}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                Toplam Puan
              </Text>
            </View>
          </View>
        </View>

        {/* Appearance Settings */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Görünüm
          </Text>
          
          <SettingItem
            icon="dark-mode"
            title="Karanlık Mod"
            subtitle="Gece kullanımı için ideal"
            rightComponent={
              <Switch
                value={isDark}
                onValueChange={toggleTheme}
                trackColor={{ false: '#E9ECEF', true: theme.colors.primary }}
                thumbColor={isDark ? '#FFFFFF' : '#F4F3F4'}
              />
            }
            theme={theme}
          />
        </View>

        {/* Notification Settings */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Bildirimler
          </Text>

          <SettingItem
            icon="notifications"
            title="Tüm Bildirimler"
            subtitle="Ana bildirim kontrolü"
            rightComponent={
              <Switch
                value={notificationSettings.enabled}
                onValueChange={(value) => {
                  updateNotificationSettings({ enabled: value });
                  setNotifications(value);
                }}
                trackColor={{ false: '#E9ECEF', true: theme.colors.primary }}
                thumbColor={notificationSettings.enabled ? '#FFFFFF' : '#F4F3F4'}
              />
            }
            theme={theme}
          />

          <SettingItem
            icon="timer"
            title="Pomodoro Bildirimleri"
            subtitle="Çalışma ve mola bildirimleri"
            rightComponent={
              <Switch
                value={notificationSettings.pomodoroEnabled}
                onValueChange={(value) => updateNotificationSettings({ pomodoroEnabled: value })}
                trackColor={{ false: '#E9ECEF', true: theme.colors.primary }}
                thumbColor={notificationSettings.pomodoroEnabled ? '#FFFFFF' : '#F4F3F4'}
                disabled={!notificationSettings.enabled}
              />
            }
            theme={theme}
          />

          <SettingItem
            icon="school"
            title="Çalışma Hatırlatıcıları"
            subtitle="Günlük ders hatırlatmaları"
            rightComponent={
              <Switch
                value={notificationSettings.studyRemindersEnabled}
                onValueChange={(value) => updateNotificationSettings({ studyRemindersEnabled: value })}
                trackColor={{ false: '#E9ECEF', true: theme.colors.primary }}
                thumbColor={notificationSettings.studyRemindersEnabled ? '#FFFFFF' : '#F4F3F4'}
                disabled={!notificationSettings.enabled}
              />
            }
            theme={theme}
          />

          <SettingItem
            icon="flag"
            title="Hedef Hatırlatıcıları"
            subtitle="İlerleme ve başarı bildirimleri"
            rightComponent={
              <Switch
                value={notificationSettings.dailyGoalReminders}
                onValueChange={(value) => updateNotificationSettings({ dailyGoalReminders: value })}
                trackColor={{ false: '#E9ECEF', true: theme.colors.primary }}
                thumbColor={notificationSettings.dailyGoalReminders ? '#FFFFFF' : '#F4F3F4'}
                disabled={!notificationSettings.enabled}
              />
            }
            theme={theme}
          />

          <TouchableOpacity
            style={[styles.testButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => NotificationService.sendTestNotification()}
          >
            <MaterialIcons name="notifications-active" size={20} color="white" />
            <Text style={styles.testButtonText}>Test Bildirimi Gönder</Text>
          </TouchableOpacity>
        </View>

        {/* Audio & Haptic Settings */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Ses ve Titreşim
          </Text>
          
          <SettingItem
            icon="volume-up"
            title="Ses Efektleri"
            subtitle="Buton sesleri ve geri bildirimler"
            rightComponent={
              <Switch
                value={soundEffects}
                onValueChange={setSoundEffects}
                trackColor={{ false: '#E9ECEF', true: theme.colors.primary }}
                thumbColor={soundEffects ? '#FFFFFF' : '#F4F3F4'}
              />
            }
            theme={theme}
          />
          
          <SettingItem
            icon="vibration"
            title="Dokunsal Geri Bildirim"
            subtitle="Titreşim ile geri bildirim"
            rightComponent={
              <Switch
                value={hapticFeedback}
                onValueChange={setHapticFeedback}
                trackColor={{ false: '#E9ECEF', true: theme.colors.primary }}
                thumbColor={hapticFeedback ? '#FFFFFF' : '#F4F3F4'}
              />
            }
            theme={theme}
          />
        </View>

        {/* Data Settings */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Veri Yönetimi
          </Text>
          
          <SettingItem
            icon="save"
            title="Otomatik Kaydetme"
            subtitle="İlerlemeyi otomatik kaydet"
            rightComponent={
              <Switch
                value={autoSave}
                onValueChange={setAutoSave}
                trackColor={{ false: '#E9ECEF', true: theme.colors.primary }}
                thumbColor={autoSave ? '#FFFFFF' : '#F4F3F4'}
              />
            }
            theme={theme}
          />
          
          <SettingItem
            icon="file-download"
            title="Verileri Dışa Aktar"
            subtitle="İlerlemenizi yedekleyin"
            onPress={handleExportData}
            theme={theme}
            showArrow
          />
          
          <SettingItem
            icon="refresh"
            title="İlerlemeyi Sıfırla"
            subtitle="Tüm verileri sil"
            onPress={handleResetProgress}
            theme={theme}
            showArrow
            isDestructive
          />
        </View>

        {/* About Section */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Hakkında
          </Text>
          
          <SettingItem
            icon="info"
            title="Uygulama Hakkında"
            subtitle="Versiyon ve geliştirici bilgileri"
            onPress={handleAbout}
            theme={theme}
            showArrow
          />
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
            TYT Fizik Kampı v1.0.0
          </Text>
          <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
            Augment Agent ile geliştirildi
          </Text>
        </View>
      </ScrollView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    color: 'white',
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 2,
  },
  headerRight: {
    width: 40,
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  statsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 15,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 15,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 15,
    borderRadius: 15,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    padding: 20,
    paddingBottom: 10,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  footerText: {
    fontSize: 12,
    marginBottom: 4,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 15,
    marginHorizontal: 15,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    gap: 8,
  },
  testButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SettingsScreen;
