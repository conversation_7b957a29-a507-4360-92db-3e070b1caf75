"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {
  trigger: true
};
exports.trigger = exports.default = void 0;
var _NativeHapticFeedback = _interopRequireDefault(require("./codegenSpec/NativeHapticFeedback"));
var _types = require("./types");
Object.keys(_types).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _types[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _types[key];
    }
  });
});
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const defaultOptions = {
  enableVibrateFallback: false,
  ignoreAndroidSystemSettings: false
};
const RNHapticFeedback = {
  trigger(type = _types.HapticFeedbackTypes.selection, options = {}) {
    try {
      _NativeHapticFeedback.default.trigger(type, {
        ...defaultOptions,
        ...options
      });
    } catch {
      console.warn("RNReactNativeHapticFeedback is not available");
    }
  }
};
const {
  trigger
} = RNHapticFeedback;
exports.trigger = trigger;
var _default = exports.default = RNHapticFeedback;
//# sourceMappingURL=index.js.map