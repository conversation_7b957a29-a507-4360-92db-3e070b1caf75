// Lesson Types
export interface Question {
  id: number;
  soru: string;
  secenekler: string[];
  cevap: string;
  aciklama: string;
  isAnswered: boolean;
}

export interface BackReference {
  soru: string;
  cevap: string;
  ipucu: string;
}

export interface ReportCard {
  cozulen_soru: number;
  zorluk: string;
  geri_bildirim: string;
}

export interface Lesson {
  id: number;
  gun: number;
  konu: string;
  video_link: string;
  ozet: string;
  gercek_hayat: string;
  geri_baglanti: BackReference | null;
  sorular: Question[];
  motivasyon: string;
  karnesi: ReportCard;
  isUnlocked: boolean;
  isCompleted: boolean;
}
// types.ts dosyasına ekleyin
export interface QuizState {
  currentQuestionIndex: number;
  answers: Record<number, string>;
  score: number;
  timeRemaining: number;
  isCompleted: boolean;
  wrongQuestions?: number[];
  retryMode?: boolean;
  retryQuestions?: any[];
}
// Navigation Types
export type RootStackParamList = {
  Home: undefined;
  DayDetail: { dayId: number };
  Quiz: { dayId: number };
  Profile: undefined;
  Settings: undefined;
};

export type BottomTabParamList = {
  HomeTab: undefined;
  ProfileTab: undefined;
  SettingsTab: undefined;
};

// Component Props Types
export interface DailyCardProps {
  day: number;
  title: string;
  completed: boolean;
  locked: boolean;
  progress: number;
  onPress: () => void;
}

export interface LockedDayCardProps {
  day: number;
  locked: boolean;
  unlockCondition: string;
}

export interface ProgressBarProps {
  progress: number;
  total?: number;
  color?: string;
  backgroundColor?: string;
  height?: number;
}

export interface QuestionCardProps {
  question: Question;
  onAnswer: (questionId: number, selectedAnswer: string, isCorrect: boolean) => void;
  disabled?: boolean;
  selectedAnswer?: string;
}

export interface SolutionModalProps {
  isVisible: boolean;
  question: Question;
  selectedAnswer: string;
  isCorrect: boolean;
  onClose: () => void;
  onNext: () => void;
  isLastQuestion?: boolean;
  onFinishQuiz?: () => void;
}

// Badge Types
export interface Badge {
  id: string;
  name: string;
  icon: string;
  description: string;
  condition: string;
  type: 'completion' | 'streak' | 'score' | 'special';
  requirement: number;
  points: number;
  earned?: boolean;
  earnedAt?: string;
}

// User Progress Types
export interface QuestionAnswer {
  questionId: number;
  isCorrect: boolean;
  answeredAt: Date;
  selectedAnswer: string;
}

export interface DayProgress {
  dayId: number;
  isCompleted: boolean;
  completedAt?: Date;
  questionsAnswered: QuestionAnswer[];
  score: number;
  timeSpent: number;
}

export interface QuizResult {
  totalQuestions: number;
  correctAnswers: number;
  wrongAnswers: number;
  skippedAnswers: number;
  successRate: number;
  isPassed: boolean;
  wrongQuestions: number[];
  timeSpent: number;
  completedAt: Date;
  attempts: number;
}

export interface UserProgress {
  completed_days: number[];
  current_streak: number;
  best_streak: number;
  total_questions_answered: number;
  total_correct_answers: number;
  total_points: number;
  last_activity: Date;
  day_progress: { [dayId: number]: DayProgress };
  badges_earned: string[]; // Badge ID'leri
  quiz_results?: { [dayId: number]: QuizResult }; // Quiz sonuçları
  wrong_questions?: { [dayId: number]: number[] }; // Yanlış sorular
  total_questions_solved?: number; // Toplam çözülen soru sayısı
  quiz_progress?: { [dayId: number]: {
    currentQuestionIndex: number;
    answers: { [questionId: number]: string };
    score: number;
    timeRemaining: number;
    lastSaved: Date;
  }}; // Kaldığı yerden devam için
}

// Theme Types
export interface Colors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  error: string;
  success: string;
  warning: string;
  disabled: string;
}

export interface Theme {
  colors: Colors;
  fonts: {
    regular: string;
    medium: string;
    bold: string;
    light: string;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
}

// Quiz Types
export interface QuizState {
  currentQuestionIndex: number;
  answers: { [questionId: number]: string };
  score: number;
  timeRemaining: number;
  isCompleted: boolean;
}

export interface QuizResult {
  dayId: number;
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeSpent: number;
  completedAt: Date;
}

// Statistics Types
export interface DayStatistics {
  dayId: number;
  questionsAttempted: number;
  questionsCorrect: number;
  timeSpent: number;
  completedAt?: string;
  score: number;
}

export interface OverallStatistics {
  totalDaysCompleted: number;
  totalQuestionsAnswered: number;
  totalCorrectAnswers: number;
  averageScore: number;
  currentStreak: number;
  bestStreak: number;
  totalTimeSpent: number;
  badgesEarned: number;
}

// Settings Types
export interface AppSettings {
  darkMode: boolean;
  nightModeAuto: boolean;
  soundEnabled: boolean;
  notificationsEnabled: boolean;
  reminderTime: string;
  language: 'tr' | 'en';
  fontSize: 'small' | 'medium' | 'large';
}

// Animation Types
export interface AnimationConfig {
  duration: number;
  easing: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out';
  delay?: number;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// API Response Types (for future use)
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: AppError;
  timestamp: string;
}

// Notification Types
export interface NotificationData {
  id: string;
  title: string;
  body: string;
  type: 'reminder' | 'achievement' | 'streak' | 'general';
  scheduledFor: string;
  data?: any;
}

// Achievement Types
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  type: 'streak' | 'score' | 'completion' | 'special';
  requirement: number;
  progress: number;
  unlocked: boolean;
  unlockedAt?: string;
}

// All types are already exported above with export interface/type
