{"name": "@react-native-community/push-notification-ios", "version": "1.11.0", "description": "React Native Push Notification API for iOS", "main": "js/index.js", "types": "index.d.ts", "author": "<PERSON> <raf<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>", "contributors": ["<PERSON> <<EMAIL>>"], "homepage": "https://github.com/react-native-community/push-notification-ios#readme", "license": "MIT", "files": ["android", "ios", "js", "index.d.ts", "RNCPushNotificationIOS.podspec"], "scripts": {"start": "react-native start", "android": "cd example && react-native run-android", "ios": "cd example && react-native run-ios", "test": "yarn test:flow && yarn test:js", "test:flow": "flow check", "test:js": "echo 0", "lint": "eslint \"js/*.js\" --cache", "test:tsc": "tsc --noEmit"}, "keywords": ["react-native", "react native", "push notification ios", "push notification", "notification ios", "notification"], "peerDependencies": {"react": ">=16.6.3", "react-native": ">=0.58.4"}, "dependencies": {"invariant": "^2.2.4"}, "devDependencies": {"@babel/core": "^7.0.0", "@react-native-community/eslint-config": "^3.0.0", "babel-jest": "^27.0.6", "babel-plugin-module-resolver": "^3.1.3", "eslint": "^7.32.0", "flow-bin": "0.137.0", "jest": "^27.0.6", "metro-react-native-babel-preset": "^0.64.0", "react": "17.0.1", "react-native": "0.64.2", "typescript": "^3.9.5"}, "repository": {"type": "git", "url": "https://github.com/react-native-community/push-notification-ios.git"}}