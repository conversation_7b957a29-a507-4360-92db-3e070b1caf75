import AsyncStorage from '@react-native-async-storage/async-storage';
import BadgeService, { BadgeProgress } from '../src/services/BadgeService';
import { UserProgress } from '../src/types';

// AsyncStorage mock
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}));

// Mock badges data
jest.mock('../src/data/badges.json', () => [
  {
    id: 'first_day',
    name: '<PERSON><PERSON>ım',
    icon: '🎯',
    description: 'İlk günü tamamla',
    condition: 'İlk günü başarıyla tamamla',
    type: 'completion',
    requirement: 1,
    points: 10,
  },
  {
    id: 'streak_3',
    name: '3 Günlük Seri',
    icon: '🔥',
    description: '3 gün üst üste çalış',
    condition: '3 günlük seri yap',
    type: 'streak',
    requirement: 3,
    points: 25,
  },
  {
    id: 'perfect_score',
    name: '<PERSON><PERSON><PERSON><PERSON>l Skor',
    icon: '💯',
    description: 'Bir günde %100 başarı',
    condition: 'Bir günde tüm soruları doğru çöz',
    type: 'score',
    requirement: 100,
    points: 50,
  },
]);

describe('BadgeService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getBadgeProgress', () => {
    it('should return initial progress when no data exists', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const progress = await BadgeService.getBadgeProgress();

      expect(progress).toHaveLength(3);
      expect(progress[0]).toEqual({
        badgeId: 'first_day',
        progress: 0,
        isEarned: false,
      });
    });

    it('should return stored progress when data exists', async () => {
      const mockProgress: BadgeProgress[] = [
        {
          badgeId: 'first_day',
          progress: 1,
          isEarned: true,
          earnedDate: new Date('2024-01-01'),
        },
        {
          badgeId: 'streak_3',
          progress: 2,
          isEarned: false,
        },
      ];

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockProgress));

      const progress = await BadgeService.getBadgeProgress();

      expect(progress[0].isEarned).toBe(true);
      expect(progress[0].earnedDate).toEqual(new Date('2024-01-01'));
    });
  });

  describe('checkAndAwardBadges', () => {
    it('should award first day badge when first day is completed', async () => {
      const mockBadgeProgress: BadgeProgress[] = [
        {
          badgeId: 'first_day',
          progress: 0,
          isEarned: false,
        },
        {
          badgeId: 'streak_3',
          progress: 0,
          isEarned: false,
        },
        {
          badgeId: 'perfect_score',
          progress: 0,
          isEarned: false,
        },
      ];

      const mockUserProgress: UserProgress = {
        completed_days: [1],
        current_streak: 1,
        best_streak: 1,
        total_questions_answered: 10,
        total_correct_answers: 10,
        total_points: 100,
        last_activity: new Date(),
        day_progress: {
          1: {
            dayId: 1,
            isCompleted: true,
            completedAt: new Date(),
            questionsAnswered: [
              {
                questionId: 1,
                isCorrect: true,
                answeredAt: new Date(),
                selectedAnswer: 'A',
              },
            ],
            score: 100,
            timeSpent: 300,
          },
        },
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockBadgeProgress));
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const newBadges = await BadgeService.checkAndAwardBadges(mockUserProgress);

      expect(newBadges).toHaveLength(2); // first_day ve perfect_score
      expect(newBadges.some(badge => badge.id === 'first_day')).toBe(true);
      expect(newBadges.some(badge => badge.id === 'perfect_score')).toBe(true);
    });

    it('should award streak badge when streak requirement is met', async () => {
      const mockBadgeProgress: BadgeProgress[] = [
        {
          badgeId: 'first_day',
          progress: 1,
          isEarned: true,
          earnedDate: new Date(),
        },
        {
          badgeId: 'streak_3',
          progress: 2,
          isEarned: false,
        },
        {
          badgeId: 'perfect_score',
          progress: 0,
          isEarned: false,
        },
      ];

      const mockUserProgress: UserProgress = {
        completed_days: [1, 2, 3],
        current_streak: 3,
        best_streak: 3,
        total_questions_answered: 30,
        total_correct_answers: 25,
        total_points: 250,
        last_activity: new Date(),
        day_progress: {},
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockBadgeProgress));
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const newBadges = await BadgeService.checkAndAwardBadges(mockUserProgress);

      expect(newBadges).toHaveLength(1);
      expect(newBadges[0].id).toBe('streak_3');
    });

    it('should not award already earned badges', async () => {
      const mockBadgeProgress: BadgeProgress[] = [
        {
          badgeId: 'first_day',
          progress: 1,
          isEarned: true,
          earnedDate: new Date(),
        },
        {
          badgeId: 'streak_3',
          progress: 0,
          isEarned: false,
        },
        {
          badgeId: 'perfect_score',
          progress: 0,
          isEarned: false,
        },
      ];

      const mockUserProgress: UserProgress = {
        completed_days: [1],
        current_streak: 1,
        best_streak: 1,
        total_questions_answered: 10,
        total_correct_answers: 8,
        total_points: 80,
        last_activity: new Date(),
        day_progress: {},
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockBadgeProgress));
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const newBadges = await BadgeService.checkAndAwardBadges(mockUserProgress);

      expect(newBadges).toHaveLength(0);
    });
  });

  describe('getEarnedBadges', () => {
    it('should return only earned badges', async () => {
      const mockBadgeProgress: BadgeProgress[] = [
        {
          badgeId: 'first_day',
          progress: 1,
          isEarned: true,
          earnedDate: new Date(),
        },
        {
          badgeId: 'streak_3',
          progress: 2,
          isEarned: false,
        },
        {
          badgeId: 'perfect_score',
          progress: 0,
          isEarned: false,
        },
      ];

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockBadgeProgress));

      const earnedBadges = await BadgeService.getEarnedBadges();

      expect(earnedBadges).toHaveLength(1);
      expect(earnedBadges[0].id).toBe('first_day');
    });
  });

  describe('getBadgeStats', () => {
    it('should return correct badge statistics', async () => {
      const mockBadgeProgress: BadgeProgress[] = [
        {
          badgeId: 'first_day',
          progress: 1,
          isEarned: true,
          earnedDate: new Date(),
        },
        {
          badgeId: 'streak_3',
          progress: 2,
          isEarned: false,
        },
        {
          badgeId: 'perfect_score',
          progress: 0,
          isEarned: false,
        },
      ];

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockBadgeProgress));

      const stats = await BadgeService.getBadgeStats();

      expect(stats).toEqual({
        total: 3,
        earned: 1,
        percentage: 33,
        totalPoints: 10,
      });
    });
  });

  describe('getTotalBadgePoints', () => {
    it('should calculate total points from earned badges', async () => {
      const mockBadgeProgress: BadgeProgress[] = [
        {
          badgeId: 'first_day',
          progress: 1,
          isEarned: true,
          earnedDate: new Date(),
        },
        {
          badgeId: 'streak_3',
          progress: 3,
          isEarned: true,
          earnedDate: new Date(),
        },
        {
          badgeId: 'perfect_score',
          progress: 0,
          isEarned: false,
        },
      ];

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockBadgeProgress));

      const totalPoints = await BadgeService.getTotalBadgePoints();

      expect(totalPoints).toBe(35); // 10 + 25
    });
  });

  describe('getBadgeById', () => {
    it('should return badge by id', async () => {
      const badge = await BadgeService.getBadgeById('first_day');

      expect(badge).toBeDefined();
      expect(badge?.id).toBe('first_day');
      expect(badge?.name).toBe('İlk Adım');
    });

    it('should return null for non-existent badge', async () => {
      const badge = await BadgeService.getBadgeById('non_existent');

      expect(badge).toBeNull();
    });
  });
});
