# ninja log v5
0	37	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86_64/CMakeFiles/cmake.verify_globs	f73f422419a24615
173	2795	7735954159413918	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	2d570285735d19bc
217	2767	7735954159049712	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	30d24efedf9ad9c8
6031	12512	7735954256481862	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/ComponentDescriptors.cpp.o	ceceaf7b4ce1592c
3274	7929	7735954210615538	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/ShadowNodes.cpp.o	4a623f0b757cf878
33	927	7736086312593161	build.ninja	f60cf6c37ccd3242
246	4440	7735954175635844	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/72f994d6b4d4ae1617cc1e36268935c9/safeareacontext/RNCSafeAreaViewState.cpp.o	d549bc6a8b34d0b8
9	3274	7735954163833295	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	5a31af35cd2d2735
205	3314	7735954164329706	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	ec25d178cdc0bc6d
269	3701	7735954168298113	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/EventEmitters.cpp.o	ca7d0c97fa5977a0
7549	10670	7735954238240128	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	d1ee4722c837ce1
11	1984	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	731fecb920f3b275
194	3678	7735954167757288	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	202db0591e7dfbea
1984	2442	7736062366174295	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libappmodules.so	4898c26db5ff2777
184	3858	7735954169640121	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	49bd34bb13cf2c4e
238	4212	7735954173247869	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	66bd0b8fafcbdd88
189	4034	7735954171136390	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	cb40ca50a6735e4b
179	4357	7735954174967747	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	3272e62f84656896
6685	14725	7735954278363438	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	e204d4f5b4a5e609
225	4243	7735954173798411	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	9cafb01282e63325
162	4323	7735954174547581	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	ee05caa0b32e9525
7210	11418	7735954245703041	CMakeFiles/appmodules.dir/OnLoad.cpp.o	efec84edef528cb8
8338	11464	7735954246235339	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	2089de11a0e68d98
3679	6685	7735954198228193	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/States.cpp.o	50c73cad559a6267
200	4871	7735954179948755	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	5ff6f888d4171987
253	5714	7735954188302054	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewShadowNode.cpp.o	1d96cc0aa8f6568a
211	6031	7735954191468114	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o	f80d248d686ea73b
2768	7491	7735954206201902	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/Props.cpp.o	c18d62371271d17e
4357	7209	7735954203488367	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/States.cpp.o	8025788519f77adc
2795	6653	7735954197842869	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/safeareacontextJSI-generated.cpp.o	a0e9f333893dd6b8
3858	7694	7735954208312810	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	ad9093f3bd1b1e03
3315	7549	7735954206537667	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2925fb9a73caffbf95f70fc9e227ae4e/codegen/jni/safeareacontext-generated.cpp.o	5f80729e6489dc66
4034	8750	7735954218704704	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	7766b35acaa38433
4243	7983	7735954210899187	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	5b4bf3cd389340c0
4212	9161	7735954222625710	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSModalScreenShadowNode.cpp.o	8b4693b5bd368b74
6654	10398	7735954235447243	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0e8eb62b16777f5f0cc7c33df312f9ed/react/renderer/components/rnscreens/RNSScreenState.cpp.o	37f13a7b7b25ccaf
3722	8338	7735954214805541	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	c3dfe8e95eb66c2
12512	12698	7735954258431455	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libreact_codegen_rnscreens.so	7346ab55f2f32660
4324	9286	7735954224253661	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	976738312e7871ea
261	5697	7735954187756546	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/ComponentDescriptors.cpp.o	38142357a1a5af1
3702	8582	7735954217077583	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3bba978b2157660a9a8f04f82fd394ad/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	144016dd69a97c46
0	24	0	clean	783ee98371267ada
7930	8613	7735954216200974	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libreact_codegen_safeareacontext.so	360bc2b587f78329
7984	11271	7735954244260179	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/rnscreensJSI-generated.cpp.o	e851ed4ee05418ef
8751	11092	7735954242496170	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	40e90893d78df47b
4871	9898	7735954230521784	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	81cde53311c24d1
7492	11202	7735954243528962	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	26731339e79f349
8582	11598	7735954247485712	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o	6f8e0c1f18c72865
7694	11530	7735954246853721	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	7f92cd23773cb0c1
4440	9038	7735954221612994	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d2daae902f606f04fd11325c49840ca/react/renderer/components/rnscreens/ShadowNodes.cpp.o	28e896081167c53
8616	11809	7735954249698667	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	d8a815185f4e5a90
5715	10867	7735954240030234	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/Props.cpp.o	326d7d892be1434c
5697	11386	7735954245280282	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d2daae902f606f04fd11325c49840ca/react/renderer/components/rnscreens/EventEmitters.cpp.o	5778bfefa497b67a
1	31	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86_64/CMakeFiles/cmake.verify_globs	f73f422419a24615
261	6489	7736086378652262	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	30d24efedf9ad9c8
203	6601	7736086378436255	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	2d570285735d19bc
6520	8623	7736086402525601	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/Props.cpp.o	e69c7625220a7a82
6602	8787	7736086404020371	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/RNHapticFeedbackSpec-generated.cpp.o	4529a7ec66a747d
8623	10036	7736086416657301	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/States.cpp.o	6e5d9fd1823e14ce
241	10634	7736086421169867	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	ec25d178cdc0bc6d
191	10821	7736086422638287	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	5a31af35cd2d2735
8787	10918	7736086425382117	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ShadowNodes.cpp.o	d50b5843e7806c42
294	12165	7736086436254485	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/EventEmitters.cpp.o	951af4f95409a476
10831	13037	0	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/RNHapticFeedbackSpecJSI-generated.cpp.o	48d32d62c5ef2308
10635	13190	7736086448114746	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/72f994d6b4d4ae1617cc1e36268935c9/safeareacontext/RNCSafeAreaViewState.cpp.o	d549bc6a8b34d0b8
12167	13960	7736086455882569	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/States.cpp.o	50c73cad559a6267
13190	15125	7736086467484263	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/safeareacontextJSI-generated.cpp.o	a0e9f333893dd6b8
13037	15432	7736086470580722	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/ShadowNodes.cpp.o	4a623f0b757cf878
10037	16346	7736086477629410	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewShadowNode.cpp.o	1d96cc0aa8f6568a
13961	16425	7736086479103133	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/Props.cpp.o	c18d62371271d17e
197	16442	7736086478869179	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	49bd34bb13cf2c4e
15126	16896	7736086485159631	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/EventEmitters.cpp.o	ca7d0c97fa5977a0
219	17039	7736086484862024	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	202db0591e7dfbea
254	17195	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	731fecb920f3b275
15432	17577	7736086491988875	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2925fb9a73caffbf95f70fc9e227ae4e/codegen/jni/safeareacontext-generated.cpp.o	5f80729e6489dc66
10918	18042	7736086494670927	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/ComponentDescriptors.cpp.o	38142357a1a5af1
277	18285	7736086497779696	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	66bd0b8fafcbdd88
18048	18493	7736086500844907	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libreact_codegen_safeareacontext.so	360bc2b587f78329
225	18762	7736086502756419	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	cb40ca50a6735e4b
211	19257	7736086507676184	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	3272e62f84656896
16443	19430	7736086510512203	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	7766b35acaa38433
17046	19754	7736086513760192	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	ad9093f3bd1b1e03
248	19974	7736086514421932	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	9cafb01282e63325
16347	20252	7736086517374020	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	976738312e7871ea
17577	20302	7736086518331622	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	5b4bf3cd389340c0
16426	20476	7736086519776660	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3bba978b2157660a9a8f04f82fd394ad/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	144016dd69a97c46
185	20517	7736086519829415	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	ee05caa0b32e9525
286	20818	7736086524088143	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/7f9d75c760005477a9a92acb25e45305/ComponentDescriptors.cpp.o	19f88d49cb71954a
16896	21123	7736086526614485	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSModalScreenShadowNode.cpp.o	8b4693b5bd368b74
178	21257	7736086528315210	CMakeFiles/appmodules.dir/OnLoad.cpp.o	2e7d8abe9a8f4be1
18493	21408	7736086529995778	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0e8eb62b16777f5f0cc7c33df312f9ed/react/renderer/components/rnscreens/RNSScreenState.cpp.o	37f13a7b7b25ccaf
20257	23295	7736086547897738	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	d1ee4722c837ce1
20481	23297	7736086547458794	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	40e90893d78df47b
20523	23298	7736086548007084	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/States.cpp.o	8025788519f77adc
269	23312	7736086545830686	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	5ff6f888d4171987
17196	23595	7736086550967562	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/Props.cpp.o	326d7d892be1434c
19994	23725	7736086553069719	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o	6f8e0c1f18c72865
20828	23817	7736086554233080	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/rnscreensJSI-generated.cpp.o	e851ed4ee05418ef
20303	24283	7736086557998783	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	2089de11a0e68d98
21409	24301	7736086558901647	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	26731339e79f349
233	25045	7736086564427184	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o	f80d248d686ea73b
19755	26030	7736086575382054	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	d8a815185f4e5a90
21259	26179	7736086576979576	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	7f92cd23773cb0c1
21127	27262	7736086587947454	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d2daae902f606f04fd11325c49840ca/react/renderer/components/rnscreens/ShadowNodes.cpp.o	28e896081167c53
19430	27745	7736086592657340	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	c3dfe8e95eb66c2
19264	28188	7736086597491304	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	81cde53311c24d1
18763	29853	7736086613791619	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d2daae902f606f04fd11325c49840ca/react/renderer/components/rnscreens/EventEmitters.cpp.o	5778bfefa497b67a
18292	32897	7736086644044493	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/ComponentDescriptors.cpp.o	ceceaf7b4ce1592c
32898	33131	7736086647408868	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libreact_codegen_rnscreens.so	7346ab55f2f32660
20	41498	7736086729090274	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	782172c22f4a6e64
41499	42057	7736086736640312	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libappmodules.so	4dd6b129146375fc
