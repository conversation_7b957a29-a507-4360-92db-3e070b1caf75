# ninja log v5
1	30	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86_64/CMakeFiles/cmake.verify_globs	f73f422419a24615
202	3583	7734432551787824	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	2d570285735d19bc
8603	12820	7734432644681746	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	d1ee4722c837ce1
3584	7544	7734432591772063	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/EventEmitters.cpp.o	ca7d0c97fa5977a0
249	3859	7734432554799890	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	ec25d178cdc0bc6d
307	5120	7734432567274256	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/72f994d6b4d4ae1617cc1e36268935c9/safeareacontext/RNCSafeAreaViewState.cpp.o	d549bc6a8b34d0b8
214	3871	7734432554994673	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	5a31af35cd2d2735
4516	9973	7734432615784065	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/ShadowNodes.cpp.o	4a623f0b757cf878
263	3626	7734432552673075	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	30d24efedf9ad9c8
5359	13908	7734432655381379	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/ComponentDescriptors.cpp.o	ceceaf7b4ce1592c
10	1764	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	731fecb920f3b275
1764	2227	7735868782568903	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libappmodules.so	4898c26db5ff2777
224	4596	7734432561743195	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	202db0591e7dfbea
218	4395	7734432560196695	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	49bd34bb13cf2c4e
286	4786	7734432564032856	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	66bd0b8fafcbdd88
230	4672	7734432563001019	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	cb40ca50a6735e4b
5154	15828	7734432674278088	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	e204d4f5b4a5e609
197	5027	7734432566418257	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	3272e62f84656896
270	4922	7734432565582760	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	9cafb01282e63325
207	5154	7734432567499029	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	ee05caa0b32e9525
10444	13874	7734432655293859	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	2089de11a0e68d98
40	5016	7734432566373390	CMakeFiles/appmodules.dir/OnLoad.cpp.o	efec84edef528cb8
3871	7173	7734432586649635	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/States.cpp.o	50c73cad559a6267
236	6180	7734432577982412	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	5ff6f888d4171987
296	6779	7734432584139064	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewShadowNode.cpp.o	1d96cc0aa8f6568a
255	6420	7734432580124341	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o	f80d248d686ea73b
6181	9419	7734432610219354	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/States.cpp.o	8025788519f77adc
316	5358	7734432569953734	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/Props.cpp.o	c18d62371271d17e
4395	8602	7734432602054765	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/safeareacontextJSI-generated.cpp.o	a0e9f333893dd6b8
4596	8833	7734432604109908	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	ad9093f3bd1b1e03
4786	10016	7734432616476216	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	7766b35acaa38433
3860	7959	7734432595460190	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2925fb9a73caffbf95f70fc9e227ae4e/codegen/jni/safeareacontext-generated.cpp.o	5f80729e6489dc66
5121	9364	7734432609697108	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	5b4bf3cd389340c0
7959	12483	7734432640446468	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0e8eb62b16777f5f0cc7c33df312f9ed/react/renderer/components/rnscreens/RNSScreenState.cpp.o	37f13a7b7b25ccaf
4673	10282	7734432618780511	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSModalScreenShadowNode.cpp.o	8b4693b5bd368b74
13908	14249	7734432658852334	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libreact_codegen_rnscreens.so	7346ab55f2f32660
5028	10788	7734432624326925	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	c3dfe8e95eb66c2
4923	10443	7734432620271514	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	976738312e7871ea
3627	10225	7734432617855639	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/ComponentDescriptors.cpp.o	38142357a1a5af1
0	34	0	clean	783ee98371267ada
5016	10861	7734432624336915	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3bba978b2157660a9a8f04f82fd394ad/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	144016dd69a97c46
9421	13318	7734432649728090	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/rnscreensJSI-generated.cpp.o	e851ed4ee05418ef
10225	11229	7734432626747891	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libreact_codegen_safeareacontext.so	360bc2b587f78329
7544	13503	7734432651540598	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	81cde53311c24d1
10283	13172	7734432648298681	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	40e90893d78df47b
8834	13512	7734432651655905	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	26731339e79f349
10016	13722	7734432653758688	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o	6f8e0c1f18c72865
9364	13958	7734432656072036	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	7f92cd23773cb0c1
7174	12735	7734432643736594	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d2daae902f606f04fd11325c49840ca/react/renderer/components/rnscreens/ShadowNodes.cpp.o	28e896081167c53
9979	14021	7734432656719089	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	d8a815185f4e5a90
6420	12649	7734432642107194	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/Props.cpp.o	326d7d892be1434c
6780	13617	7734432652591652	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d2daae902f606f04fd11325c49840ca/react/renderer/components/rnscreens/EventEmitters.cpp.o	5778bfefa497b67a
1	33	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86_64/CMakeFiles/cmake.verify_globs	f73f422419a24615
21	1947	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	731fecb920f3b275
1948	2455	7735875853950739	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libappmodules.so	4898c26db5ff2777
4	37	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86_64/CMakeFiles/cmake.verify_globs	f73f422419a24615
24	1894	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	731fecb920f3b275
1895	2352	7735886384413135	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libappmodules.so	4898c26db5ff2777
1	37	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86_64/CMakeFiles/cmake.verify_globs	f73f422419a24615
26	2092	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	731fecb920f3b275
2092	2680	7735892054853013	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libappmodules.so	4898c26db5ff2777
2	34	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86_64/CMakeFiles/cmake.verify_globs	f73f422419a24615
20	2107	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	731fecb920f3b275
2107	2609	7735894661357103	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libappmodules.so	4898c26db5ff2777
2	35	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86_64/CMakeFiles/cmake.verify_globs	f73f422419a24615
27	2150	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	731fecb920f3b275
2150	2696	7735899978324930	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libappmodules.so	4898c26db5ff2777
1	35	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86_64/CMakeFiles/cmake.verify_globs	f73f422419a24615
21	2102	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	731fecb920f3b275
2103	2596	7735901486488576	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libappmodules.so	4898c26db5ff2777
1	32	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86_64/CMakeFiles/cmake.verify_globs	f73f422419a24615
18	1950	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	731fecb920f3b275
1950	2414	7735920959055096	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libappmodules.so	4898c26db5ff2777
1	39	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86_64/CMakeFiles/cmake.verify_globs	f73f422419a24615
31	2142	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	731fecb920f3b275
2142	2642	7735927384740239	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86_64/libappmodules.so	4898c26db5ff2777
