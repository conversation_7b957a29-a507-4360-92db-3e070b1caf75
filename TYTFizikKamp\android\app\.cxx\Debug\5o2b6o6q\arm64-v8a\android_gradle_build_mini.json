{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native-haptic-feedback\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\.cxx\\Debug\\5o2b6o6q\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\.cxx\\Debug\\5o2b6o6q\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"artifactName": "react_codegen_RNVectorIconsSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\cxx\\Debug\\5o2b6o6q\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b6a6ccfe82730aba80016e7d9940f54a\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\cxx\\Debug\\5o2b6o6q\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b6a6ccfe82730aba80016e7d9940f54a\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\cxx\\Debug\\5o2b6o6q\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\cxx\\Debug\\5o2b6o6q\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\cxx\\Debug\\5o2b6o6q\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b6a6ccfe82730aba80016e7d9940f54a\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_RNHapticFeedbackSpec::@a83561f277f6afbb326c": {"artifactName": "react_codegen_RNHapticFeedbackSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "arm64-v8a", "runtimeFiles": []}}}