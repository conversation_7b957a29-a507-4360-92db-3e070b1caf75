import { Animated, Easing } from 'react-native';

export class AnimationUtils {
  // Temel animasyon presetleri
  static fadeIn(animatedValue: Animated.Value, duration: number = 300): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 1,
      duration,
      easing: Easing.out(Easing.quad),
      useNativeDriver: true,
    });
  }

  static fadeOut(animatedValue: Animated.Value, duration: number = 300): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: Easing.in(Easing.quad),
      useNativeDriver: true,
    });
  }

  static slideInFromBottom(animatedValue: Animated.Value, duration: number = 400): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: Easing.out(Easing.back(1.2)),
      useNativeDriver: true,
    });
  }

  static slideOutToBottom(animatedValue: Animated.Value, toValue: number, duration: number = 300): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue,
      duration,
      easing: Easing.in(Easing.back(1.2)),
      useNativeDriver: true,
    });
  }

  static scaleIn(animatedValue: Animated.Value, duration: number = 300): Animated.CompositeAnimation {
    return Animated.spring(animatedValue, {
      toValue: 1,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    });
  }

  static scaleOut(animatedValue: Animated.Value, duration: number = 200): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: Easing.in(Easing.quad),
      useNativeDriver: true,
    });
  }

  // Karmaşık animasyon kombinasyonları
  static bounceIn(animatedValue: Animated.Value): Animated.CompositeAnimation {
    return Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: 1.2,
        duration: 200,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: 0.9,
        duration: 100,
        easing: Easing.in(Easing.quad),
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 100,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
    ]);
  }

  static pulse(animatedValue: Animated.Value, minScale: number = 0.95, maxScale: number = 1.05): Animated.CompositeAnimation {
    return Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: maxScale,
          duration: 1000,
          easing: Easing.inOut(Easing.quad),
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: minScale,
          duration: 1000,
          easing: Easing.inOut(Easing.quad),
          useNativeDriver: true,
        }),
      ])
    );
  }

  static shake(animatedValue: Animated.Value, intensity: number = 10): Animated.CompositeAnimation {
    return Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: intensity,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: -intensity,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: intensity,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 50,
        useNativeDriver: true,
      }),
    ]);
  }

  static wiggle(animatedValue: Animated.Value): Animated.CompositeAnimation {
    return Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: 10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: -10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: 5,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }),
    ]);
  }

  // İlerleme çubuğu animasyonları
  static progressAnimation(animatedValue: Animated.Value, toValue: number, duration: number = 1000): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue,
      duration,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: false, // Width animasyonu için false
    });
  }

  // Sayı animasyonları
  static countUp(animatedValue: Animated.Value, toValue: number, duration: number = 1500): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue,
      duration,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: false, // Sayı interpolasyonu için false
    });
  }

  // Kart animasyonları
  static cardFlip(animatedValue: Animated.Value): Animated.CompositeAnimation {
    return Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: 90,
        duration: 300,
        easing: Easing.in(Easing.quad),
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 300,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
    ]);
  }

  // Stagger animasyonları (sıralı animasyonlar)
  static staggerAnimation(
    animatedValues: Animated.Value[],
    animationType: 'fadeIn' | 'slideIn' | 'scaleIn' = 'fadeIn',
    staggerDelay: number = 100
  ): Animated.CompositeAnimation {
    const animations = animatedValues.map((value, index) => {
      let animation: Animated.CompositeAnimation;
      
      switch (animationType) {
        case 'fadeIn':
          animation = this.fadeIn(value);
          break;
        case 'slideIn':
          animation = this.slideInFromBottom(value);
          break;
        case 'scaleIn':
          animation = this.scaleIn(value);
          break;
        default:
          animation = this.fadeIn(value);
      }

      return Animated.sequence([
        Animated.delay(index * staggerDelay),
        animation,
      ]);
    });

    return Animated.parallel(animations);
  }

  // Mikro etkileşim animasyonları
  static buttonPress(animatedValue: Animated.Value): Animated.CompositeAnimation {
    return Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: 0.95,
        duration: 100,
        easing: Easing.in(Easing.quad),
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 100,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
    ]);
  }

  static successFeedback(scaleValue: Animated.Value, opacityValue: Animated.Value): Animated.CompositeAnimation {
    return Animated.parallel([
      Animated.sequence([
        Animated.timing(scaleValue, {
          toValue: 1.2,
          duration: 200,
          easing: Easing.out(Easing.back(2)),
          useNativeDriver: true,
        }),
        Animated.timing(scaleValue, {
          toValue: 1,
          duration: 300,
          easing: Easing.out(Easing.quad),
          useNativeDriver: true,
        }),
      ]),
      Animated.sequence([
        Animated.timing(opacityValue, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.delay(1000),
        Animated.timing(opacityValue, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]),
    ]);
  }

  static errorFeedback(animatedValue: Animated.Value): Animated.CompositeAnimation {
    return this.shake(animatedValue, 15);
  }

  // Özel easing fonksiyonları
  static customEasing = {
    elastic: Easing.elastic(2),
    bounce: Easing.bounce,
    back: Easing.back(1.5),
    smooth: Easing.bezier(0.25, 0.46, 0.45, 0.94),
    swift: Easing.bezier(0.4, 0.0, 0.2, 1),
  };

  // Animasyon yardımcı fonksiyonları
  static createAnimatedValue(initialValue: number = 0): Animated.Value {
    return new Animated.Value(initialValue);
  }

  static createAnimatedValueXY(initialValue: { x: number; y: number } = { x: 0, y: 0 }): Animated.ValueXY {
    return new Animated.ValueXY(initialValue);
  }

  // Animasyon durumunu sıfırlama
  static resetAnimation(animatedValue: Animated.Value, toValue: number = 0): void {
    animatedValue.setValue(toValue);
  }

  static resetAnimations(animatedValues: Animated.Value[], toValue: number = 0): void {
    animatedValues.forEach(value => value.setValue(toValue));
  }
}

export default AnimationUtils;
