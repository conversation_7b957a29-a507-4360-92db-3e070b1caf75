{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-0c3e2a7b77c56039a433.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-dc4c3ea52b7fd9291656.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-d5c1b00078b8e6b814bf.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-dc4c3ea52b7fd9291656.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-d5c1b00078b8e6b814bf.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-0c3e2a7b77c56039a433.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}