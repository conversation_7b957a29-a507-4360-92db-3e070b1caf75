
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsH.js
 */
#pragma once

#include <react/renderer/components/view/ViewProps.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>
#include <react/renderer/graphics/Color.h>
#include <vector>

namespace facebook::react {

class RNSFullWindowOverlayProps final : public ViewProps {
 public:
  RNSFullWindowOverlayProps() = default;
  RNSFullWindowOverlayProps(const PropsParserContext& context, const RNSFullWindowOverlayProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  bool accessibilityContainerViewIsModal{true};
};

enum class RNSModalScreenStackPresentation { Push, Modal, TransparentModal, FullScreenModal, FormSheet, PageSheet, ContainedModal, ContainedTransparentModal };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSModalScreenStackPresentation &result) {
  auto string = (std::string)value;
  if (string == "push") { result = RNSModalScreenStackPresentation::Push; return; }
  if (string == "modal") { result = RNSModalScreenStackPresentation::Modal; return; }
  if (string == "transparentModal") { result = RNSModalScreenStackPresentation::TransparentModal; return; }
  if (string == "fullScreenModal") { result = RNSModalScreenStackPresentation::FullScreenModal; return; }
  if (string == "formSheet") { result = RNSModalScreenStackPresentation::FormSheet; return; }
  if (string == "pageSheet") { result = RNSModalScreenStackPresentation::PageSheet; return; }
  if (string == "containedModal") { result = RNSModalScreenStackPresentation::ContainedModal; return; }
  if (string == "containedTransparentModal") { result = RNSModalScreenStackPresentation::ContainedTransparentModal; return; }
  abort();
}

static inline std::string toString(const RNSModalScreenStackPresentation &value) {
  switch (value) {
    case RNSModalScreenStackPresentation::Push: return "push";
    case RNSModalScreenStackPresentation::Modal: return "modal";
    case RNSModalScreenStackPresentation::TransparentModal: return "transparentModal";
    case RNSModalScreenStackPresentation::FullScreenModal: return "fullScreenModal";
    case RNSModalScreenStackPresentation::FormSheet: return "formSheet";
    case RNSModalScreenStackPresentation::PageSheet: return "pageSheet";
    case RNSModalScreenStackPresentation::ContainedModal: return "containedModal";
    case RNSModalScreenStackPresentation::ContainedTransparentModal: return "containedTransparentModal";
  }
}
enum class RNSModalScreenStackAnimation { Default, Flip, Simple_push, None, Fade, Slide_from_right, Slide_from_left, Slide_from_bottom, Fade_from_bottom, Ios_from_right, Ios_from_left };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSModalScreenStackAnimation &result) {
  auto string = (std::string)value;
  if (string == "default") { result = RNSModalScreenStackAnimation::Default; return; }
  if (string == "flip") { result = RNSModalScreenStackAnimation::Flip; return; }
  if (string == "simple_push") { result = RNSModalScreenStackAnimation::Simple_push; return; }
  if (string == "none") { result = RNSModalScreenStackAnimation::None; return; }
  if (string == "fade") { result = RNSModalScreenStackAnimation::Fade; return; }
  if (string == "slide_from_right") { result = RNSModalScreenStackAnimation::Slide_from_right; return; }
  if (string == "slide_from_left") { result = RNSModalScreenStackAnimation::Slide_from_left; return; }
  if (string == "slide_from_bottom") { result = RNSModalScreenStackAnimation::Slide_from_bottom; return; }
  if (string == "fade_from_bottom") { result = RNSModalScreenStackAnimation::Fade_from_bottom; return; }
  if (string == "ios_from_right") { result = RNSModalScreenStackAnimation::Ios_from_right; return; }
  if (string == "ios_from_left") { result = RNSModalScreenStackAnimation::Ios_from_left; return; }
  abort();
}

static inline std::string toString(const RNSModalScreenStackAnimation &value) {
  switch (value) {
    case RNSModalScreenStackAnimation::Default: return "default";
    case RNSModalScreenStackAnimation::Flip: return "flip";
    case RNSModalScreenStackAnimation::Simple_push: return "simple_push";
    case RNSModalScreenStackAnimation::None: return "none";
    case RNSModalScreenStackAnimation::Fade: return "fade";
    case RNSModalScreenStackAnimation::Slide_from_right: return "slide_from_right";
    case RNSModalScreenStackAnimation::Slide_from_left: return "slide_from_left";
    case RNSModalScreenStackAnimation::Slide_from_bottom: return "slide_from_bottom";
    case RNSModalScreenStackAnimation::Fade_from_bottom: return "fade_from_bottom";
    case RNSModalScreenStackAnimation::Ios_from_right: return "ios_from_right";
    case RNSModalScreenStackAnimation::Ios_from_left: return "ios_from_left";
  }
}
enum class RNSModalScreenReplaceAnimation { Pop, Push };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSModalScreenReplaceAnimation &result) {
  auto string = (std::string)value;
  if (string == "pop") { result = RNSModalScreenReplaceAnimation::Pop; return; }
  if (string == "push") { result = RNSModalScreenReplaceAnimation::Push; return; }
  abort();
}

static inline std::string toString(const RNSModalScreenReplaceAnimation &value) {
  switch (value) {
    case RNSModalScreenReplaceAnimation::Pop: return "pop";
    case RNSModalScreenReplaceAnimation::Push: return "push";
  }
}
enum class RNSModalScreenSwipeDirection { Vertical, Horizontal };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSModalScreenSwipeDirection &result) {
  auto string = (std::string)value;
  if (string == "vertical") { result = RNSModalScreenSwipeDirection::Vertical; return; }
  if (string == "horizontal") { result = RNSModalScreenSwipeDirection::Horizontal; return; }
  abort();
}

static inline std::string toString(const RNSModalScreenSwipeDirection &value) {
  switch (value) {
    case RNSModalScreenSwipeDirection::Vertical: return "vertical";
    case RNSModalScreenSwipeDirection::Horizontal: return "horizontal";
  }
}
struct RNSModalScreenGestureResponseDistanceStruct {
  Float start{0.0};
  Float end{0.0};
  Float top{0.0};
  Float bottom{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSModalScreenGestureResponseDistanceStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_start = map.find("start");
  if (tmp_start != map.end()) {
    fromRawValue(context, tmp_start->second, result.start);
  }
  auto tmp_end = map.find("end");
  if (tmp_end != map.end()) {
    fromRawValue(context, tmp_end->second, result.end);
  }
  auto tmp_top = map.find("top");
  if (tmp_top != map.end()) {
    fromRawValue(context, tmp_top->second, result.top);
  }
  auto tmp_bottom = map.find("bottom");
  if (tmp_bottom != map.end()) {
    fromRawValue(context, tmp_bottom->second, result.bottom);
  }
}

static inline std::string toString(const RNSModalScreenGestureResponseDistanceStruct &value) {
  return "[Object RNSModalScreenGestureResponseDistanceStruct]";
}
class RNSModalScreenProps final : public ViewProps {
 public:
  RNSModalScreenProps() = default;
  RNSModalScreenProps(const PropsParserContext& context, const RNSModalScreenProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::vector<Float> sheetAllowedDetents{};
  int sheetLargestUndimmedDetent{-1};
  bool sheetGrabberVisible{false};
  Float sheetCornerRadius{-1.0};
  bool sheetExpandsWhenScrolledToEdge{false};
  int sheetInitialDetent{0};
  int sheetElevation{24};
  bool customAnimationOnSwipe{false};
  bool fullScreenSwipeEnabled{false};
  bool fullScreenSwipeShadowEnabled{true};
  bool homeIndicatorHidden{false};
  bool preventNativeDismiss{false};
  bool gestureEnabled{true};
  SharedColor statusBarColor{};
  bool statusBarHidden{false};
  std::string screenOrientation{};
  std::string statusBarAnimation{};
  std::string statusBarStyle{};
  bool statusBarTranslucent{false};
  RNSModalScreenGestureResponseDistanceStruct gestureResponseDistance{};
  RNSModalScreenStackPresentation stackPresentation{RNSModalScreenStackPresentation::Push};
  RNSModalScreenStackAnimation stackAnimation{RNSModalScreenStackAnimation::Default};
  int transitionDuration{500};
  RNSModalScreenReplaceAnimation replaceAnimation{RNSModalScreenReplaceAnimation::Pop};
  RNSModalScreenSwipeDirection swipeDirection{RNSModalScreenSwipeDirection::Horizontal};
  bool hideKeyboardOnSwipe{false};
  Float activityState{-1.0};
  SharedColor navigationBarColor{};
  bool navigationBarTranslucent{false};
  bool navigationBarHidden{false};
  bool nativeBackButtonDismissalEnabled{false};
};

class RNSScreenContainerProps final : public ViewProps {
 public:
  RNSScreenContainerProps() = default;
  RNSScreenContainerProps(const PropsParserContext& context, const RNSScreenContainerProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  
};

class RNSScreenContentWrapperProps final : public ViewProps {
 public:
  RNSScreenContentWrapperProps() = default;
  RNSScreenContentWrapperProps(const PropsParserContext& context, const RNSScreenContentWrapperProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  
};

class RNSScreenFooterProps final : public ViewProps {
 public:
  RNSScreenFooterProps() = default;
  RNSScreenFooterProps(const PropsParserContext& context, const RNSScreenFooterProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  
};

enum class RNSScreenStackPresentation { Push, Modal, TransparentModal, FullScreenModal, FormSheet, PageSheet, ContainedModal, ContainedTransparentModal };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenStackPresentation &result) {
  auto string = (std::string)value;
  if (string == "push") { result = RNSScreenStackPresentation::Push; return; }
  if (string == "modal") { result = RNSScreenStackPresentation::Modal; return; }
  if (string == "transparentModal") { result = RNSScreenStackPresentation::TransparentModal; return; }
  if (string == "fullScreenModal") { result = RNSScreenStackPresentation::FullScreenModal; return; }
  if (string == "formSheet") { result = RNSScreenStackPresentation::FormSheet; return; }
  if (string == "pageSheet") { result = RNSScreenStackPresentation::PageSheet; return; }
  if (string == "containedModal") { result = RNSScreenStackPresentation::ContainedModal; return; }
  if (string == "containedTransparentModal") { result = RNSScreenStackPresentation::ContainedTransparentModal; return; }
  abort();
}

static inline std::string toString(const RNSScreenStackPresentation &value) {
  switch (value) {
    case RNSScreenStackPresentation::Push: return "push";
    case RNSScreenStackPresentation::Modal: return "modal";
    case RNSScreenStackPresentation::TransparentModal: return "transparentModal";
    case RNSScreenStackPresentation::FullScreenModal: return "fullScreenModal";
    case RNSScreenStackPresentation::FormSheet: return "formSheet";
    case RNSScreenStackPresentation::PageSheet: return "pageSheet";
    case RNSScreenStackPresentation::ContainedModal: return "containedModal";
    case RNSScreenStackPresentation::ContainedTransparentModal: return "containedTransparentModal";
  }
}
enum class RNSScreenStackAnimation { Default, Flip, Simple_push, None, Fade, Slide_from_right, Slide_from_left, Slide_from_bottom, Fade_from_bottom, Ios_from_right, Ios_from_left };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenStackAnimation &result) {
  auto string = (std::string)value;
  if (string == "default") { result = RNSScreenStackAnimation::Default; return; }
  if (string == "flip") { result = RNSScreenStackAnimation::Flip; return; }
  if (string == "simple_push") { result = RNSScreenStackAnimation::Simple_push; return; }
  if (string == "none") { result = RNSScreenStackAnimation::None; return; }
  if (string == "fade") { result = RNSScreenStackAnimation::Fade; return; }
  if (string == "slide_from_right") { result = RNSScreenStackAnimation::Slide_from_right; return; }
  if (string == "slide_from_left") { result = RNSScreenStackAnimation::Slide_from_left; return; }
  if (string == "slide_from_bottom") { result = RNSScreenStackAnimation::Slide_from_bottom; return; }
  if (string == "fade_from_bottom") { result = RNSScreenStackAnimation::Fade_from_bottom; return; }
  if (string == "ios_from_right") { result = RNSScreenStackAnimation::Ios_from_right; return; }
  if (string == "ios_from_left") { result = RNSScreenStackAnimation::Ios_from_left; return; }
  abort();
}

static inline std::string toString(const RNSScreenStackAnimation &value) {
  switch (value) {
    case RNSScreenStackAnimation::Default: return "default";
    case RNSScreenStackAnimation::Flip: return "flip";
    case RNSScreenStackAnimation::Simple_push: return "simple_push";
    case RNSScreenStackAnimation::None: return "none";
    case RNSScreenStackAnimation::Fade: return "fade";
    case RNSScreenStackAnimation::Slide_from_right: return "slide_from_right";
    case RNSScreenStackAnimation::Slide_from_left: return "slide_from_left";
    case RNSScreenStackAnimation::Slide_from_bottom: return "slide_from_bottom";
    case RNSScreenStackAnimation::Fade_from_bottom: return "fade_from_bottom";
    case RNSScreenStackAnimation::Ios_from_right: return "ios_from_right";
    case RNSScreenStackAnimation::Ios_from_left: return "ios_from_left";
  }
}
enum class RNSScreenReplaceAnimation { Pop, Push };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenReplaceAnimation &result) {
  auto string = (std::string)value;
  if (string == "pop") { result = RNSScreenReplaceAnimation::Pop; return; }
  if (string == "push") { result = RNSScreenReplaceAnimation::Push; return; }
  abort();
}

static inline std::string toString(const RNSScreenReplaceAnimation &value) {
  switch (value) {
    case RNSScreenReplaceAnimation::Pop: return "pop";
    case RNSScreenReplaceAnimation::Push: return "push";
  }
}
enum class RNSScreenSwipeDirection { Vertical, Horizontal };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenSwipeDirection &result) {
  auto string = (std::string)value;
  if (string == "vertical") { result = RNSScreenSwipeDirection::Vertical; return; }
  if (string == "horizontal") { result = RNSScreenSwipeDirection::Horizontal; return; }
  abort();
}

static inline std::string toString(const RNSScreenSwipeDirection &value) {
  switch (value) {
    case RNSScreenSwipeDirection::Vertical: return "vertical";
    case RNSScreenSwipeDirection::Horizontal: return "horizontal";
  }
}
struct RNSScreenGestureResponseDistanceStruct {
  Float start{0.0};
  Float end{0.0};
  Float top{0.0};
  Float bottom{0.0};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenGestureResponseDistanceStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_start = map.find("start");
  if (tmp_start != map.end()) {
    fromRawValue(context, tmp_start->second, result.start);
  }
  auto tmp_end = map.find("end");
  if (tmp_end != map.end()) {
    fromRawValue(context, tmp_end->second, result.end);
  }
  auto tmp_top = map.find("top");
  if (tmp_top != map.end()) {
    fromRawValue(context, tmp_top->second, result.top);
  }
  auto tmp_bottom = map.find("bottom");
  if (tmp_bottom != map.end()) {
    fromRawValue(context, tmp_bottom->second, result.bottom);
  }
}

static inline std::string toString(const RNSScreenGestureResponseDistanceStruct &value) {
  return "[Object RNSScreenGestureResponseDistanceStruct]";
}
class RNSScreenProps final : public ViewProps {
 public:
  RNSScreenProps() = default;
  RNSScreenProps(const PropsParserContext& context, const RNSScreenProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::vector<Float> sheetAllowedDetents{};
  int sheetLargestUndimmedDetent{-1};
  bool sheetGrabberVisible{false};
  Float sheetCornerRadius{-1.0};
  bool sheetExpandsWhenScrolledToEdge{false};
  int sheetInitialDetent{0};
  int sheetElevation{24};
  bool customAnimationOnSwipe{false};
  bool fullScreenSwipeEnabled{false};
  bool fullScreenSwipeShadowEnabled{true};
  bool homeIndicatorHidden{false};
  bool preventNativeDismiss{false};
  bool gestureEnabled{true};
  SharedColor statusBarColor{};
  bool statusBarHidden{false};
  std::string screenOrientation{};
  std::string statusBarAnimation{};
  std::string statusBarStyle{};
  bool statusBarTranslucent{false};
  RNSScreenGestureResponseDistanceStruct gestureResponseDistance{};
  RNSScreenStackPresentation stackPresentation{RNSScreenStackPresentation::Push};
  RNSScreenStackAnimation stackAnimation{RNSScreenStackAnimation::Default};
  int transitionDuration{500};
  RNSScreenReplaceAnimation replaceAnimation{RNSScreenReplaceAnimation::Pop};
  RNSScreenSwipeDirection swipeDirection{RNSScreenSwipeDirection::Horizontal};
  bool hideKeyboardOnSwipe{false};
  Float activityState{-1.0};
  SharedColor navigationBarColor{};
  bool navigationBarTranslucent{false};
  bool navigationBarHidden{false};
  bool nativeBackButtonDismissalEnabled{false};
};

class RNSScreenNavigationContainerProps final : public ViewProps {
 public:
  RNSScreenNavigationContainerProps() = default;
  RNSScreenNavigationContainerProps(const PropsParserContext& context, const RNSScreenNavigationContainerProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  
};

enum class RNSScreenStackHeaderConfigDirection { Rtl, Ltr };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenStackHeaderConfigDirection &result) {
  auto string = (std::string)value;
  if (string == "rtl") { result = RNSScreenStackHeaderConfigDirection::Rtl; return; }
  if (string == "ltr") { result = RNSScreenStackHeaderConfigDirection::Ltr; return; }
  abort();
}

static inline std::string toString(const RNSScreenStackHeaderConfigDirection &value) {
  switch (value) {
    case RNSScreenStackHeaderConfigDirection::Rtl: return "rtl";
    case RNSScreenStackHeaderConfigDirection::Ltr: return "ltr";
  }
}
enum class RNSScreenStackHeaderConfigBackButtonDisplayMode { Minimal, Default, Generic };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenStackHeaderConfigBackButtonDisplayMode &result) {
  auto string = (std::string)value;
  if (string == "minimal") { result = RNSScreenStackHeaderConfigBackButtonDisplayMode::Minimal; return; }
  if (string == "default") { result = RNSScreenStackHeaderConfigBackButtonDisplayMode::Default; return; }
  if (string == "generic") { result = RNSScreenStackHeaderConfigBackButtonDisplayMode::Generic; return; }
  abort();
}

static inline std::string toString(const RNSScreenStackHeaderConfigBackButtonDisplayMode &value) {
  switch (value) {
    case RNSScreenStackHeaderConfigBackButtonDisplayMode::Minimal: return "minimal";
    case RNSScreenStackHeaderConfigBackButtonDisplayMode::Default: return "default";
    case RNSScreenStackHeaderConfigBackButtonDisplayMode::Generic: return "generic";
  }
}
enum class RNSScreenStackHeaderConfigBlurEffect { None, ExtraLight, Light, Dark, Regular, Prominent, SystemUltraThinMaterial, SystemThinMaterial, SystemMaterial, SystemThickMaterial, SystemChromeMaterial, SystemUltraThinMaterialLight, SystemThinMaterialLight, SystemMaterialLight, SystemThickMaterialLight, SystemChromeMaterialLight, SystemUltraThinMaterialDark, SystemThinMaterialDark, SystemMaterialDark, SystemThickMaterialDark, SystemChromeMaterialDark };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenStackHeaderConfigBlurEffect &result) {
  auto string = (std::string)value;
  if (string == "none") { result = RNSScreenStackHeaderConfigBlurEffect::None; return; }
  if (string == "extraLight") { result = RNSScreenStackHeaderConfigBlurEffect::ExtraLight; return; }
  if (string == "light") { result = RNSScreenStackHeaderConfigBlurEffect::Light; return; }
  if (string == "dark") { result = RNSScreenStackHeaderConfigBlurEffect::Dark; return; }
  if (string == "regular") { result = RNSScreenStackHeaderConfigBlurEffect::Regular; return; }
  if (string == "prominent") { result = RNSScreenStackHeaderConfigBlurEffect::Prominent; return; }
  if (string == "systemUltraThinMaterial") { result = RNSScreenStackHeaderConfigBlurEffect::SystemUltraThinMaterial; return; }
  if (string == "systemThinMaterial") { result = RNSScreenStackHeaderConfigBlurEffect::SystemThinMaterial; return; }
  if (string == "systemMaterial") { result = RNSScreenStackHeaderConfigBlurEffect::SystemMaterial; return; }
  if (string == "systemThickMaterial") { result = RNSScreenStackHeaderConfigBlurEffect::SystemThickMaterial; return; }
  if (string == "systemChromeMaterial") { result = RNSScreenStackHeaderConfigBlurEffect::SystemChromeMaterial; return; }
  if (string == "systemUltraThinMaterialLight") { result = RNSScreenStackHeaderConfigBlurEffect::SystemUltraThinMaterialLight; return; }
  if (string == "systemThinMaterialLight") { result = RNSScreenStackHeaderConfigBlurEffect::SystemThinMaterialLight; return; }
  if (string == "systemMaterialLight") { result = RNSScreenStackHeaderConfigBlurEffect::SystemMaterialLight; return; }
  if (string == "systemThickMaterialLight") { result = RNSScreenStackHeaderConfigBlurEffect::SystemThickMaterialLight; return; }
  if (string == "systemChromeMaterialLight") { result = RNSScreenStackHeaderConfigBlurEffect::SystemChromeMaterialLight; return; }
  if (string == "systemUltraThinMaterialDark") { result = RNSScreenStackHeaderConfigBlurEffect::SystemUltraThinMaterialDark; return; }
  if (string == "systemThinMaterialDark") { result = RNSScreenStackHeaderConfigBlurEffect::SystemThinMaterialDark; return; }
  if (string == "systemMaterialDark") { result = RNSScreenStackHeaderConfigBlurEffect::SystemMaterialDark; return; }
  if (string == "systemThickMaterialDark") { result = RNSScreenStackHeaderConfigBlurEffect::SystemThickMaterialDark; return; }
  if (string == "systemChromeMaterialDark") { result = RNSScreenStackHeaderConfigBlurEffect::SystemChromeMaterialDark; return; }
  abort();
}

static inline std::string toString(const RNSScreenStackHeaderConfigBlurEffect &value) {
  switch (value) {
    case RNSScreenStackHeaderConfigBlurEffect::None: return "none";
    case RNSScreenStackHeaderConfigBlurEffect::ExtraLight: return "extraLight";
    case RNSScreenStackHeaderConfigBlurEffect::Light: return "light";
    case RNSScreenStackHeaderConfigBlurEffect::Dark: return "dark";
    case RNSScreenStackHeaderConfigBlurEffect::Regular: return "regular";
    case RNSScreenStackHeaderConfigBlurEffect::Prominent: return "prominent";
    case RNSScreenStackHeaderConfigBlurEffect::SystemUltraThinMaterial: return "systemUltraThinMaterial";
    case RNSScreenStackHeaderConfigBlurEffect::SystemThinMaterial: return "systemThinMaterial";
    case RNSScreenStackHeaderConfigBlurEffect::SystemMaterial: return "systemMaterial";
    case RNSScreenStackHeaderConfigBlurEffect::SystemThickMaterial: return "systemThickMaterial";
    case RNSScreenStackHeaderConfigBlurEffect::SystemChromeMaterial: return "systemChromeMaterial";
    case RNSScreenStackHeaderConfigBlurEffect::SystemUltraThinMaterialLight: return "systemUltraThinMaterialLight";
    case RNSScreenStackHeaderConfigBlurEffect::SystemThinMaterialLight: return "systemThinMaterialLight";
    case RNSScreenStackHeaderConfigBlurEffect::SystemMaterialLight: return "systemMaterialLight";
    case RNSScreenStackHeaderConfigBlurEffect::SystemThickMaterialLight: return "systemThickMaterialLight";
    case RNSScreenStackHeaderConfigBlurEffect::SystemChromeMaterialLight: return "systemChromeMaterialLight";
    case RNSScreenStackHeaderConfigBlurEffect::SystemUltraThinMaterialDark: return "systemUltraThinMaterialDark";
    case RNSScreenStackHeaderConfigBlurEffect::SystemThinMaterialDark: return "systemThinMaterialDark";
    case RNSScreenStackHeaderConfigBlurEffect::SystemMaterialDark: return "systemMaterialDark";
    case RNSScreenStackHeaderConfigBlurEffect::SystemThickMaterialDark: return "systemThickMaterialDark";
    case RNSScreenStackHeaderConfigBlurEffect::SystemChromeMaterialDark: return "systemChromeMaterialDark";
  }
}

class RNSScreenStackHeaderConfigProps final : public ViewProps {
 public:
  RNSScreenStackHeaderConfigProps() = default;
  RNSScreenStackHeaderConfigProps(const PropsParserContext& context, const RNSScreenStackHeaderConfigProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  SharedColor backgroundColor{};
  std::string backTitle{};
  std::string backTitleFontFamily{};
  int backTitleFontSize{0};
  bool backTitleVisible{true};
  SharedColor color{};
  RNSScreenStackHeaderConfigDirection direction{RNSScreenStackHeaderConfigDirection::Ltr};
  bool hidden{false};
  bool hideShadow{false};
  bool largeTitle{false};
  std::string largeTitleFontFamily{};
  int largeTitleFontSize{0};
  std::string largeTitleFontWeight{};
  SharedColor largeTitleBackgroundColor{};
  bool largeTitleHideShadow{false};
  SharedColor largeTitleColor{};
  bool translucent{false};
  std::string title{};
  std::string titleFontFamily{};
  int titleFontSize{0};
  std::string titleFontWeight{};
  SharedColor titleColor{};
  bool disableBackButtonMenu{false};
  RNSScreenStackHeaderConfigBackButtonDisplayMode backButtonDisplayMode{RNSScreenStackHeaderConfigBackButtonDisplayMode::Default};
  bool hideBackButton{false};
  bool backButtonInCustomView{false};
  RNSScreenStackHeaderConfigBlurEffect blurEffect{RNSScreenStackHeaderConfigBlurEffect::None};
  bool topInsetEnabled{false};
};

enum class RNSScreenStackHeaderSubviewType { Back, Right, Left, Title, Center, SearchBar };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSScreenStackHeaderSubviewType &result) {
  auto string = (std::string)value;
  if (string == "back") { result = RNSScreenStackHeaderSubviewType::Back; return; }
  if (string == "right") { result = RNSScreenStackHeaderSubviewType::Right; return; }
  if (string == "left") { result = RNSScreenStackHeaderSubviewType::Left; return; }
  if (string == "title") { result = RNSScreenStackHeaderSubviewType::Title; return; }
  if (string == "center") { result = RNSScreenStackHeaderSubviewType::Center; return; }
  if (string == "searchBar") { result = RNSScreenStackHeaderSubviewType::SearchBar; return; }
  abort();
}

static inline std::string toString(const RNSScreenStackHeaderSubviewType &value) {
  switch (value) {
    case RNSScreenStackHeaderSubviewType::Back: return "back";
    case RNSScreenStackHeaderSubviewType::Right: return "right";
    case RNSScreenStackHeaderSubviewType::Left: return "left";
    case RNSScreenStackHeaderSubviewType::Title: return "title";
    case RNSScreenStackHeaderSubviewType::Center: return "center";
    case RNSScreenStackHeaderSubviewType::SearchBar: return "searchBar";
  }
}

class RNSScreenStackHeaderSubviewProps final : public ViewProps {
 public:
  RNSScreenStackHeaderSubviewProps() = default;
  RNSScreenStackHeaderSubviewProps(const PropsParserContext& context, const RNSScreenStackHeaderSubviewProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  RNSScreenStackHeaderSubviewType type{RNSScreenStackHeaderSubviewType::Left};
};

class RNSScreenStackProps final : public ViewProps {
 public:
  RNSScreenStackProps() = default;
  RNSScreenStackProps(const PropsParserContext& context, const RNSScreenStackProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  
};

enum class RNSSearchBarAutoCapitalize { None, Words, Sentences, Characters };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSSearchBarAutoCapitalize &result) {
  auto string = (std::string)value;
  if (string == "none") { result = RNSSearchBarAutoCapitalize::None; return; }
  if (string == "words") { result = RNSSearchBarAutoCapitalize::Words; return; }
  if (string == "sentences") { result = RNSSearchBarAutoCapitalize::Sentences; return; }
  if (string == "characters") { result = RNSSearchBarAutoCapitalize::Characters; return; }
  abort();
}

static inline std::string toString(const RNSSearchBarAutoCapitalize &value) {
  switch (value) {
    case RNSSearchBarAutoCapitalize::None: return "none";
    case RNSSearchBarAutoCapitalize::Words: return "words";
    case RNSSearchBarAutoCapitalize::Sentences: return "sentences";
    case RNSSearchBarAutoCapitalize::Characters: return "characters";
  }
}
enum class RNSSearchBarPlacement { Automatic, Inline, Stacked };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNSSearchBarPlacement &result) {
  auto string = (std::string)value;
  if (string == "automatic") { result = RNSSearchBarPlacement::Automatic; return; }
  if (string == "inline") { result = RNSSearchBarPlacement::Inline; return; }
  if (string == "stacked") { result = RNSSearchBarPlacement::Stacked; return; }
  abort();
}

static inline std::string toString(const RNSSearchBarPlacement &value) {
  switch (value) {
    case RNSSearchBarPlacement::Automatic: return "automatic";
    case RNSSearchBarPlacement::Inline: return "inline";
    case RNSSearchBarPlacement::Stacked: return "stacked";
  }
}

class RNSSearchBarProps final : public ViewProps {
 public:
  RNSSearchBarProps() = default;
  RNSSearchBarProps(const PropsParserContext& context, const RNSSearchBarProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  bool hideWhenScrolling{false};
  RNSSearchBarAutoCapitalize autoCapitalize{RNSSearchBarAutoCapitalize::None};
  std::string placeholder{};
  RNSSearchBarPlacement placement{RNSSearchBarPlacement::Stacked};
  bool obscureBackground{false};
  bool hideNavigationBar{false};
  std::string cancelButtonText{};
  SharedColor barTintColor{};
  SharedColor tintColor{};
  SharedColor textColor{};
  bool disableBackButtonOverride{false};
  std::string inputType{};
  SharedColor hintTextColor{};
  SharedColor headerIconColor{};
  bool shouldShowHintSearchIcon{true};
};

} // namespace facebook::react
