
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleJniCpp.js
 */

#include "rnscreens.h"

namespace facebook::react {



NativeScreensModuleSpecJSI::NativeScreensModuleSpecJSI(const JavaTurboModule::InitParams &params)
  : JavaTurboModule(params) {

}

std::shared_ptr<TurboModule> rnscreens_ModuleProvider(const std::string &moduleName, const JavaTurboModule::InitParams &params) {
  if (moduleName == "RNSModule") {
    return std::make_shared<NativeScreensModuleSpecJSI>(params);
  }
  return nullptr;
}

} // namespace facebook::react
