1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.tytfizikkamp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:3:5-67
11-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.VIBRATE" />
12-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:4:5-66
12-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:4:22-63
13    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
13-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:5:5-80
13-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:5:22-78
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:6:5-68
14-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:6:22-65
15    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
15-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:7:5-77
15-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:7:22-74
16    <!--
17    This manifest file is used only by Gradle to configure debug-only capabilities
18    for React Native Apps.
19    -->
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->[com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:16:5-78
20-->[com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:16:22-75
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
21-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:22:5-79
21-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:22:22-76
22    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
22-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:25:5-82
22-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:25:22-79
23
24    <permission
24-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
25        android:name="com.tytfizikkamp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
25-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
26        android:protectionLevel="signature" />
26-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
27
28    <uses-permission android:name="com.tytfizikkamp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- for android -->
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
29    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
30    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
31    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
32    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
33    <!-- for Samsung -->
34    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
34-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
34-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
35    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
35-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
35-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
36    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
36-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
36-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
37    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
37-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
37-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
38    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
38-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
38-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
39    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
39-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
39-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
40    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
40-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
40-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
41    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
41-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
41-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
42    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
42-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
42-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
43    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
43-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
43-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
44    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
44-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
44-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
45    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
45-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
45-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
46    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
46-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
46-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
47    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
47-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
47-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
48    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
48-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
48-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
49    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
49-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
49-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b32365d19bffceda26486f540927618c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
50
51    <application
51-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:9:5-52:19
52        android:name="com.tytfizikkamp.MainApplication"
52-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:10:7-38
53        android:allowBackup="false"
53-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:14:7-34
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ec4e31fa8d0cf74d3d6955f9c39b3c31\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
55        android:debuggable="true"
56        android:extractNativeLibs="false"
57        android:icon="@mipmap/ic_launcher"
57-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:12:7-41
58        android:label="@string/app_name"
58-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:11:7-39
59        android:roundIcon="@mipmap/ic_launcher_round"
59-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:13:7-52
60        android:supportsRtl="true"
60-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:16:7-33
61        android:theme="@style/AppTheme"
61-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:15:7-38
62        android:usesCleartextTraffic="true" >
62-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\debug\AndroidManifest.xml:6:9-44
63        <activity
63-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:17:7-28:18
64            android:name="com.tytfizikkamp.MainActivity"
64-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:18:9-37
65            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
65-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:20:9-118
66            android:exported="true"
66-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:23:9-32
67            android:label="@string/app_name"
67-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:19:9-41
68            android:launchMode="singleTask"
68-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:21:9-40
69            android:windowSoftInputMode="adjustResize" >
69-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:22:9-51
70            <intent-filter>
70-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:24:9-27:25
71                <action android:name="android.intent.action.MAIN" />
71-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:25:13-65
71-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:25:21-62
72
73                <category android:name="android.intent.category.LAUNCHER" />
73-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:26:13-73
73-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:26:23-70
74            </intent-filter>
75        </activity> <!-- Push Notification Receivers -->
76        <receiver
76-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:31:7-32:44
77            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationActions"
77-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:31:17-103
78            android:exported="false" />
78-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:32:17-41
79        <receiver
79-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:33:7-34:44
80            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationPublisher"
80-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:33:17-105
81            android:exported="false" />
81-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:34:17-41
82        <receiver
82-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:35:7-42:18
83            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationBootEventReceiver"
83-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:35:17-113
84            android:exported="true" >
84-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:36:17-40
85            <intent-filter>
85-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:37:11-41:27
86                <action android:name="android.intent.action.BOOT_COMPLETED" />
86-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:38:15-77
86-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:38:23-74
87                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
87-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:39:15-80
87-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:39:23-77
88                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
88-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:40:15-79
88-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:40:23-77
89            </intent-filter>
90        </receiver>
91
92        <service
92-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:44:7-50:17
93            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationListenerService"
93-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:45:11-105
94            android:exported="false" >
94-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:46:11-35
95            <intent-filter>
95-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:47:11-49:27
96                <action android:name="com.google.firebase.MESSAGING_EVENT" />
96-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:48:15-76
96-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:48:23-73
97            </intent-filter>
98        </service>
99
100        <activity
100-->[com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:19:9-21:40
101            android:name="com.facebook.react.devsupport.DevSettingsActivity"
101-->[com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:20:13-77
102            android:exported="false" />
102-->[com.facebook.react:react-android:0.80.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d221509c521a46e74f6572064742339\transformed\react-android-0.80.1-debug\AndroidManifest.xml:21:13-37
103
104        <receiver
104-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:28:9-35:20
105            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
105-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:29:13-78
106            android:exported="true"
106-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:30:13-36
107            android:permission="com.google.android.c2dm.permission.SEND" >
107-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:31:13-73
108            <intent-filter>
108-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:32:13-34:29
109                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
109-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:33:17-81
109-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:33:25-78
110            </intent-filter>
111        </receiver>
112        <!--
113             FirebaseMessagingService performs security checks at runtime,
114             but set to not exported to explicitly avoid allowing another app to call it.
115        -->
116        <service
116-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:41:9-48:19
117            android:name="com.google.firebase.messaging.FirebaseMessagingService"
117-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:42:13-82
118            android:directBootAware="true"
118-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:43:13-43
119            android:exported="false" >
119-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:44:13-37
120            <intent-filter android:priority="-500" >
120-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:47:11-49:27
121                <action android:name="com.google.firebase.MESSAGING_EVENT" />
121-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:48:15-76
121-->C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\AndroidManifest.xml:48:23-73
122            </intent-filter>
123        </service>
124        <service
124-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:49:9-55:19
125            android:name="com.google.firebase.components.ComponentDiscoveryService"
125-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:50:13-84
126            android:directBootAware="true"
126-->[com.google.firebase:firebase-common:19.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a85414b02711ccc65a7cbabeaa85bcb8\transformed\firebase-common-19.5.0\AndroidManifest.xml:35:13-43
127            android:exported="false" >
127-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:51:13-37
128            <meta-data
128-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:52:13-54:85
129                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
129-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:53:17-119
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-messaging:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e932dc8d24e50c2cb3b8fbb16fc5745d\transformed\firebase-messaging-21.1.0\AndroidManifest.xml:54:17-82
131            <meta-data
131-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8c4ab793c24d7170edcc6fd74a40b7ce\transformed\firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
132                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
132-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8c4ab793c24d7170edcc6fd74a40b7ce\transformed\firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8c4ab793c24d7170edcc6fd74a40b7ce\transformed\firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
134            <meta-data
134-->[com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\005d760d502c14dfd1cb236064d486a0\transformed\firebase-datatransport-17.0.10\AndroidManifest.xml:28:13-30:85
135                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
135-->[com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\005d760d502c14dfd1cb236064d486a0\transformed\firebase-datatransport-17.0.10\AndroidManifest.xml:29:17-115
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[com.google.firebase:firebase-datatransport:17.0.10] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\005d760d502c14dfd1cb236064d486a0\transformed\firebase-datatransport-17.0.10\AndroidManifest.xml:30:17-82
137            <meta-data
137-->[com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5fa98bbea297d887922711694205bcb9\transformed\firebase-installations-16.3.5\AndroidManifest.xml:18:13-20:85
138                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
138-->[com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5fa98bbea297d887922711694205bcb9\transformed\firebase-installations-16.3.5\AndroidManifest.xml:19:17-127
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-installations:16.3.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5fa98bbea297d887922711694205bcb9\transformed\firebase-installations-16.3.5\AndroidManifest.xml:20:17-82
140            <!--
141                This registrar is not defined in the dynamic-module-support sdk itself to allow non-firebase
142                clients to use it as well, by defining this registrar in their own core/common library.
143            -->
144            <meta-data
144-->[com.google.firebase:firebase-common:19.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a85414b02711ccc65a7cbabeaa85bcb8\transformed\firebase-common-19.5.0\AndroidManifest.xml:43:13-45:85
145                android:name="com.google.firebase.components:com.google.firebase.dynamicloading.DynamicLoadingRegistrar"
145-->[com.google.firebase:firebase-common:19.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a85414b02711ccc65a7cbabeaa85bcb8\transformed\firebase-common-19.5.0\AndroidManifest.xml:44:17-121
146                android:value="com.google.firebase.components.ComponentRegistrar" />
146-->[com.google.firebase:firebase-common:19.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a85414b02711ccc65a7cbabeaa85bcb8\transformed\firebase-common-19.5.0\AndroidManifest.xml:45:17-82
147        </service>
148
149        <provider
149-->[com.google.firebase:firebase-common:19.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a85414b02711ccc65a7cbabeaa85bcb8\transformed\firebase-common-19.5.0\AndroidManifest.xml:26:9-31:39
150            android:name="com.google.firebase.provider.FirebaseInitProvider"
150-->[com.google.firebase:firebase-common:19.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a85414b02711ccc65a7cbabeaa85bcb8\transformed\firebase-common-19.5.0\AndroidManifest.xml:27:13-77
151            android:authorities="com.tytfizikkamp.firebaseinitprovider"
151-->[com.google.firebase:firebase-common:19.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a85414b02711ccc65a7cbabeaa85bcb8\transformed\firebase-common-19.5.0\AndroidManifest.xml:28:13-72
152            android:directBootAware="true"
152-->[com.google.firebase:firebase-common:19.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a85414b02711ccc65a7cbabeaa85bcb8\transformed\firebase-common-19.5.0\AndroidManifest.xml:29:13-43
153            android:exported="false"
153-->[com.google.firebase:firebase-common:19.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a85414b02711ccc65a7cbabeaa85bcb8\transformed\firebase-common-19.5.0\AndroidManifest.xml:30:13-37
154            android:initOrder="100" />
154-->[com.google.firebase:firebase-common:19.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a85414b02711ccc65a7cbabeaa85bcb8\transformed\firebase-common-19.5.0\AndroidManifest.xml:31:13-36
155
156        <meta-data
156-->[com.google.android.gms:play-services-basement:17.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d6862beb0c03f816962150b27b444ce5\transformed\play-services-basement-17.0.0\AndroidManifest.xml:23:9-25:69
157            android:name="com.google.android.gms.version"
157-->[com.google.android.gms:play-services-basement:17.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d6862beb0c03f816962150b27b444ce5\transformed\play-services-basement-17.0.0\AndroidManifest.xml:24:13-58
158            android:value="@integer/google_play_services_version" />
158-->[com.google.android.gms:play-services-basement:17.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d6862beb0c03f816962150b27b444ce5\transformed\play-services-basement-17.0.0\AndroidManifest.xml:25:13-66
159
160        <provider
160-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
161            android:name="androidx.startup.InitializationProvider"
161-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
162            android:authorities="com.tytfizikkamp.androidx-startup"
162-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
163            android:exported="false" >
163-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
164            <meta-data
164-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
165                android:name="androidx.emoji2.text.EmojiCompatInitializer"
165-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
166                android:value="androidx.startup" />
166-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\184371a37a31633bc799675a52e2ed56\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
167            <meta-data
167-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
168                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
168-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
169                android:value="androidx.startup" />
169-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\22777817996210683c00c27568dea84f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
170            <meta-data
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
171                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
172                android:value="androidx.startup" />
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
173        </provider>
174
175        <receiver
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
176            android:name="androidx.profileinstaller.ProfileInstallReceiver"
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
177            android:directBootAware="false"
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
178            android:enabled="true"
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
179            android:exported="true"
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
180            android:permission="android.permission.DUMP" >
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
181            <intent-filter>
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
182                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
183            </intent-filter>
184            <intent-filter>
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
185                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
186            </intent-filter>
187            <intent-filter>
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
188                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
189            </intent-filter>
190            <intent-filter>
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
191                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ab6840840bad5eecbd4248abd2d5c8a3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
192            </intent-filter>
193        </receiver>
194
195        <service
195-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c076031a777d4abc005a9705e2944fbe\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
196            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
196-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c076031a777d4abc005a9705e2944fbe\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
197            android:exported="false" >
197-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c076031a777d4abc005a9705e2944fbe\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
198            <meta-data
198-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c076031a777d4abc005a9705e2944fbe\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
199                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
199-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c076031a777d4abc005a9705e2944fbe\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
200                android:value="cct" />
200-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c076031a777d4abc005a9705e2944fbe\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
201        </service>
202        <service
202-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b24d3d6bd7cd2ede64fa8bfcbc4c3e2f\transformed\transport-runtime-2.2.5\AndroidManifest.xml:26:9-30:19
203            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
203-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b24d3d6bd7cd2ede64fa8bfcbc4c3e2f\transformed\transport-runtime-2.2.5\AndroidManifest.xml:27:13-117
204            android:exported="false"
204-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b24d3d6bd7cd2ede64fa8bfcbc4c3e2f\transformed\transport-runtime-2.2.5\AndroidManifest.xml:28:13-37
205            android:permission="android.permission.BIND_JOB_SERVICE" >
205-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b24d3d6bd7cd2ede64fa8bfcbc4c3e2f\transformed\transport-runtime-2.2.5\AndroidManifest.xml:29:13-69
206        </service>
207
208        <receiver
208-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b24d3d6bd7cd2ede64fa8bfcbc4c3e2f\transformed\transport-runtime-2.2.5\AndroidManifest.xml:32:9-34:40
209            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
209-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b24d3d6bd7cd2ede64fa8bfcbc4c3e2f\transformed\transport-runtime-2.2.5\AndroidManifest.xml:33:13-132
210            android:exported="false" />
210-->[com.google.android.datatransport:transport-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b24d3d6bd7cd2ede64fa8bfcbc4c3e2f\transformed\transport-runtime-2.2.5\AndroidManifest.xml:34:13-37
211
212        <meta-data
212-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\363f04887ef37f5443484b40dba2058e\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
213            android:name="com.facebook.soloader.enabled"
213-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\363f04887ef37f5443484b40dba2058e\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
214            android:value="false" />
214-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\363f04887ef37f5443484b40dba2058e\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
215    </application>
216
217</manifest>
