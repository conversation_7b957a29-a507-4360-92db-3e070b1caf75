import AsyncStorage from '@react-native-async-storage/async-storage';

export interface UserProgress {
  completed_days: number[];
  last_active_day: number;
  last_access_time: string;
  total_questions_solved: number;
  badges_earned: string[];
  current_streak: number;
  best_streak: number;
}

export interface DayProgress {
  dayId: number;
  questionsAnswered: number[];
  isCompleted: boolean;
  completedAt?: string;
  score: number;
}

class StorageService {
  private static readonly USER_PROGRESS_KEY = 'user_progress';
  private static readonly DAY_PROGRESS_KEY = 'day_progress_';

  // Kullanıcı genel ilerlemesini getir
  static async getUserProgress(): Promise<UserProgress> {
    try {
      const data = await AsyncStorage.getItem(this.USER_PROGRESS_KEY);
      if (data) {
        return JSON.parse(data);
      }
      
      // İlk kez açılıyorsa varsayılan değerler
      const defaultProgress: UserProgress = {
        completed_days: [],
        last_active_day: 1,
        last_access_time: new Date().toISOString(),
        total_questions_solved: 0,
        badges_earned: [],
        current_streak: 0,
        best_streak: 0
      };
      
      await this.saveUserProgress(defaultProgress);
      return defaultProgress;
    } catch (error) {
      console.error('Error getting user progress:', error);
      throw error;
    }
  }

  // Kullanıcı genel ilerlemesini kaydet
  static async saveUserProgress(progress: UserProgress): Promise<void> {
    try {
      await AsyncStorage.setItem(this.USER_PROGRESS_KEY, JSON.stringify(progress));
    } catch (error) {
      console.error('Error saving user progress:', error);
      throw error;
    }
  }

  // Belirli bir günün ilerlemesini getir
  static async getDayProgress(dayId: number): Promise<DayProgress | null> {
    try {
      const data = await AsyncStorage.getItem(`${this.DAY_PROGRESS_KEY}${dayId}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Error getting day progress:', error);
      return null;
    }
  }

  // Belirli bir günün ilerlemesini kaydet
  static async saveDayProgress(dayProgress: DayProgress): Promise<void> {
    try {
      await AsyncStorage.setItem(
        `${this.DAY_PROGRESS_KEY}${dayProgress.dayId}`,
        JSON.stringify(dayProgress)
      );
    } catch (error) {
      console.error('Error saving day progress:', error);
      throw error;
    }
  }

  // Günü tamamla
  static async completeDay(dayId: number): Promise<boolean> {
    try {
      const userProgress = await this.getUserProgress();
      const now = new Date();
      
      // Aynı gün kontrolü
      const lastAccessDate = new Date(userProgress.last_access_time);
      if (this.isSameDay(now, lastAccessDate) && userProgress.last_active_day !== dayId) {
        return false; // Aynı gün içinde farklı gün tamamlanamaz
      }

      // Sıralı erişim kontrolü
      if (dayId > userProgress.last_active_day + 1) {
        return false; // Sıralı olmayan erişim
      }

      // Günü tamamlandı olarak işaretle
      if (!userProgress.completed_days.includes(dayId)) {
        userProgress.completed_days.push(dayId);
        userProgress.completed_days.sort((a, b) => a - b);
      }

      userProgress.last_active_day = Math.max(userProgress.last_active_day, dayId);
      userProgress.last_access_time = now.toISOString();
      
      // Streak hesapla
      this.updateStreak(userProgress);

      await this.saveUserProgress(userProgress);
      return true;
    } catch (error) {
      console.error('Error completing day:', error);
      return false;
    }
  }

  // Soru cevapla
  static async answerQuestion(dayId: number, questionId: number, isCorrect: boolean): Promise<void> {
    try {
      let dayProgress = await this.getDayProgress(dayId);
      
      if (!dayProgress) {
        dayProgress = {
          dayId,
          questionsAnswered: [],
          isCompleted: false,
          score: 0
        };
      }

      if (!dayProgress.questionsAnswered.includes(questionId)) {
        dayProgress.questionsAnswered.push(questionId);
        if (isCorrect) {
          dayProgress.score += 20; // Her doğru cevap 20 puan
        }
      }

      await this.saveDayProgress(dayProgress);

      // Genel istatistikleri güncelle
      if (isCorrect) {
        const userProgress = await this.getUserProgress();
        userProgress.total_questions_solved += 1;
        await this.saveUserProgress(userProgress);
      }
    } catch (error) {
      console.error('Error answering question:', error);
      throw error;
    }
  }

  // Rozet ekle
  static async addBadge(badgeId: string): Promise<void> {
    try {
      const userProgress = await this.getUserProgress();
      if (!userProgress.badges_earned.includes(badgeId)) {
        userProgress.badges_earned.push(badgeId);
        await this.saveUserProgress(userProgress);
      }
    } catch (error) {
      console.error('Error adding badge:', error);
      throw error;
    }
  }

  // Aynı gün kontrolü
  private static isSameDay(date1: Date, date2: Date): boolean {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  }

  // Streak güncelle
  private static updateStreak(userProgress: UserProgress): void {
    const sortedDays = userProgress.completed_days.sort((a, b) => a - b);
    let currentStreak = 0;
    let maxStreak = 0;
    let tempStreak = 1;

    for (let i = 1; i < sortedDays.length; i++) {
      if (sortedDays[i] === sortedDays[i - 1] + 1) {
        tempStreak++;
      } else {
        maxStreak = Math.max(maxStreak, tempStreak);
        tempStreak = 1;
      }
    }

    maxStreak = Math.max(maxStreak, tempStreak);
    currentStreak = tempStreak;

    userProgress.current_streak = currentStreak;
    userProgress.best_streak = Math.max(userProgress.best_streak, maxStreak);
  }

  // Tüm verileri sıfırla (test amaçlı)
  static async resetAllData(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Error resetting data:', error);
      throw error;
    }
  }
}

export default StorageService;
