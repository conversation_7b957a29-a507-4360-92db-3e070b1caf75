import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserProgress, DayProgress, QuestionAnswer } from '../types';

// Re-export types for convenience
export type { UserProgress, DayProgress };



class StorageService {
  private static readonly USER_PROGRESS_KEY = 'user_progress';
  private static readonly DAY_PROGRESS_KEY = 'day_progress_';

  // Kullanıcı genel ilerlemesini getir
  static async getUserProgress(): Promise<UserProgress> {
    try {
      const data = await AsyncStorage.getItem(this.USER_PROGRESS_KEY);
      if (data) {
        const progress = JSON.parse(data);
        // Date objelerini yeniden oluştur
        if (progress.last_activity) {
          progress.last_activity = new Date(progress.last_activity);
        }

        // Day progress içindeki date'leri de dönüştür
        Object.keys(progress.day_progress || {}).forEach(dayId => {
          const dayProgress = progress.day_progress[dayId];
          if (dayProgress.completedAt) {
            dayProgress.completedAt = new Date(dayProgress.completedAt);
          }
          if (dayProgress.questionsAnswered) {
            dayProgress.questionsAnswered.forEach((qa: any) => {
              if (qa.answeredAt) {
                qa.answeredAt = new Date(qa.answeredAt);
              }
            });
          }
        });

        return progress;
      }

      // İlk kez açılıyorsa varsayılan değerler
      const defaultProgress: UserProgress = {
        completed_days: [],
        current_streak: 0,
        best_streak: 0,
        total_questions_answered: 0,
        total_correct_answers: 0,
        total_points: 0,
        last_activity: new Date(),
        day_progress: {},
        badges_earned: [],
      };

      await this.saveUserProgress(defaultProgress);
      return defaultProgress;
    } catch (error) {
      console.error('Error getting user progress:', error);
      return {
        completed_days: [],
        current_streak: 0,
        best_streak: 0,
        total_questions_answered: 0,
        total_correct_answers: 0,
        total_points: 0,
        last_activity: new Date(),
        day_progress: {},
        badges_earned: []
      };
    }
  }

  
  // Kullanıcı genel ilerlemesini kaydet
  static async saveUserProgress(progress: UserProgress): Promise<void> {
    try {
      await AsyncStorage.setItem(this.USER_PROGRESS_KEY, JSON.stringify(progress));
    } catch (error) {
      console.error('Error saving user progress:', error);
      throw error;
    }
  }



  // Soru cevapla
  static async answerQuestion(dayId: number, questionId: number, isCorrect: boolean, selectedAnswer: string): Promise<void> {
    try {
      const userProgress = await this.getUserProgress();

      // Gün progress'ini al veya oluştur
      let dayProgress = userProgress.day_progress[dayId];
      if (!dayProgress) {
        dayProgress = {
          dayId,
          isCompleted: false,
          questionsAnswered: [],
          score: 0,
          timeSpent: 0,
        };
        userProgress.day_progress[dayId] = dayProgress;
      }

      // Soruyu daha önce cevaplamış mı kontrol et
      const alreadyAnswered = dayProgress.questionsAnswered.some(qa => qa.questionId === questionId);

      if (!alreadyAnswered) {
        if (isCorrect) {
          dayProgress.score += 20; // Her doğru cevap 20 puan
          userProgress.total_correct_answers++;
        }
        userProgress.total_questions_answered++;
        userProgress.total_points += isCorrect ? 20 : 0;

        // Soru cevabını kaydet
        const questionAnswer: QuestionAnswer = {
          questionId,
          isCorrect,
          answeredAt: new Date(),
          selectedAnswer,
        };
        dayProgress.questionsAnswered.push(questionAnswer);

        await this.saveUserProgress(userProgress);
      }
    } catch (error) {
      console.error('Error answering question:', error);
      throw error;
    }
  }

  // Quiz progress'ini kaydet (kaldığı yerden devam için)
  static async saveQuizProgress(dayId: number, progress: {
    currentQuestionIndex: number;
    answers: { [questionId: number]: string };
    score: number;
    timeRemaining: number;
    wrongQuestions?: number[]; // Bu satırı ekleyin

  }): Promise<void> {
    try {
      const userProgress = await this.getUserProgress();

      if (!userProgress.quiz_progress) {
        userProgress.quiz_progress = {};
      }

      userProgress.quiz_progress[dayId] = {
        ...progress,
        lastSaved: new Date(),
      };

      await this.saveUserProgress(userProgress);
    } catch (error) {
      console.error('Error saving quiz progress:', error);
      throw error;
    }
  }

  // Quiz progress'ini getir
  static async getQuizProgress(dayId: number): Promise<{
    currentQuestionIndex: number;
    answers: { [questionId: number]: string };
    score: number;
    timeRemaining: number;
  } | null> {
    try {
      const userProgress = await this.getUserProgress();
      return userProgress.quiz_progress?.[dayId] || null;
    } catch (error) {
      console.error('Error getting quiz progress:', error);
      return null;
    }
  }

  // Yanlış soruları getir (son 3 günden)
  static async getWrongQuestionsForReview(currentDayId: number): Promise<{
    dayId: number;
    questionIds: number[];
  }[]> {
    try {
      const userProgress = await this.getUserProgress();
      const wrongQuestions: { dayId: number; questionIds: number[] }[] = [];

      if (!userProgress.wrong_questions) return wrongQuestions;

      // Son 3 günden yanlış soruları al
      for (let i = Math.max(1, currentDayId - 3); i < currentDayId; i++) {
        if (userProgress.wrong_questions[i] && userProgress.wrong_questions[i].length > 0) {
          wrongQuestions.push({
            dayId: i,
            questionIds: userProgress.wrong_questions[i]
          });
        }
      }

      return wrongQuestions;
    } catch (error) {
      console.error('Error getting wrong questions:', error);
      return [];
    }
  }

  // Quiz sonucunu kaydet
  static async completeQuiz(dayId: number, quizResult: {
    totalQuestions: number;
    correctAnswers: number;
    wrongAnswers: number;
    skippedAnswers: number;
    successRate: number;
    isPassed: boolean;
    wrongQuestions: number[];
    timeSpent: number;
  }): Promise<void> {
    try {
      const userProgress = await this.getUserProgress();

      // Quiz sonucunu kaydet
      if (!userProgress.quiz_results) {
        userProgress.quiz_results = {};
      }

      userProgress.quiz_results[dayId] = {
        ...quizResult,
        completedAt: new Date(),
        attempts: (userProgress.quiz_results[dayId]?.attempts || 0) + 1
      };

      // Yanlış soruları global listede tut
      if (!userProgress.wrong_questions) {
        userProgress.wrong_questions = {};
      }

      if (quizResult.wrongQuestions.length > 0) {
        userProgress.wrong_questions[dayId] = quizResult.wrongQuestions;
      }

      // Toplam çözülen soru sayısını güncelle
      if (!userProgress.total_questions_solved) {
        userProgress.total_questions_solved = 0;
      }
      userProgress.total_questions_solved += quizResult.totalQuestions;

      userProgress.last_activity = new Date();
      await this.saveUserProgress(userProgress);
    } catch (error) {
      console.error('Error saving quiz result:', error);
      throw error;
    }
  }

  // Günü tamamla (sadece 90% başarı ile ve günlük tek tamamlama)
  static async completeDay(dayId: number, score: number, timeSpent: number): Promise<void> {
    try {
      const userProgress = await this.getUserProgress();
      const today = new Date();

      // Bugün zaten bir gün tamamlanmış mı kontrol et
      const todayCompletions = userProgress.completed_days.filter(completedDay => {
        const dayProgress = userProgress.day_progress[completedDay];
        if (!dayProgress?.completedAt) return false;

        const completedDate = new Date(dayProgress.completedAt);
        return this.isSameDay(today, completedDate);
      });

      // Eğer bugün zaten bir gün tamamlanmışsa, sadece progress'i güncelle ama completed_days'e ekleme
      const alreadyCompletedToday = todayCompletions.length > 0;

      // Gün progress'ini güncelle
      let dayProgress = userProgress.day_progress[dayId];
      if (!dayProgress) {
        dayProgress = {
          dayId,
          isCompleted: false,
          questionsAnswered: [],
          score: 0,
          timeSpent: 0,
        };
        userProgress.day_progress[dayId] = dayProgress;
      }

      dayProgress.isCompleted = true;
      dayProgress.completedAt = new Date();
      dayProgress.score = score;
      dayProgress.timeSpent = timeSpent;

      // Sadece bugün ilk tamamlama ise completed_days'e ekle
      if (!alreadyCompletedToday && !userProgress.completed_days.includes(dayId)) {
        userProgress.completed_days.push(dayId);
        userProgress.completed_days.sort((a, b) => a - b);

        // Streak hesapla (sadece ilk tamamlamada)
        this.updateStreak(userProgress);
      }

      userProgress.last_activity = new Date();
      await this.saveUserProgress(userProgress);
    } catch (error) {
      console.error('Error completing day:', error);
      throw error;
    }
  }

  // Aynı gün kontrolü için yardımcı metod
  private static isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  // Streak güncelle
  private static updateStreak(userProgress: UserProgress): void {
    const sortedDays = userProgress.completed_days.sort((a, b) => a - b);
    let currentStreak = 0;
    let maxStreak = 0;
    let tempStreak = 1;

    for (let i = 1; i < sortedDays.length; i++) {
      if (sortedDays[i] === sortedDays[i - 1] + 1) {
        tempStreak++;
      } else {
        maxStreak = Math.max(maxStreak, tempStreak);
        tempStreak = 1;
      }
    }

    maxStreak = Math.max(maxStreak, tempStreak);
    currentStreak = tempStreak;

    userProgress.current_streak = currentStreak;
    userProgress.best_streak = Math.max(userProgress.best_streak, maxStreak);
  }

  // Tüm verileri sıfırla (test amaçlı)
  static async resetAllData(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Error resetting data:', error);
      throw error;
    }
  }

  static async isDayUnlocked(dayId: number): Promise<boolean> {
    try {
      if (dayId === 1) return true; // İlk gün her zaman açık

      const progress = await this.getUserProgress();
      const previousDayId = dayId - 1;

      // Önceki gün tamamlanmış mı?
      if (!progress.completed_days.includes(previousDayId)) {
        return false;
      }

      // Önceki günün tamamlanma zamanını kontrol et
      const previousDayProgress = progress.day_progress[previousDayId];
      if (!previousDayProgress || !previousDayProgress.completedAt) {
        return false;
      }

      // 6 saat geçmiş mi?
      const now = new Date();
      const completedAt = new Date(previousDayProgress.completedAt);
      const hoursDiff = (now.getTime() - completedAt.getTime()) / (1000 * 60 * 60);

      return hoursDiff >= 6;
    } catch (error) {
      console.error('Error checking day unlock status:', error);
      return false;
    }
  }

  static async resetProgress(): Promise<void> {
    try {
      await AsyncStorage.removeItem('user_progress');
    } catch (error) {
      console.error('Error resetting progress:', error);
      throw error;
    }
  }

  static async getStreakInfo(): Promise<{
    current: number;
    best: number;
    daysCompleted: number[];
  }> {
    try {
      const progress = await this.getUserProgress();
      return {
        current: progress.current_streak,
        best: progress.best_streak,
        daysCompleted: progress.completed_days,
      };
    } catch (error) {
      console.error('Error getting streak info:', error);
      return { current: 0, best: 0, daysCompleted: [] };
    }
  }

  static async getStatistics(): Promise<{
    totalDaysCompleted: number;
    totalQuestionsAnswered: number;
    totalCorrectAnswers: number;
    averageScore: number;
    currentStreak: number;
    bestStreak: number;
    totalTimeSpent: number;
    successRate: number;
  }> {
    try {
      const progress = await this.getUserProgress();

      const totalTimeSpent = Object.values(progress.day_progress)
        .reduce((total, day) => total + day.timeSpent, 0);

      const averageScore = progress.completed_days.length > 0
        ? Object.values(progress.day_progress)
            .reduce((total, day) => total + day.score, 0) / progress.completed_days.length
        : 0;

      const successRate = progress.total_questions_answered > 0
        ? (progress.total_correct_answers / progress.total_questions_answered) * 100
        : 0;

      return {
        totalDaysCompleted: progress.completed_days.length,
        totalQuestionsAnswered: progress.total_questions_answered,
        totalCorrectAnswers: progress.total_correct_answers,
        averageScore,
        currentStreak: progress.current_streak,
        bestStreak: progress.best_streak,
        totalTimeSpent,
        successRate,
      };
    } catch (error) {
      console.error('Error getting statistics:', error);
      return {
        totalDaysCompleted: 0,
        totalQuestionsAnswered: 0,
        totalCorrectAnswers: 0,
        averageScore: 0,
        currentStreak: 0,
        bestStreak: 0,
        totalTimeSpent: 0,
        successRate: 0,
      };
    }
  }
}

export default StorageService;
