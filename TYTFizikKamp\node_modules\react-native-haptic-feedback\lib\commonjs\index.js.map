{"version": 3, "names": ["_NativeHapticFeedback", "_interopRequireDefault", "require", "_types", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "e", "__esModule", "default", "defaultOptions", "enableVibrateFallback", "ignoreAndroidSystemSettings", "RNHapticFeedback", "trigger", "type", "HapticFeedbackTypes", "selection", "options", "NativeHapticFeedback", "console", "warn", "_default"], "sourceRoot": "../../src", "sources": ["index.ts"], "mappings": ";;;;;;;;;AAAA,IAAAA,qBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAuBAE,MAAA,CAAAC,IAAA,CAAAF,MAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAJ,MAAA,CAAAI,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAZ,MAAA,CAAAI,GAAA;IAAA;EAAA;AAAA;AAAwB,SAAAN,uBAAAe,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AApBxB,MAAMG,cAAc,GAAG;EACrBC,qBAAqB,EAAE,KAAK;EAC5BC,2BAA2B,EAAE;AAC/B,CAAC;AAED,MAAMC,gBAAgB,GAAG;EACvBC,OAAOA,CACLC,IAEuB,GAAGC,0BAAmB,CAACC,SAAS,EACvDC,OAAsB,GAAG,CAAC,CAAC,EAC3B;IACA,IAAI;MACFC,6BAAoB,CAACL,OAAO,CAACC,IAAI,EAAE;QAAE,GAAGL,cAAc;QAAE,GAAGQ;MAAQ,CAAC,CAAC;IACvE,CAAC,CAAC,MAAM;MACNE,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;IAC9D;EACF;AACF,CAAC;AAGM,MAAM;EAAEP;AAAQ,CAAC,GAAGD,gBAAgB;AAACV,OAAA,CAAAW,OAAA,GAAAA,OAAA;AAAA,IAAAQ,QAAA,GAAAnB,OAAA,CAAAM,OAAA,GAC7BI,gBAAgB", "ignoreList": []}