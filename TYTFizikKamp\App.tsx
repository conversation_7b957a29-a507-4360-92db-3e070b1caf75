/**
 * TYT Fizik Kampı - 14 Günlük Yoğun Eğitim Uygulaması
 * Augment Agent tarafından geliştirildi
 *
 * @format
 */

import React from 'react';
import { StatusBar } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator, StackScreenProps } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

// Navigation Types
export type RootStackParamList = {
  Main: undefined;
  DayDetail: { dayId: number };
  Quiz: { dayId: number; reviewMode?: boolean; reviewQuestions?: number[]; retryMode?: boolean; };
};

export type DayDetailScreenProps = StackScreenProps<RootStackParamList, 'DayDetail'>;
export type QuizScreenProps = StackScreenProps<RootStackParamList, 'Quiz'>;

import { ThemeProvider, useTheme } from './src/contexts/ThemeContext';

// Screens
import HomeScreen from './src/screens/HomeScreen';
import DayDetailScreen from './src/screens/DayDetailScreen';
import NewQuizScreen from './src/screens/NewQuizScreen';
import BadgeScreen from './src/screens/BadgeScreen';
import SettingsScreen from './src/screens/SettingsScreen';
import AnalyticsScreen from './src/screens/AnalyticsScreen';
import PomodoroScreen from './src/screens/PomodoroScreen';
import NotificationService from './src/services/NotificationService';

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator();

// Ana Tab Navigator
const TabNavigator = () => {
  const { theme } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Ana Sayfa':
              iconName = 'home';
              break;
            case 'Rehberlik':
              iconName = 'analytics';
              break;
            case 'Pomodoro':
              iconName = 'timer';
              break;
            case 'Rozetler':
              iconName = 'emoji-events';
              break;
            case 'Ayarlar':
              iconName = 'settings';
              break;
            default:
              iconName = 'circle';
          }

          return <MaterialIcons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.textSecondary,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopColor: theme.colors.border,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen name="Ana Sayfa" component={HomeScreen} />
      <Tab.Screen name="Rehberlik" component={AnalyticsScreen} />
      <Tab.Screen name="Pomodoro" component={PomodoroScreen} />
      <Tab.Screen name="Rozetler" component={BadgeScreen} />
      <Tab.Screen name="Ayarlar" component={SettingsScreen} />
    </Tab.Navigator>
  );
};

// Ana Stack Navigator
const AppNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      }}
    >
      <Stack.Screen name="Main" component={TabNavigator} />
      <Stack.Screen name="DayDetail" component={DayDetailScreen} />
      <Stack.Screen name="Quiz" component={NewQuizScreen} />
    </Stack.Navigator>
  );
};

// Ana App Bileşeni
const App = () => {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
};

// Theme Provider içindeki içerik
const AppContent = () => {
  const { theme } = useTheme();

  // Bildirim servisini başlat
  React.useEffect(() => {
    const initializeNotifications = async () => {
      try {
        await NotificationService.initialize();
        await NotificationService.requestPermission();
        console.log('Notification service initialized successfully');
      } catch (error) {
        console.error('Error initializing notification service:', error);
      }
    };

    initializeNotifications();
  }, []);

  return (
    <NavigationContainer>
      <StatusBar
        barStyle={theme.isDark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.colors.primary}
      />
      <AppNavigator />
    </NavigationContainer>
  );
};

export default App;
