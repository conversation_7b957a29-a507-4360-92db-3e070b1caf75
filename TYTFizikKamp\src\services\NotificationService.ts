import { Alert, Platform, PermissionsAndroid } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import PushNotification from 'react-native-push-notification';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import Sound from 'react-native-sound';
// import Sound from 'react-native-sound';

interface NotificationSettings {
  enabled: boolean;
  pomodoroEnabled: boolean;
  studyRemindersEnabled: boolean;
  dailyGoalReminders: boolean;
  reminderTime: string; // HH:MM format
}

interface PomodoroNotification {
  id: string;
  title: string;
  message: string;
  scheduledTime: Date;
  type: 'work_complete' | 'break_complete' | 'session_reminder';
}

class NotificationService {
  private static readonly SETTINGS_KEY = 'notification_settings';
  private static readonly SCHEDULED_NOTIFICATIONS_KEY = 'scheduled_notifications';
  private static isInitialized = false;

  // Varsayılan ayarlar
  private static defaultSettings: NotificationSettings = {
    enabled: true,
    pomodoroEnabled: true,
    studyRemindersEnabled: true,
    dailyGoalReminders: true,
    reminderTime: '20:00',
  };

  // Haptic feedback seçenekleri
  private static hapticOptions = {
    enableVibrateFallback: true,
    ignoreAndroidSystemSettings: false,
  };

  // Ses sistemi - basitleştirilmiş
  private static soundEnabled: boolean = true;

  // Ses dosyalarını yükle - basitleştirilmiş
  private static loadSounds(): void {
    try {
      // Ses kategorisini ayarla
      Sound.setCategory('Playback');
      console.log('Sound system initialized successfully');
    } catch (error) {
      console.error('Error loading sound system:', error);
      this.soundEnabled = false;
    }
  }

  // Bildirim kanalları
  private static channels = {
    pomodoro: 'pomodoro-channel',
    study: 'study-channel',
    achievement: 'achievement-channel',
    reminder: 'reminder-channel',
  };

  // Servis başlatma
  static async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Ses dosyalarını yükle
      this.loadSounds();

      // Push notification konfigürasyonu
      PushNotification.configure({
        onRegister: function (token: any) {
          console.log('TOKEN:', token);
        },
        onNotification: function (notification: any) {
          console.log('NOTIFICATION:', notification);

          // Bildirime tıklandığında haptic feedback
          if (notification.userInteraction) {
            NotificationService.triggerHapticFeedback('impactMedium');
          }
        },
        onAction: function (notification: any) {
          console.log('ACTION:', notification.action);
          console.log('NOTIFICATION:', notification);
        },
        onRegistrationError: function(err: any) {
          console.error(err.message, err);
        },
        permissions: {
          alert: true,
          badge: true,
          sound: true,
        },
        popInitialNotification: true,
        requestPermissions: Platform.OS === 'ios',
      });

      // Bildirim kanalları oluştur (Android)
      if (Platform.OS === 'android') {
        PushNotification.createChannel(
          {
            channelId: this.channels.pomodoro,
            channelName: 'Pomodoro Bildirimleri',
            channelDescription: 'Pomodoro timer bildirimleri',
            playSound: true,
            soundName: 'default',
            importance: 4,
            vibrate: true,
          },
          (created: any) => console.log(`Pomodoro channel created: ${created}`)
        );

        PushNotification.createChannel(
          {
            channelId: this.channels.study,
            channelName: 'Çalışma Bildirimleri',
            channelDescription: 'Günlük çalışma hatırlatıcıları',
            playSound: true,
            soundName: 'default',
            importance: 3,
            vibrate: true,
          },
          (created: any) => console.log(`Study channel created: ${created}`)
        );

        PushNotification.createChannel(
          {
            channelId: this.channels.achievement,
            channelName: 'Başarı Bildirimleri',
            channelDescription: 'Başarı ve rozet bildirimleri',
            playSound: true,
            soundName: 'default',
            importance: 3,
            vibrate: true,
          },
          (created: any) => console.log(`Achievement channel created: ${created}`)
        );

        PushNotification.createChannel(
          {
            channelId: this.channels.reminder,
            channelName: 'Hatırlatıcılar',
            channelDescription: 'Genel hatırlatıcı bildirimleri',
            playSound: true,
            soundName: 'default',
            importance: 2,
            vibrate: false,
          },
          (created: any) => console.log(`Reminder channel created: ${created}`)
        );
      }

      this.isInitialized = true;
      console.log('NotificationService initialized successfully');
    } catch (error) {
      console.error('Error initializing NotificationService:', error);
    }
  }

  // Bildirim ayarlarını getir
  static async getSettings(): Promise<NotificationSettings> {
    try {
      const settings = await AsyncStorage.getItem(this.SETTINGS_KEY);
      if (settings) {
        return { ...this.defaultSettings, ...JSON.parse(settings) };
      }
      return this.defaultSettings;
    } catch (error) {
      console.error('Error getting notification settings:', error);
      return this.defaultSettings;
    }
  }

  // Bildirim ayarlarını kaydet
  static async saveSettings(settings: NotificationSettings): Promise<void> {
    try {
      await AsyncStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving notification settings:', error);
      throw error;
    }
  }

  // Haptic feedback tetikle
  static triggerHapticFeedback(type: 'selection' | 'impactLight' | 'impactMedium' | 'impactHeavy' | 'notificationSuccess' | 'notificationWarning' | 'notificationError' = 'impactMedium'): void {
    try {
      ReactNativeHapticFeedback.trigger(type, this.hapticOptions);
    } catch (error) {
      console.error('Error triggering haptic feedback:', error);
    }
  }

  // Ses çal
  static playSound(soundType: 'notification' | 'success' | 'pomodoro' | 'error' = 'notification'): void {
    try {
      if (!this.soundEnabled) {
        console.log('Sound system disabled');
        return;
      }

      // Sistem varsayılan sesini çal
      const sound = new Sound('default', '', (error) => {
        if (error) {
          console.log('Failed to load sound:', error);
        } else {
          sound.play((success) => {
            if (!success) {
              console.log('Failed to play sound:', soundType);
            }
            sound.release(); // Belleği temizle
          });
        }
      });
    } catch (error) {
      console.error('Error playing sound:', error);
    }
  }

  // Bildirim izni iste
  static async requestPermission(): Promise<boolean> {
    try {
      await this.initialize();

      if (Platform.OS === 'android') {
        // Android 13+ için POST_NOTIFICATIONS izni
        if (Platform.Version >= 33) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
            {
              title: 'Bildirim İzni',
              message: 'TYT Fizik Kamp uygulaması bildirim gönderebilmek için izin istiyor.',
              buttonNeutral: 'Daha Sonra Sor',
              buttonNegative: 'İptal',
              buttonPositive: 'İzin Ver',
            }
          );
          return granted === PermissionsAndroid.RESULTS.GRANTED;
        }
        return true;
      } else {
        // iOS için izin kontrolü PushNotification.configure içinde yapılıyor
        return true;
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }

  // Pomodoro bildirimi gönder
  static async sendPomodoroNotification(
    type: 'work_complete' | 'break_complete' | 'session_reminder',
    customMessage?: string
  ): Promise<void> {
    try {
      const settings = await this.getSettings();

      if (!settings.enabled || !settings.pomodoroEnabled) {
        return;
      }

      let title = '';
      let message = customMessage || '';
      let hapticType: 'impactLight' | 'impactMedium' | 'impactHeavy' | 'notificationSuccess' = 'impactMedium';

      switch (type) {
        case 'work_complete':
          title = '🎉 Çalışma Tamamlandı!';
          message = message || 'Harika! Şimdi mola zamanı. Biraz dinlen ve geri dön.';
          hapticType = 'notificationSuccess';
          break;
        case 'break_complete':
          title = '⏰ Mola Bitti!';
          message = message || 'Mola sona erdi. Tekrar çalışma zamanı!';
          hapticType = 'impactHeavy';
          break;
        case 'session_reminder':
          title = '📚 Çalışma Zamanı!';
          message = message || 'Pomodoro seansını başlatmayı unutma!';
          hapticType = 'impactMedium';
          break;
      }

      // Haptic feedback tetikle
      this.triggerHapticFeedback(hapticType);

      // Ses çal
      this.playSound('pomodoro');

      // Push notification gönder
      this.sendPushNotification(title, message, this.channels.pomodoro, {
        priority: 'high',
        vibrate: true,
        playSound: true,
        largeIcon: 'ic_launcher',
        smallIcon: 'ic_notification',
      });

    } catch (error) {
      console.error('Error sending pomodoro notification:', error);
    }
  }

  // Günlük çalışma hatırlatıcısı
  static async sendDailyStudyReminder(): Promise<void> {
    try {
      const settings = await this.getSettings();

      if (!settings.enabled || !settings.studyRemindersEnabled) {
        return;
      }

      const title = '📖 Günlük Çalışma Zamanı!';
      const message = 'TYT Fizik kampında bugünkü dersini tamamlamayı unutma!';

      this.triggerHapticFeedback('impactMedium');
      this.playSound('notification');
      this.sendPushNotification(title, message, this.channels.study, {
        priority: 'default',
        vibrate: true,
        playSound: true,
      });

    } catch (error) {
      console.error('Error sending daily study reminder:', error);
    }
  }

  // Hedef hatırlatıcısı
  static async sendGoalReminder(completedDays: number, totalDays: number = 14): Promise<void> {
    try {
      const settings = await this.getSettings();

      if (!settings.enabled || !settings.dailyGoalReminders) {
        return;
      }

      const remainingDays = totalDays - completedDays;
      const title = '🎯 Hedefine Yaklaşıyorsun!';
      const message = `${completedDays}/${totalDays} gün tamamlandı. ${remainingDays} gün kaldı!`;

      this.triggerHapticFeedback('impactLight');
      this.sendPushNotification(title, message, this.channels.reminder, {
        priority: 'default',
        vibrate: false,
        playSound: true,
      });

    } catch (error) {
      console.error('Error sending goal reminder:', error);
    }
  }

  // Başarı bildirimi
  static async sendAchievementNotification(achievement: string): Promise<void> {
    try {
      const settings = await this.getSettings();

      if (!settings.enabled) {
        return;
      }

      const title = '🏆 Yeni Başarı!';
      const message = achievement;

      this.triggerHapticFeedback('notificationSuccess');
      this.playSound('success');
      this.sendPushNotification(title, message, this.channels.achievement, {
        priority: 'high',
        vibrate: true,
        playSound: true,
        largeIcon: 'ic_launcher',
        color: '#FFD700',
      });

    } catch (error) {
      console.error('Error sending achievement notification:', error);
    }
  }

  // Yanlış soru hatırlatıcısı
  static async sendWrongQuestionReminder(wrongQuestionCount: number): Promise<void> {
    try {
      const settings = await this.getSettings();
      
      if (!settings.enabled || !settings.studyRemindersEnabled) {
        return;
      }

      if (wrongQuestionCount > 0) {
        const title = '🔄 Yanlış Sorular!';
        const message = `${wrongQuestionCount} yanlış sorun var. Tekrar çözmeyi unutma!`;
        
        this.triggerHapticFeedback('impactMedium');
        this.playSound('notification');
        this.sendPushNotification(title, message, this.channels.reminder, {
          priority: 'default',
          vibrate: true,
          playSound: true,
        });
      }
      
    } catch (error) {
      console.error('Error sending wrong question reminder:', error);
    }
  }

  // Streak hatırlatıcısı
  static async sendStreakReminder(currentStreak: number): Promise<void> {
    try {
      const settings = await this.getSettings();
      
      if (!settings.enabled || !settings.studyRemindersEnabled) {
        return;
      }

      let title = '';
      let message = '';

      if (currentStreak === 0) {
        title = '🔥 Seriyi Başlat!';
        message = 'Bugün çalışarak yeni bir seri başlat!';
      } else if (currentStreak >= 7) {
        title = '🔥 Muhteşem Seri!';
        message = `${currentStreak} günlük serini koruyorsun! Devam et!`;
      } else {
        title = '🔥 Seriyi Koru!';
        message = `${currentStreak} günlük serini kaybetme! Bugün de çalış.`;
      }
      
      this.triggerHapticFeedback('impactLight');
      this.playSound('notification');
      this.sendPushNotification(title, message, this.channels.reminder, {
        priority: 'default',
        vibrate: false,
        playSound: true,
      });
      
    } catch (error) {
      console.error('Error sending streak reminder:', error);
    }
  }

  // Push notification gönder
  private static sendPushNotification(
    title: string,
    message: string,
    channelId: string,
    options: {
      priority?: 'min' | 'low' | 'default' | 'high' | 'max';
      vibrate?: boolean;
      playSound?: boolean;
      largeIcon?: string;
      smallIcon?: string;
      color?: string;
      autoCancel?: boolean;
      ongoing?: boolean;
    } = {}
  ): void {
    try {
      const notificationId = Math.floor(Math.random() * 1000000);

      PushNotification.localNotification({
        id: notificationId,
        title: title,
        message: message,
        channelId: channelId,
        priority: options.priority || 'default',
        vibrate: options.vibrate !== false,
        playSound: options.playSound !== false,
        soundName: 'default',
        largeIcon: options.largeIcon || 'ic_launcher',
        smallIcon: options.smallIcon || 'ic_notification',
        color: options.color || '#6C5CE7',
        autoCancel: options.autoCancel !== false,
        ongoing: options.ongoing || false,
        invokeApp: true,
        actions: ['Tamam'],
        userInfo: {
          type: channelId,
          timestamp: Date.now(),
        },
      });

      console.log(`Notification sent: ${title} - ${message}`);
    } catch (error) {
      console.error('Error sending push notification:', error);

      // Fallback olarak Alert göster
      setTimeout(() => {
        Alert.alert(title, message, [
          { text: 'Tamam', style: 'default' }
        ]);
      }, 100);
    }
  }

  // Zamanlanmış bildirimleri kaydet
  static async scheduleNotification(notification: PomodoroNotification): Promise<void> {
    try {
      const existing = await AsyncStorage.getItem(this.SCHEDULED_NOTIFICATIONS_KEY);
      const notifications: PomodoroNotification[] = existing ? JSON.parse(existing) : [];
      
      notifications.push(notification);
      
      await AsyncStorage.setItem(this.SCHEDULED_NOTIFICATIONS_KEY, JSON.stringify(notifications));
    } catch (error) {
      console.error('Error scheduling notification:', error);
    }
  }

  // Zamanlanmış bildirimleri temizle
  static async clearScheduledNotifications(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.SCHEDULED_NOTIFICATIONS_KEY);
    } catch (error) {
      console.error('Error clearing scheduled notifications:', error);
    }
  }

  // Test bildirimi gönder
  static async sendTestNotification(): Promise<void> {
    const title = '🧪 Test Bildirimi';
    const message = 'Bildirimler düzgün çalışıyor!';

    this.triggerHapticFeedback('impactMedium');
    this.playSound('notification');
    this.sendPushNotification(title, message, this.channels.reminder, {
      priority: 'high',
      vibrate: true,
      playSound: true,
      color: '#00FF00',
    });
  }

  // Quiz tamamlama bildirimi
  static async sendQuizCompletionNotification(score: number, passed: boolean): Promise<void> {
    try {
      const settings = await this.getSettings();

      if (!settings.enabled) {
        return;
      }

      let title = '';
      let message = '';
      let hapticType: 'notificationSuccess' | 'notificationWarning' = 'notificationSuccess';

      if (passed) {
        title = '🎉 Quiz Başarıyla Tamamlandı!';
        message = `Tebrikler! %${score} ile geçtin. Bir sonraki güne geçebilirsin.`;
        hapticType = 'notificationSuccess';
      } else {
        title = '📚 Quiz Tamamlandı';
        message = `%${score} aldın. %90 için tekrar dene!`;
        hapticType = 'notificationWarning';
      }

      this.triggerHapticFeedback(hapticType);
      this.playSound(passed ? 'success' : 'notification');
      this.sendPushNotification(title, message, this.channels.study, {
        priority: 'high',
        vibrate: true,
        playSound: true,
        color: passed ? '#00FF00' : '#FFA500',
      });

    } catch (error) {
      console.error('Error sending quiz completion notification:', error);
    }
  }

  // Günlük tamamlama bildirimi
  static async sendDayCompletionNotification(dayNumber: number): Promise<void> {
    try {
      const settings = await this.getSettings();

      if (!settings.enabled) {
        return;
      }

      const title = '🎯 Gün Tamamlandı!';
      const message = `${dayNumber}. gün başarıyla tamamlandı! Yarın yeni konular seni bekliyor.`;

      this.triggerHapticFeedback('notificationSuccess');
      this.playSound('success');
      this.sendPushNotification(title, message, this.channels.achievement, {
        priority: 'high',
        vibrate: true,
        playSound: true,
        color: '#6C5CE7',
      });

    } catch (error) {
      console.error('Error sending day completion notification:', error);
    }
  }
}

export default NotificationService;
export type { NotificationSettings, PomodoroNotification };
