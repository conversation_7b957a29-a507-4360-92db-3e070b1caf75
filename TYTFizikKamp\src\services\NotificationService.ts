import { Alert, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface NotificationSettings {
  enabled: boolean;
  pomodoroEnabled: boolean;
  studyRemindersEnabled: boolean;
  dailyGoalReminders: boolean;
  reminderTime: string; // HH:MM format
}

interface PomodoroNotification {
  id: string;
  title: string;
  message: string;
  scheduledTime: Date;
  type: 'work_complete' | 'break_complete' | 'session_reminder';
}

class NotificationService {
  private static readonly SETTINGS_KEY = 'notification_settings';
  private static readonly SCHEDULED_NOTIFICATIONS_KEY = 'scheduled_notifications';

  // Varsayılan ayarlar
  private static defaultSettings: NotificationSettings = {
    enabled: true,
    pomodoroEnabled: true,
    studyRemindersEnabled: true,
    dailyGoalReminders: true,
    reminderTime: '20:00',
  };

  // Bildirim ayarlarını getir
  static async getSettings(): Promise<NotificationSettings> {
    try {
      const settings = await AsyncStorage.getItem(this.SETTINGS_KEY);
      if (settings) {
        return { ...this.defaultSettings, ...JSON.parse(settings) };
      }
      return this.defaultSettings;
    } catch (error) {
      console.error('Error getting notification settings:', error);
      return this.defaultSettings;
    }
  }

  // Bildirim ayarlarını kaydet
  static async saveSettings(settings: NotificationSettings): Promise<void> {
    try {
      await AsyncStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving notification settings:', error);
      throw error;
    }
  }

  // Bildirim izni iste
  static async requestPermission(): Promise<boolean> {
    try {
      // React Native'de gerçek push notification için
      // @react-native-push-notification veya @react-native-firebase/messaging kullanılmalı
      // Şimdilik basit Alert ile simüle ediyoruz
      
      if (Platform.OS === 'android') {
        // Android için izin kontrolü
        return true;
      } else {
        // iOS için izin kontrolü
        return true;
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }

  // Pomodoro bildirimi gönder
  static async sendPomodoroNotification(
    type: 'work_complete' | 'break_complete' | 'session_reminder',
    customMessage?: string
  ): Promise<void> {
    try {
      const settings = await this.getSettings();
      
      if (!settings.enabled || !settings.pomodoroEnabled) {
        return;
      }

      let title = '';
      let message = customMessage || '';

      switch (type) {
        case 'work_complete':
          title = '🎉 Çalışma Tamamlandı!';
          message = message || 'Harika! Şimdi mola zamanı. Biraz dinlen ve geri dön.';
          break;
        case 'break_complete':
          title = '⏰ Mola Bitti!';
          message = message || 'Mola sona erdi. Tekrar çalışma zamanı!';
          break;
        case 'session_reminder':
          title = '📚 Çalışma Zamanı!';
          message = message || 'Pomodoro seansını başlatmayı unutma!';
          break;
      }

      // Gerçek uygulamada push notification gönderilir
      // Şimdilik Alert ile gösteriyoruz
      this.showLocalNotification(title, message);
      
    } catch (error) {
      console.error('Error sending pomodoro notification:', error);
    }
  }

  // Günlük çalışma hatırlatıcısı
  static async sendDailyStudyReminder(): Promise<void> {
    try {
      const settings = await this.getSettings();
      
      if (!settings.enabled || !settings.studyRemindersEnabled) {
        return;
      }

      const title = '📖 Günlük Çalışma Zamanı!';
      const message = 'TYT Fizik kampında bugünkü dersini tamamlamayı unutma!';
      
      this.showLocalNotification(title, message);
      
    } catch (error) {
      console.error('Error sending daily study reminder:', error);
    }
  }

  // Hedef hatırlatıcısı
  static async sendGoalReminder(completedDays: number, totalDays: number = 14): Promise<void> {
    try {
      const settings = await this.getSettings();
      
      if (!settings.enabled || !settings.dailyGoalReminders) {
        return;
      }

      const remainingDays = totalDays - completedDays;
      const title = '🎯 Hedefine Yaklaşıyorsun!';
      const message = `${completedDays}/${totalDays} gün tamamlandı. ${remainingDays} gün kaldı!`;
      
      this.showLocalNotification(title, message);
      
    } catch (error) {
      console.error('Error sending goal reminder:', error);
    }
  }

  // Başarı bildirimi
  static async sendAchievementNotification(achievement: string): Promise<void> {
    try {
      const settings = await this.getSettings();
      
      if (!settings.enabled) {
        return;
      }

      const title = '🏆 Yeni Başarı!';
      const message = achievement;
      
      this.showLocalNotification(title, message);
      
    } catch (error) {
      console.error('Error sending achievement notification:', error);
    }
  }

  // Yanlış soru hatırlatıcısı
  static async sendWrongQuestionReminder(wrongQuestionCount: number): Promise<void> {
    try {
      const settings = await this.getSettings();
      
      if (!settings.enabled || !settings.studyRemindersEnabled) {
        return;
      }

      if (wrongQuestionCount > 0) {
        const title = '🔄 Yanlış Sorular!';
        const message = `${wrongQuestionCount} yanlış sorun var. Tekrar çözmeyi unutma!`;
        
        this.showLocalNotification(title, message);
      }
      
    } catch (error) {
      console.error('Error sending wrong question reminder:', error);
    }
  }

  // Streak hatırlatıcısı
  static async sendStreakReminder(currentStreak: number): Promise<void> {
    try {
      const settings = await this.getSettings();
      
      if (!settings.enabled || !settings.studyRemindersEnabled) {
        return;
      }

      let title = '';
      let message = '';

      if (currentStreak === 0) {
        title = '🔥 Seriyi Başlat!';
        message = 'Bugün çalışarak yeni bir seri başlat!';
      } else if (currentStreak >= 7) {
        title = '🔥 Muhteşem Seri!';
        message = `${currentStreak} günlük serini koruyorsun! Devam et!`;
      } else {
        title = '🔥 Seriyi Koru!';
        message = `${currentStreak} günlük serini kaybetme! Bugün de çalış.`;
      }
      
      this.showLocalNotification(title, message);
      
    } catch (error) {
      console.error('Error sending streak reminder:', error);
    }
  }

  // Yerel bildirim göster (Alert ile simüle)
  private static showLocalNotification(title: string, message: string): void {
    // Gerçek uygulamada burada push notification kütüphanesi kullanılır
    // Örnek: PushNotification.localNotification({ title, message })
    
    // Şimdilik Alert ile gösteriyoruz
    setTimeout(() => {
      Alert.alert(title, message, [
        { text: 'Tamam', style: 'default' }
      ]);
    }, 100);
  }

  // Zamanlanmış bildirimleri kaydet
  static async scheduleNotification(notification: PomodoroNotification): Promise<void> {
    try {
      const existing = await AsyncStorage.getItem(this.SCHEDULED_NOTIFICATIONS_KEY);
      const notifications: PomodoroNotification[] = existing ? JSON.parse(existing) : [];
      
      notifications.push(notification);
      
      await AsyncStorage.setItem(this.SCHEDULED_NOTIFICATIONS_KEY, JSON.stringify(notifications));
    } catch (error) {
      console.error('Error scheduling notification:', error);
    }
  }

  // Zamanlanmış bildirimleri temizle
  static async clearScheduledNotifications(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.SCHEDULED_NOTIFICATIONS_KEY);
    } catch (error) {
      console.error('Error clearing scheduled notifications:', error);
    }
  }

  // Test bildirimi gönder
  static async sendTestNotification(): Promise<void> {
    const title = '🧪 Test Bildirimi';
    const message = 'Bildirimler düzgün çalışıyor!';
    this.showLocalNotification(title, message);
  }
}

export default NotificationService;
export type { NotificationSettings, PomodoroNotification };
