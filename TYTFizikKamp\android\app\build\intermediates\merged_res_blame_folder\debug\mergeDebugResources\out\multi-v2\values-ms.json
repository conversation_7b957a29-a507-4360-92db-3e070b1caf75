{"logs": [{"outputFile": "com.tytfizikkamp.app-mergeDebugResources-45:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e932dc8d24e50c2cb3b8fbb16fc5745d\\transformed\\firebase-messaging-21.1.0\\res\\values-ms\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "207", "endColumns": "102", "endOffsets": "309"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5068", "endColumns": "106", "endOffsets": "5170"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d6862beb0c03f816962150b27b444ce5\\transformed\\play-services-basement-17.0.0\\res\\values-ms\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "204", "endOffsets": "451"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4643", "endColumns": "204", "endOffsets": "4843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\625ed137c6a3f5343b71917adedb437c\\transformed\\appcompat-1.7.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "329,440,545,653,740,844,955,1034,1112,1203,1296,1391,1485,1583,1676,1771,1865,1956,2047,2127,2239,2347,2444,2553,2657,2764,2923,10650", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "435,540,648,735,839,950,1029,1107,1198,1291,1386,1480,1578,1671,1766,1860,1951,2042,2122,2234,2342,2439,2548,2652,2759,2918,3019,10726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ec4e31fa8d0cf74d3d6955f9c39b3c31\\transformed\\core-1.16.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "39,40,41,42,43,44,45,137", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3520,3615,3717,3814,3924,4030,4148,11616", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "3610,3712,3809,3919,4025,4143,4258,11712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,204,274,342,424,493,567,643,725,808,885,968,1042,1127,1212,1290,1367,1444,1530,1605,1682,1752", "endColumns": "70,77,69,67,81,68,73,75,81,82,76,82,73,84,84,77,76,76,85,74,76,69,73", "endOffsets": "121,199,269,337,419,488,562,638,720,803,880,963,1037,1122,1207,1285,1362,1439,1525,1600,1677,1747,1821"}, "to": {"startLines": "33,49,55,57,58,60,74,75,122,123,124,129,130,131,132,133,134,135,136,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3024,4565,5175,5310,5378,5519,6513,6587,10408,10490,10573,10971,11054,11128,11213,11298,11376,11453,11530,11717,11792,11869,11939", "endColumns": "70,77,69,67,81,68,73,75,81,82,76,82,73,84,84,77,76,76,85,74,76,69,73", "endOffsets": "3090,4638,5240,5373,5455,5583,6582,6658,10485,10568,10645,11049,11123,11208,11293,11371,11448,11525,11611,11787,11864,11934,12008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3e7df312ad5b5ee0d6592d754d6c3af1\\transformed\\material-1.12.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,279,359,438,525,617,704,807,923,1006,1068,1133,1226,1291,1350,1437,1499,1561,1621,1687,1749,1803,1911,1968,2029,2084,2155,2275,2366,2443,2540,2625,2711,2859,2945,3031,3159,3247,3325,3378,3429,3495,3566,3644,3715,3794,3867,3943,4016,4087,4194,4286,4359,4449,4542,4616,4687,4778,4830,4910,4978,5062,5147,5209,5273,5336,5408,5512,5620,5716,5822,5879,5934,6020,6105,6183", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,78,86,91,86,102,115,82,61,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,76,96,84,85,147,85,85,127,87,77,52,50,65,70,77,70,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85,84,77,76", "endOffsets": "274,354,433,520,612,699,802,918,1001,1063,1128,1221,1286,1345,1432,1494,1556,1616,1682,1744,1798,1906,1963,2024,2079,2150,2270,2361,2438,2535,2620,2706,2854,2940,3026,3154,3242,3320,3373,3424,3490,3561,3639,3710,3789,3862,3938,4011,4082,4189,4281,4354,4444,4537,4611,4682,4773,4825,4905,4973,5057,5142,5204,5268,5331,5403,5507,5615,5711,5817,5874,5929,6015,6100,6178,6255"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,51,52,53,56,59,61,62,63,64,65,66,67,68,69,70,71,72,73,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3095,3175,3254,3341,3433,4263,4366,4482,4848,4910,4975,5245,5460,5588,5675,5737,5799,5859,5925,5987,6041,6149,6206,6267,6322,6393,6663,6754,6831,6928,7013,7099,7247,7333,7419,7547,7635,7713,7766,7817,7883,7954,8032,8103,8182,8255,8331,8404,8475,8582,8674,8747,8837,8930,9004,9075,9166,9218,9298,9366,9450,9535,9597,9661,9724,9796,9900,10008,10104,10210,10267,10322,10731,10816,10894", "endLines": "5,34,35,36,37,38,46,47,48,51,52,53,56,59,61,62,63,64,65,66,67,68,69,70,71,72,73,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,126,127,128", "endColumns": "12,79,78,86,91,86,102,115,82,61,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,76,96,84,85,147,85,85,127,87,77,52,50,65,70,77,70,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85,84,77,76", "endOffsets": "324,3170,3249,3336,3428,3515,4361,4477,4560,4905,4970,5063,5305,5514,5670,5732,5794,5854,5920,5982,6036,6144,6201,6262,6317,6388,6508,6749,6826,6923,7008,7094,7242,7328,7414,7542,7630,7708,7761,7812,7878,7949,8027,8098,8177,8250,8326,8399,8470,8577,8669,8742,8832,8925,8999,9070,9161,9213,9293,9361,9445,9530,9592,9656,9719,9791,9895,10003,10099,10205,10262,10317,10403,10811,10889,10966"}}]}]}