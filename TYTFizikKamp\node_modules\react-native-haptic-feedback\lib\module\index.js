"use strict";

import NativeHapticFeedback from './codegenSpec/NativeHapticFeedback';
import { HapticFeedbackTypes } from "./types";
const defaultOptions = {
  enableVibrateFallback: false,
  ignoreAndroidSystemSettings: false
};
const RNHapticFeedback = {
  trigger(type = HapticFeedbackTypes.selection, options = {}) {
    try {
      NativeHapticFeedback.trigger(type, {
        ...defaultOptions,
        ...options
      });
    } catch {
      console.warn("RNReactNativeHapticFeedback is not available");
    }
  }
};
export * from "./types";
export const {
  trigger
} = RNHapticFeedback;
export default RNHapticFeedback;
//# sourceMappingURL=index.js.map