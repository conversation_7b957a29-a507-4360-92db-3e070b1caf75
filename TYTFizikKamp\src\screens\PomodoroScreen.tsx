import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
  AppState,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NotificationService from '../services/NotificationService';

const { width } = Dimensions.get('window');

interface PomodoroSession {
  id: string;
  startTime: Date;
  endTime?: Date;
  duration: number;
  type: 'work' | 'shortBreak' | 'longBreak';
  completed: boolean;
}

interface PomodoroStats {
  totalSessions: number;
  totalWorkTime: number;
  totalBreakTime: number;
  todaySessions: number;
  currentStreak: number;
  bestStreak: number;
}

const PomodoroScreen = () => {
  // Timer States
  const [timeLeft, setTimeLeft] = useState(1 * 60); // 1 dakika - test için
  const [isActive, setIsActive] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentSession, setCurrentSession] = useState<'work' | 'shortBreak' | 'longBreak'>('work');
  const [sessionCount, setSessionCount] = useState(0);
  
  // Settings
  const [workDuration, setWorkDuration] = useState(1); // Test için kısaltıldı
  const [shortBreakDuration, setShortBreakDuration] = useState(1); // Test için kısaltıldı
  const [longBreakDuration, setLongBreakDuration] = useState(1); // Test için kısaltıldı
  const [longBreakInterval, setLongBreakInterval] = useState(4);
  
  // Stats
  const [stats, setStats] = useState<PomodoroStats>({
    totalSessions: 0,
    totalWorkTime: 0,
    totalBreakTime: 0,
    todaySessions: 0,
    currentStreak: 0,
    bestStreak: 0,
  });
  
  // UI States
  const [showSettings, setShowSettings] = useState(false);
  const [showStats, setShowStats] = useState(false);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const appStateRef = useRef(AppState.currentState);

  useEffect(() => {
    loadPomodoroData();
    
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, []);

  useEffect(() => {
    if (isActive && !isPaused) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(time => {
          if (time <= 1) {
            handleSessionComplete();
            return 0;
          }
          return time - 1;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isActive, isPaused]);

  const handleAppStateChange = (nextAppState: string) => {
    if (appStateRef.current.match(/inactive|background/) && nextAppState === 'active') {
      // Uygulama ön plana geldiğinde timer'ı güncelle
      if (isActive && !isPaused) {
        // Background'da geçen süreyi hesapla ve timer'ı güncelle
        // Bu özellik daha gelişmiş bir implementasyon gerektirir
      }
    }
    appStateRef.current = nextAppState as any;
  };

  const loadPomodoroData = async () => {
    try {
      const savedStats = await AsyncStorage.getItem('pomodoro_stats');
      if (savedStats) {
        setStats(JSON.parse(savedStats));
      }
      
      const savedSettings = await AsyncStorage.getItem('pomodoro_settings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        setWorkDuration(settings.workDuration || 25);
        setShortBreakDuration(settings.shortBreakDuration || 5);
        setLongBreakDuration(settings.longBreakDuration || 15);
        setLongBreakInterval(settings.longBreakInterval || 4);
      }
    } catch (error) {
      console.error('Pomodoro data loading error:', error);
    }
  };

  const savePomodoroData = async (newStats: PomodoroStats) => {
    try {
      await AsyncStorage.setItem('pomodoro_stats', JSON.stringify(newStats));
      setStats(newStats);
    } catch (error) {
      console.error('Pomodoro data saving error:', error);
    }
  };

  const saveSettings = async () => {
    try {
      const settings = {
        workDuration,
        shortBreakDuration,
        longBreakDuration,
        longBreakInterval,
      };
      await AsyncStorage.setItem('pomodoro_settings', JSON.stringify(settings));
    } catch (error) {
      console.error('Settings saving error:', error);
    }
  };

  const handleSessionComplete = () => {
    const newStats = { ...stats };
    
    if (currentSession === 'work') {
      newStats.totalSessions += 1;
      newStats.totalWorkTime += workDuration;
      newStats.todaySessions += 1;
      newStats.currentStreak += 1;
      
      if (newStats.currentStreak > newStats.bestStreak) {
        newStats.bestStreak = newStats.currentStreak;
      }
      
      setSessionCount(prev => prev + 1);
      
      // Sonraki session'ı belirle
      if ((sessionCount + 1) % longBreakInterval === 0) {
        setCurrentSession('longBreak');
        setTimeLeft(longBreakDuration * 60);
      } else {
        setCurrentSession('shortBreak');
        setTimeLeft(shortBreakDuration * 60);
      }
      
      showNotification('🎉 Çalışma Tamamlandı!', 'Harika! Şimdi mola zamanı.');

      // Bildirim gönder
      NotificationService.sendPomodoroNotification('work_complete');
    } else {
      newStats.totalBreakTime += currentSession === 'shortBreak' ? shortBreakDuration : longBreakDuration;

      setCurrentSession('work');
      setTimeLeft(workDuration * 60);

      showNotification('⏰ Mola Bitti!', 'Tekrar çalışma zamanı!');

      // Bildirim gönder
      NotificationService.sendPomodoroNotification('break_complete');
    }
    
    savePomodoroData(newStats);
    setIsActive(false);
    setIsPaused(false);
  };

  const showNotification = (title: string, message: string) => {
    Alert.alert(title, message, [
      { text: 'Devam Et', onPress: () => {} }
    ]);
  };

  const startTimer = () => {
    NotificationService.triggerHapticFeedback('impactLight');
    setIsActive(true);
    setIsPaused(false);
  };

  const pauseTimer = () => {
    NotificationService.triggerHapticFeedback('impactMedium');
    setIsPaused(true);
  };

  const resetTimer = () => {
    NotificationService.triggerHapticFeedback('impactHeavy');
    setIsActive(false);
    setIsPaused(false);

    switch (currentSession) {
      case 'work':
        setTimeLeft(workDuration * 60);
        break;
      case 'shortBreak':
        setTimeLeft(shortBreakDuration * 60);
        break;
      case 'longBreak':
        setTimeLeft(longBreakDuration * 60);
        break;
    }
  };

  const skipSession = () => {
    Alert.alert(
      'Oturumu Atla',
      'Bu oturumu atlamak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Atla', onPress: handleSessionComplete }
      ]
    );
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getSessionColor = () => {
    switch (currentSession) {
      case 'work':
        return ['#6C5CE7', '#A29BFE'];
      case 'shortBreak':
        return ['#00CEFF', '#74B9FF'];
      case 'longBreak':
        return ['#00B894', '#55EFC4'];
      default:
        return ['#6C5CE7', '#A29BFE'];
    }
  };

  const getSessionTitle = () => {
    switch (currentSession) {
      case 'work':
        return 'Çalışma Zamanı';
      case 'shortBreak':
        return 'Kısa Mola';
      case 'longBreak':
        return 'Uzun Mola';
      default:
        return 'Pomodoro';
    }
  };

  const getSessionIcon = () => {
    switch (currentSession) {
      case 'work':
        return 'work';
      case 'shortBreak':
        return 'coffee';
      case 'longBreak':
        return 'spa';
      default:
        return 'timer';
    }
  };

  const progress = currentSession === 'work' 
    ? 1 - (timeLeft / (workDuration * 60))
    : currentSession === 'shortBreak'
    ? 1 - (timeLeft / (shortBreakDuration * 60))
    : 1 - (timeLeft / (longBreakDuration * 60));

  return (
    <LinearGradient colors={getSessionColor()} style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.headerButton}
          onPress={() => setShowStats(!showStats)}
        >
          <MaterialIcons name="bar-chart" size={24} color="white" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Pomodoro Timer</Text>
        
        <TouchableOpacity 
          style={styles.headerButton}
          onPress={() => setShowSettings(!showSettings)}
        >
          <MaterialIcons name="settings" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Session Info */}
      <View style={styles.sessionInfo}>
        <MaterialIcons name={getSessionIcon()} size={32} color="white" />
        <Text style={styles.sessionTitle}>{getSessionTitle()}</Text>
        <Text style={styles.sessionSubtitle}>
          {currentSession === 'work' ? `${sessionCount + 1}. Oturum` : 'Mola Zamanı'}
        </Text>
      </View>

      {/* Timer Circle */}
      <View style={styles.timerContainer}>
        <View style={styles.timerCircle}>
          <View style={[styles.progressCircle, { 
            transform: [{ rotate: `${progress * 360}deg` }] 
          }]} />
          <View style={styles.timerInner}>
            <Text style={styles.timerText}>{formatTime(timeLeft)}</Text>
            <Text style={styles.timerLabel}>
              {isActive ? (isPaused ? 'Duraklatıldı' : 'Çalışıyor') : 'Hazır'}
            </Text>
          </View>
        </View>
      </View>

      {/* Control Buttons */}
      <View style={styles.controlsContainer}>
        {!isActive ? (
          <TouchableOpacity style={styles.primaryButton} onPress={startTimer}>
            <MaterialIcons name="play-arrow" size={32} color="white" />
            <Text style={styles.primaryButtonText}>Başla</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.activeControls}>
            <TouchableOpacity
              style={styles.secondaryButton}
              onPress={isPaused ? startTimer : pauseTimer}
            >
              <MaterialIcons
                name={isPaused ? "play-arrow" : "pause"}
                size={24}
                color="white"
              />
            </TouchableOpacity>

            <TouchableOpacity style={styles.secondaryButton} onPress={resetTimer}>
              <MaterialIcons name="stop" size={24} color="white" />
            </TouchableOpacity>

            <TouchableOpacity style={styles.secondaryButton} onPress={skipSession}>
              <MaterialIcons name="skip-next" size={24} color="white" />
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Quick Stats */}
      <View style={styles.quickStats}>
        <View style={styles.quickStat}>
          <Text style={styles.quickStatNumber}>{stats.todaySessions}</Text>
          <Text style={styles.quickStatLabel}>Bugün</Text>
        </View>
        <View style={styles.quickStat}>
          <Text style={styles.quickStatNumber}>{stats.currentStreak}</Text>
          <Text style={styles.quickStatLabel}>Seri</Text>
        </View>
        <View style={styles.quickStat}>
          <Text style={styles.quickStatNumber}>{Math.floor(stats.totalWorkTime / 60)}h</Text>
          <Text style={styles.quickStatLabel}>Toplam</Text>
        </View>
      </View>

      {/* Stats Modal */}
      {showStats && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>İstatistikler</Text>
              <TouchableOpacity onPress={() => setShowStats(false)}>
                <MaterialIcons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <MaterialIcons name="timer" size={32} color="#6C5CE7" />
                <Text style={styles.statNumber}>{stats.totalSessions}</Text>
                <Text style={styles.statLabel}>Toplam Oturum</Text>
              </View>

              <View style={styles.statCard}>
                <MaterialIcons name="work" size={32} color="#00CEFF" />
                <Text style={styles.statNumber}>{Math.floor(stats.totalWorkTime / 60)}h</Text>
                <Text style={styles.statLabel}>Çalışma Saati</Text>
              </View>

              <View style={styles.statCard}>
                <MaterialIcons name="coffee" size={32} color="#00B894" />
                <Text style={styles.statNumber}>{Math.floor(stats.totalBreakTime / 60)}h</Text>
                <Text style={styles.statLabel}>Mola Saati</Text>
              </View>

              <View style={styles.statCard}>
                <MaterialIcons name="local-fire-department" size={32} color="#FF6B6B" />
                <Text style={styles.statNumber}>{stats.bestStreak}</Text>
                <Text style={styles.statLabel}>En İyi Seri</Text>
              </View>
            </View>
          </View>
        </View>
      )}

      {/* Settings Modal */}
      {showSettings && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Ayarlar</Text>
              <TouchableOpacity onPress={() => setShowSettings(false)}>
                <MaterialIcons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <View style={styles.settingsContainer}>
              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Çalışma Süresi (dakika)</Text>
                <View style={styles.settingControls}>
                  <TouchableOpacity
                    style={styles.settingButton}
                    onPress={() => setWorkDuration(Math.max(1, workDuration - 5))}
                  >
                    <MaterialIcons name="remove" size={20} color="#6C5CE7" />
                  </TouchableOpacity>
                  <Text style={styles.settingValue}>{workDuration}</Text>
                  <TouchableOpacity
                    style={styles.settingButton}
                    onPress={() => setWorkDuration(Math.min(60, workDuration + 5))}
                  >
                    <MaterialIcons name="add" size={20} color="#6C5CE7" />
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Kısa Mola (dakika)</Text>
                <View style={styles.settingControls}>
                  <TouchableOpacity
                    style={styles.settingButton}
                    onPress={() => setShortBreakDuration(Math.max(1, shortBreakDuration - 1))}
                  >
                    <MaterialIcons name="remove" size={20} color="#6C5CE7" />
                  </TouchableOpacity>
                  <Text style={styles.settingValue}>{shortBreakDuration}</Text>
                  <TouchableOpacity
                    style={styles.settingButton}
                    onPress={() => setShortBreakDuration(Math.min(30, shortBreakDuration + 1))}
                  >
                    <MaterialIcons name="add" size={20} color="#6C5CE7" />
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Uzun Mola (dakika)</Text>
                <View style={styles.settingControls}>
                  <TouchableOpacity
                    style={styles.settingButton}
                    onPress={() => setLongBreakDuration(Math.max(5, longBreakDuration - 5))}
                  >
                    <MaterialIcons name="remove" size={20} color="#6C5CE7" />
                  </TouchableOpacity>
                  <Text style={styles.settingValue}>{longBreakDuration}</Text>
                  <TouchableOpacity
                    style={styles.settingButton}
                    onPress={() => setLongBreakDuration(Math.min(60, longBreakDuration + 5))}
                  >
                    <MaterialIcons name="add" size={20} color="#6C5CE7" />
                  </TouchableOpacity>
                </View>
              </View>

              <TouchableOpacity
                style={styles.saveButton}
                onPress={() => {
                  saveSettings();
                  setShowSettings(false);
                  resetTimer(); // Yeni ayarları uygula
                }}
              >
                <Text style={styles.saveButtonText}>Kaydet</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  sessionInfo: {
    alignItems: 'center',
    marginBottom: 40,
  },
  sessionTitle: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 12,
  },
  sessionSubtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    marginTop: 4,
  },
  timerContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  timerCircle: {
    width: 280,
    height: 280,
    borderRadius: 140,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  progressCircle: {
    position: 'absolute',
    width: 280,
    height: 280,
    borderRadius: 140,
    borderWidth: 8,
    borderColor: 'white',
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  timerInner: {
    alignItems: 'center',
  },
  timerText: {
    color: 'white',
    fontSize: 48,
    fontWeight: 'bold',
    fontFamily: 'monospace',
  },
  timerLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    marginTop: 8,
  },
  controlsContainer: {
    paddingHorizontal: 40,
    marginBottom: 30,
  },
  primaryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: 'white',
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  activeControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  secondaryButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 40,
    marginBottom: 20,
  },
  quickStat: {
    alignItems: 'center',
  },
  quickStatNumber: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  quickStatLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    marginTop: 4,
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    width: width - 40,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: (width - 100) / 2,
    backgroundColor: '#F8F9FA',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    textAlign: 'center',
  },
  settingsContainer: {
    paddingVertical: 10,
  },
  settingItem: {
    marginBottom: 20,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  settingControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  settingButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  settingValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginHorizontal: 20,
    minWidth: 40,
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: '#6C5CE7',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default PomodoroScreen;
