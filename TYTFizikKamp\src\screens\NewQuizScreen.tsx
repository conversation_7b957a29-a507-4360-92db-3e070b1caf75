import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  StatusBar,
  Alert,
  ScrollView,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import { QuizScreenProps } from '../../App';
import { Question, Lesson } from '../types';
import StorageService from '../services/StorageService';
import lessonsData from '../data/lessons.json';

const { width, height } = Dimensions.get('window');

interface QuizState {
  currentQuestionIndex: number;
  answers: Record<number, string>;
  correctAnswers: number;
  timeRemaining: number;
  isCompleted: boolean;
  showExplanation: boolean;
  selectedAnswer: string | null;
  isAnswerCorrect: boolean | null;
  canNavigate: boolean; // Sorular arası gezinme için
}

const NewQuizScreen: React.FC<QuizScreenProps> = ({ navigation, route }) => {
  const { dayId, reviewMode = false, reviewQuestions = [], retryMode = false } = route.params;
  
  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [quizState, setQuizState] = useState<QuizState>({
    currentQuestionIndex: 0,
    answers: {},
    correctAnswers: 0,
    timeRemaining: 900, // 15 dakika
    isCompleted: false,
    showExplanation: false,
    selectedAnswer: null,
    isAnswerCorrect: null,
    canNavigate: true, // Başlangıçta gezinme açık
  });

  // Animasyonlar
  const progressAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    initializeQuiz();
  }, [dayId, reviewMode, retryMode]);

  useEffect(() => {
    // Progress bar animasyonu
    const progress = ((quizState.currentQuestionIndex + 1) / questions.length) * 100;
    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [quizState.currentQuestionIndex, questions.length]);

  useEffect(() => {
    // Timer
    if (quizState.timeRemaining > 0 && !quizState.isCompleted) {
      const timer = setTimeout(() => {
        setQuizState(prev => ({ ...prev, timeRemaining: prev.timeRemaining - 1 }));
      }, 1000);
      return () => clearTimeout(timer);
    } else if (quizState.timeRemaining === 0) {
      handleTimeUp();
    }
  }, [quizState.timeRemaining, quizState.isCompleted]);

  const initializeQuiz = async () => {
    try {
      const lessonData = lessonsData.find(l => l.gun === dayId) as Lesson;
      if (!lessonData) {
        Alert.alert('Hata', 'Ders bulunamadı!');
        navigation.goBack();
        return;
      }

      setLesson(lessonData);

      // Soruları belirle
      let quizQuestions: Question[] = [];
      if (reviewMode && reviewQuestions.length > 0) {
        quizQuestions = lessonData.sorular.filter(q => reviewQuestions.includes(q.id));
      } else {
        quizQuestions = lessonData.sorular;
      }

      setQuestions(quizQuestions);

      // Quiz state'i başlat
      if (reviewMode || retryMode) {
        // Review/Retry modunda her zaman temiz başla
        setQuizState({
          currentQuestionIndex: 0,
          answers: {}, // Boş başla - soru durumları sıfırlanır
          correctAnswers: 0,
          timeRemaining: 900,
          isCompleted: false,
          showExplanation: false,
          selectedAnswer: null,
          isAnswerCorrect: null,
          canNavigate: true,
        });
      } else {
        // Normal modda kaydedilmiş progress varsa yükle
        const savedProgress = await StorageService.getQuizProgress(dayId);
        if (savedProgress) {
          setQuizState(prev => ({
            ...prev,
            currentQuestionIndex: savedProgress.currentQuestionIndex || 0,
            answers: savedProgress.answers || {},
            correctAnswers: Object.values(savedProgress.answers || {}).filter((answer, index) => {
              const questionId = Object.keys(savedProgress.answers || {})[index];
              const question = quizQuestions.find(q => q.id === parseInt(questionId));
              return question && answer === question.cevap;
            }).length,
            timeRemaining: savedProgress.timeRemaining || 900,
          }));
        } else {
          // İlk kez çözülüyorsa temiz başla
          setQuizState({
            currentQuestionIndex: 0,
            answers: {},
            correctAnswers: 0,
            timeRemaining: 900,
            isCompleted: false,
            showExplanation: false,
            selectedAnswer: null,
            isAnswerCorrect: null,
            canNavigate: true,
          });
        }
      }
    } catch (error) {
      console.error('Quiz initialization error:', error);
      Alert.alert('Hata', 'Quiz yüklenirken bir hata oluştu.');
      navigation.goBack();
    }
  };

  const handleTimeUp = () => {
    Alert.alert(
      'Süre Doldu!',
      'Quiz süresi doldu. Sonuçlarınızı görebilirsiniz.',
      [{ text: 'Tamam', onPress: completeQuiz }]
    );
  };

  const handleAnswerSelect = async (answer: string) => {
    if (quizState.selectedAnswer || quizState.showExplanation) return;

    const currentQuestion = questions[quizState.currentQuestionIndex];

    // Cevap karşılaştırması: seçenek "A) Şiir yazma" formatında, cevap "A" formatında
    const selectedOptionLetter = answer.charAt(0); // "A) Şiir yazma" -> "A"
    const isCorrect = selectedOptionLetter === currentQuestion.cevap;

    console.log('Answer check:', {
      selectedAnswer: answer,
      selectedLetter: selectedOptionLetter,
      correctAnswer: currentQuestion.cevap,
      isCorrect: isCorrect
    });

    // Animasyon
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    setQuizState(prev => ({
      ...prev,
      selectedAnswer: answer,
      isAnswerCorrect: isCorrect,
      showExplanation: true,
      answers: { ...prev.answers, [currentQuestion.id]: answer },
      correctAnswers: isCorrect ? prev.correctAnswers + 1 : prev.correctAnswers,
      canNavigate: true, // Cevap verildikten sonra gezinme açık
    }));

    // Progress kaydet
    try {
      await StorageService.answerQuestion(dayId, currentQuestion.id, isCorrect, answer);
      await StorageService.saveQuizProgress(dayId, {
        currentQuestionIndex: quizState.currentQuestionIndex,
        answers: { ...quizState.answers, [currentQuestion.id]: answer },
        score: isCorrect ? quizState.correctAnswers + 1 : quizState.correctAnswers,
        timeRemaining: quizState.timeRemaining,
      });
    } catch (error) {
      console.error('Error saving progress:', error);
    }
  };

  const handleNextQuestion = () => {
    if (quizState.currentQuestionIndex < questions.length - 1) {
      // Slide animasyonu
      Animated.timing(slideAnim, {
        toValue: -width,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setQuizState(prev => ({
          ...prev,
          currentQuestionIndex: prev.currentQuestionIndex + 1,
          showExplanation: false,
          selectedAnswer: null,
          isAnswerCorrect: null,
          canNavigate: true,
        }));

        slideAnim.setValue(width);
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start();
      });
    }
  };

  const handlePreviousQuestion = () => {
    if (quizState.currentQuestionIndex > 0) {
      // Slide animasyonu (ters yön)
      Animated.timing(slideAnim, {
        toValue: width,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setQuizState(prev => ({
          ...prev,
          currentQuestionIndex: prev.currentQuestionIndex - 1,
          showExplanation: false,
          selectedAnswer: null,
          isAnswerCorrect: null,
          canNavigate: true,
        }));

        slideAnim.setValue(-width);
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start();
      });
    }
  };

  const goToQuestion = (index: number) => {
    if (index >= 0 && index < questions.length && index !== quizState.currentQuestionIndex) {
      setQuizState(prev => ({
        ...prev,
        currentQuestionIndex: index,
        showExplanation: false,
        selectedAnswer: null,
        isAnswerCorrect: null,
        canNavigate: true,
      }));
    }
  };

  const completeQuiz = async () => {
    try {
      const totalQuestions = questions.length;
      const correctAnswers = quizState.correctAnswers;
      const successRate = (correctAnswers / totalQuestions) * 100;
      const isPassed = successRate >= 90;

      // Yanlış cevaplanan soruları bul
      const wrongQuestionIds = Object.entries(quizState.answers)
        .filter(([questionId, answer]) => {
          const question = questions.find(q => q.id === parseInt(questionId));
          if (!question) return false;
          const selectedLetter = answer.charAt(0); // "A) Şiir yazma" -> "A"
          return selectedLetter !== question.cevap;
        })
        .map(([questionId]) => parseInt(questionId));

      console.log('Wrong questions found:', wrongQuestionIds);

      // Quiz sonucunu kaydet
      await StorageService.completeQuiz(dayId, {
        totalQuestions,
        correctAnswers,
        wrongAnswers: totalQuestions - correctAnswers,
        skippedAnswers: 0,
        successRate,
        isPassed,
        wrongQuestions: wrongQuestionIds,
        timeSpent: 900 - quizState.timeRemaining,
      });

      // Yanlış soruları sonraki günler için kaydet
      if (wrongQuestionIds.length > 0) {
        await StorageService.saveWrongQuestionsForReview(dayId, wrongQuestionIds);
        console.log('Wrong questions saved for review:', wrongQuestionIds);
      }

      // Eğer review mode'da ve başarılıysa günü tamamla
      // SADECE o günün kendi yanlış sorularını çözüyorsa günü tamamla
      if (reviewMode && isPassed) {
        // Çözülen soruların hangi güne ait olduğunu kontrol et
        const isCurrentDayQuestions = questions.every(q => {
          const lesson = lessonsData.find(l => l.gun === dayId);
          return lesson?.sorular.some(ls => ls.id === q.id);
        });

        if (isCurrentDayQuestions) {
          // Bu günün kendi sorularını çözüyor
          const canComplete = await StorageService.canCompleteToday();

          if (canComplete) {
            // Günü tamamla
            await StorageService.completeDay(dayId, correctAnswers, 900 - quizState.timeRemaining);

            // Tebrik mesajı göster
            setTimeout(() => {
              Alert.alert(
                '🎉 Tebrikler!',
                `Gün ${dayId} başarıyla tamamlandı! Yanlış sorularınızı düzelttiniz ve %${successRate.toFixed(1)} başarı oranına ulaştınız. Sonraki gün açıldı!`,
                [
                  {
                    text: 'Harika!',
                    onPress: () => navigation.navigate('Main')
                  }
                ]
              );
            }, 1000);
          } else {
            // Aynı gün içinde ikinci tamamlama girişimi
            setTimeout(() => {
              Alert.alert(
                'Bilgi',
                'Bugün zaten bir eğitim tamamladınız. Aynı gün içinde sadece bir eğitim tamamlanabilir.',
                [{ text: 'Tamam', onPress: () => navigation.goBack() }]
              );
            }, 1000);
          }
        } else {
          // Önceki günlerin sorularını çözüyor - sadece tebrik mesajı
          setTimeout(() => {
            Alert.alert(
              '✅ Tebrikler!',
              `Dün yanlış yaptığınız soruları bugün başarı ile çözdünüz! %${successRate.toFixed(1)} başarı oranına ulaştınız.`,
              [
                {
                  text: 'Harika!',
                  onPress: () => navigation.navigate('DayDetail', { dayId })
                }
              ]
            );
          }, 1000);
        }
      } else {
        // Normal quiz tamamlama - direkt DayDetail'e git
        setTimeout(() => {
          if (wrongQuestionIds.length > 0) {
            // Yanlış sorular varsa DayDetail'e git (yanlışları çöz butonu için)
            navigation.navigate('DayDetail', { dayId });
          } else {
            // Hiç yanlış yoksa ana sayfaya git
            navigation.navigate('Main');
          }
        }, 1000);
      }

      setQuizState(prev => ({ ...prev, isCompleted: true }));
    } catch (error) {
      console.error('Error completing quiz:', error);
      setQuizState(prev => ({ ...prev, isCompleted: true }));
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  if (!lesson || questions.length === 0) {
    return (
      <LinearGradient colors={['#6C5CE7', '#A29BFE']} style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Quiz Yükleniyor...</Text>
        </View>
      </LinearGradient>
    );
  }

  const currentQuestion = questions[quizState.currentQuestionIndex];
  const progress = ((quizState.currentQuestionIndex + 1) / questions.length) * 100;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#6C5CE7" />
      
      {/* Header */}
      <LinearGradient colors={['#6C5CE7', '#A29BFE']} style={styles.header}>
        <View style={styles.headerTop}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <MaterialIcons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          
          <View style={styles.headerCenter}>
            <Text style={styles.headerTitle}>
              {reviewMode ? '🔄 Tekrar' : `Gün ${dayId}`}
            </Text>
            <Text style={styles.headerSubtitle}>
              {reviewMode ? `${questions.length} Yanlış Soru` : lesson.konu}
            </Text>
          </View>
          
          <View style={styles.timerContainer}>
            <MaterialIcons name="timer" size={20} color="white" />
            <Text style={styles.timerText}>{formatTime(quizState.timeRemaining)}</Text>
          </View>
        </View>

        {/* Progress Bar */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <Animated.View
              style={[
                styles.progressFill,
                {
                  width: progressAnim.interpolate({
                    inputRange: [0, 100],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>
          <Text style={styles.progressText}>
            {quizState.currentQuestionIndex + 1} / {questions.length}
          </Text>
        </View>
      </LinearGradient>

      {/* Scrollable Content */}
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Question Content */}
        <Animated.View style={[styles.questionContainer, { transform: [{ translateX: slideAnim }] }]}>
        <Animated.View style={[styles.questionCard, { transform: [{ scale: scaleAnim }] }]}>
          <Text style={styles.questionNumber}>Soru {quizState.currentQuestionIndex + 1}</Text>
          <Text style={styles.questionText}>{currentQuestion.soru}</Text>

          <View style={styles.optionsContainer}>
            {currentQuestion.secenekler.map((option, index) => {
              const optionLetter = String.fromCharCode(65 + index); // A, B, C, D
              const isSelected = quizState.selectedAnswer === option;
              const isCorrect = option.charAt(0) === currentQuestion.cevap; // "A) Şiir yazma" -> "A" === "A"
              const showResult = quizState.showExplanation;

              let optionStyle: any = [styles.optionButton];
              let textStyle: any = [styles.optionText];

              if (showResult) {
                if (isCorrect) {
                  optionStyle = [styles.optionButton, styles.correctOption];
                  textStyle = [styles.optionText, styles.correctOptionText];
                } else if (isSelected && !isCorrect) {
                  optionStyle = [styles.optionButton, styles.wrongOption];
                  textStyle = [styles.optionText, styles.wrongOptionText];
                }
              } else if (isSelected) {
                optionStyle = [styles.optionButton, styles.selectedOption];
                textStyle = [styles.optionText, styles.selectedOptionText];
              }

              return (
                <TouchableOpacity
                  key={index}
                  style={optionStyle}
                  onPress={() => handleAnswerSelect(option)}
                  disabled={quizState.showExplanation}
                >
                  <View style={styles.optionContent}>
                    <View style={[styles.optionLetter, isSelected && styles.selectedOptionLetter]}>
                      <Text style={[styles.optionLetterText, isSelected && styles.selectedOptionLetterText]}>
                        {optionLetter}
                      </Text>
                    </View>
                    <Text style={textStyle}>{option}</Text>
                  </View>

                  {showResult && isCorrect && (
                    <MaterialIcons name="check-circle" size={24} color="#27AE60" />
                  )}
                  {showResult && isSelected && !isCorrect && (
                    <MaterialIcons name="cancel" size={24} color="#E74C3C" />
                  )}
                </TouchableOpacity>
              );
            })}
          </View>

          {/* Explanation Section */}
          {quizState.showExplanation && (
            <View style={styles.explanationContainer}>
              <View style={[
                styles.explanationHeader,
                quizState.isAnswerCorrect ? styles.correctHeader : styles.wrongHeader
              ]}>
                <MaterialIcons
                  name={quizState.isAnswerCorrect ? "check-circle" : "cancel"}
                  size={24}
                  color="white"
                />
                <Text style={styles.explanationHeaderText}>
                  {quizState.isAnswerCorrect ? "Doğru Cevap! 🎉" : "Yanlış Cevap 😔"}
                </Text>
              </View>

              <Text style={styles.explanationText}>{currentQuestion.aciklama}</Text>


            </View>
          )}
        </Animated.View>

        {/* Navigation Buttons */}
        <View style={styles.navigationContainer}>
          {/* Previous Button */}
          <TouchableOpacity
            style={[styles.navButton, quizState.currentQuestionIndex === 0 && styles.navButtonDisabled]}
            onPress={handlePreviousQuestion}
            disabled={quizState.currentQuestionIndex === 0}
          >
            <MaterialIcons name="arrow-back" size={20} color={quizState.currentQuestionIndex === 0 ? "#999" : "#6C5CE7"} />
            <Text style={[styles.navButtonText, quizState.currentQuestionIndex === 0 && styles.navButtonTextDisabled]}>
              Önceki
            </Text>
          </TouchableOpacity>

          {/* Question Numbers */}
          <View style={styles.questionNumbers}>
            {questions.map((_, index) => {
              const question = questions[index];
              const userAnswer = quizState.answers[question.id];
              const isAnswered = !!userAnswer;
              const isActive = index === quizState.currentQuestionIndex;

              // Cevap durumunu kontrol et
              let isCorrect = false;
              if (isAnswered) {
                const selectedLetter = userAnswer.charAt(0); // "A) Option" -> "A"
                isCorrect = selectedLetter === question.cevap;
              }

              // Buton stilini belirle
              let buttonStyle: any[] = [styles.questionNumberButton];
              let textStyle: any[] = [styles.questionNumberText];

              if (isActive) {
                buttonStyle.push(styles.questionNumberActive);
                textStyle.push(styles.questionNumberTextActive);
              } else if (isAnswered) {
                if (isCorrect) {
                  buttonStyle.push(styles.questionNumberCorrect);
                  textStyle.push(styles.questionNumberTextCorrect);
                } else {
                  buttonStyle.push(styles.questionNumberWrong);
                  textStyle.push(styles.questionNumberTextWrong);
                }
              }

              return (
                <TouchableOpacity
                  key={index}
                  style={buttonStyle}
                  onPress={() => goToQuestion(index)}
                >
                  <Text style={textStyle}>
                    {index + 1}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>

          {/* Next Button */}
          <TouchableOpacity
            style={[styles.navButton, quizState.currentQuestionIndex === questions.length - 1 && styles.navButtonDisabled]}
            onPress={handleNextQuestion}
            disabled={quizState.currentQuestionIndex === questions.length - 1}
          >
            <Text style={[styles.navButtonText, quizState.currentQuestionIndex === questions.length - 1 && styles.navButtonTextDisabled]}>
              Sonraki
            </Text>
            <MaterialIcons name="arrow-forward" size={20} color={quizState.currentQuestionIndex === questions.length - 1 ? "#999" : "#6C5CE7"} />
          </TouchableOpacity>
        </View>

        {/* Complete Quiz Button */}
        <View style={styles.completeContainer}>
          <TouchableOpacity
            style={[
              styles.completeQuizButton,
              Object.keys(quizState.answers).length === 0 && styles.completeQuizButtonDisabled
            ]}
            onPress={completeQuiz}
            disabled={Object.keys(quizState.answers).length === 0}
          >
            <Text style={[
              styles.completeQuizButtonText,
              Object.keys(quizState.answers).length === 0 && styles.completeQuizButtonTextDisabled
            ]}>
              Testi Bitir ({Object.keys(quizState.answers).length}/{questions.length})
            </Text>
            <MaterialIcons
              name="assessment"
              size={20}
              color={Object.keys(quizState.answers).length === 0 ? "#999" : "white"}
            />
          </TouchableOpacity>
        </View>
      </Animated.View>
      </ScrollView>

      {/* Results Modal */}
      {quizState.isCompleted && (
        <View style={styles.resultsOverlay}>
          <View style={styles.resultsModal}>
            <LinearGradient
              colors={quizState.correctAnswers / questions.length >= 0.9 ? ['#27AE60', '#2ECC71'] : ['#E74C3C', '#FF6B6B']}
              style={styles.resultsHeader}
            >
              <MaterialIcons
                name={quizState.correctAnswers / questions.length >= 0.9 ? "emoji-events" : "sentiment-dissatisfied"}
                size={48}
                color="white"
              />
              <Text style={styles.resultsTitle}>
                {quizState.correctAnswers / questions.length >= 0.9 ? "Tebrikler! 🎉" : "Tekrar Deneyin! 💪"}
              </Text>
              <Text style={styles.resultsSubtitle}>
                Quiz Tamamlandı
              </Text>
            </LinearGradient>

            <View style={styles.resultsContent}>
              <View style={styles.scoreContainer}>
                <Text style={styles.scoreText}>
                  {quizState.correctAnswers} / {questions.length}
                </Text>
                <Text style={styles.scoreLabel}>Doğru Cevap</Text>

                <View style={styles.percentageContainer}>
                  <Text style={styles.percentageText}>
                    %{Math.round((quizState.correctAnswers / questions.length) * 100)}
                  </Text>
                  <Text style={styles.percentageLabel}>Başarı Oranı</Text>
                </View>
              </View>

              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <MaterialIcons name="check-circle" size={24} color="#27AE60" />
                  <Text style={styles.statText}>{quizState.correctAnswers} Doğru</Text>
                </View>
                <View style={styles.statItem}>
                  <MaterialIcons name="cancel" size={24} color="#E74C3C" />
                  <Text style={styles.statText}>{questions.length - quizState.correctAnswers} Yanlış</Text>
                </View>
                <View style={styles.statItem}>
                  <MaterialIcons name="timer" size={24} color="#6C5CE7" />
                  <Text style={styles.statText}>{formatTime(900 - quizState.timeRemaining)} Süre</Text>
                </View>
              </View>

              <View style={styles.resultsButtons}>
                {quizState.correctAnswers / questions.length < 0.9 && (
                  <TouchableOpacity
                    style={[styles.resultButton, styles.retryButton]}
                    onPress={() => {
                      setQuizState({
                        currentQuestionIndex: 0,
                        answers: {},
                        correctAnswers: 0,
                        timeRemaining: 900,
                        isCompleted: false,
                        showExplanation: false,
                        selectedAnswer: null,
                        isAnswerCorrect: null,
                        canNavigate: true,
                      });
                    }}
                  >
                    <MaterialIcons name="refresh" size={20} color="white" />
                    <Text style={styles.resultButtonText}>Tekrar Dene</Text>
                  </TouchableOpacity>
                )}

                <TouchableOpacity
                  style={[styles.resultButton, styles.homeButton]}
                  onPress={() => navigation.goBack()}
                >
                  <MaterialIcons name="home" size={20} color="white" />
                  <Text style={styles.resultButtonText}>Ana Sayfa</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  header: {
    paddingTop: StatusBar.currentHeight || 40,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginTop: 2,
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  timerText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 3,
    marginRight: 12,
  },
  progressFill: {
    height: '100%',
    backgroundColor: 'white',
    borderRadius: 3,
  },
  progressText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  questionContainer: {
    flex: 1,
    padding: 20,
  },
  questionCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  questionNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6C5CE7',
    marginBottom: 12,
  },
  questionText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#2D3436',
    lineHeight: 26,
    marginBottom: 24,
  },
  optionsContainer: {
    gap: 12,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedOption: {
    backgroundColor: '#E8F4FD',
    borderColor: '#6C5CE7',
  },
  correctOption: {
    backgroundColor: '#E8F5E8',
    borderColor: '#27AE60',
  },
  wrongOption: {
    backgroundColor: '#FFEAEA',
    borderColor: '#E74C3C',
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionLetter: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#DDD',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  selectedOptionLetter: {
    backgroundColor: '#6C5CE7',
  },
  optionLetterText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
  },
  selectedOptionLetterText: {
    color: 'white',
  },
  optionText: {
    fontSize: 16,
    color: '#2D3436',
    flex: 1,
  },
  selectedOptionText: {
    color: '#6C5CE7',
    fontWeight: '500',
  },
  correctOptionText: {
    color: '#27AE60',
    fontWeight: '500',
  },
  wrongOptionText: {
    color: '#E74C3C',
    fontWeight: '500',
  },
  explanationContainer: {
    marginTop: 24,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
    paddingTop: 20,
  },
  explanationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  correctHeader: {
    backgroundColor: '#27AE60',
  },
  wrongHeader: {
    backgroundColor: '#E74C3C',
  },
  explanationHeaderText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  explanationText: {
    fontSize: 15,
    color: '#495057',
    lineHeight: 22,
    marginBottom: 20,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#6C5CE7',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  nextButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  resultsModal: {
    backgroundColor: 'white',
    borderRadius: 20,
    width: '100%',
    maxWidth: 400,
    overflow: 'hidden',
  },
  resultsHeader: {
    padding: 30,
    alignItems: 'center',
  },
  resultsTitle: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 12,
  },
  resultsSubtitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 16,
    marginTop: 4,
  },
  resultsContent: {
    padding: 24,
  },
  scoreContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  scoreText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#2D3436',
  },
  scoreLabel: {
    fontSize: 16,
    color: '#636E72',
    marginTop: 4,
  },
  percentageContainer: {
    alignItems: 'center',
    marginTop: 16,
    padding: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    minWidth: 120,
  },
  percentageText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#6C5CE7',
  },
  percentageLabel: {
    fontSize: 14,
    color: '#636E72',
    marginTop: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
    paddingVertical: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
  },
  statItem: {
    alignItems: 'center',
    gap: 8,
  },
  statText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2D3436',
  },
  resultsButtons: {
    gap: 12,
  },
  resultButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  retryButton: {
    backgroundColor: '#E74C3C',
  },
  homeButton: {
    backgroundColor: '#6C5CE7',
  },
  resultButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  navigationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: 'rgba(255,255,255,0.1)',
    marginHorizontal: 20,
    borderRadius: 15,
    marginBottom: 15,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 10,
    minWidth: 80,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  navButtonDisabled: {
    backgroundColor: 'rgba(255,255,255,0.5)',
  },
  navButtonText: {
    color: '#6C5CE7',
    fontSize: 14,
    fontWeight: '600',
    marginHorizontal: 5,
  },
  navButtonTextDisabled: {
    color: '#999',
  },
  questionNumbers: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    flex: 1,
    marginHorizontal: 10,
  },
  questionNumberButton: {
    width: 35,
    height: 35,
    borderRadius: 17.5,
    backgroundColor: 'rgba(255,255,255,0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 3,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  questionNumberActive: {
    backgroundColor: '#6C5CE7',
    borderColor: '#A29BFE',
  },
  questionNumberCorrect: {
    backgroundColor: '#27AE60',
    borderColor: '#2ECC71',
  },
  questionNumberWrong: {
    backgroundColor: '#E74C3C',
    borderColor: '#FF6B6B',
  },
  questionNumberText: {
    color: '#6C5CE7',
    fontSize: 12,
    fontWeight: '600',
  },
  questionNumberTextActive: {
    color: 'white',
    fontWeight: 'bold',
  },
  questionNumberTextCorrect: {
    color: 'white',
    fontWeight: 'bold',
  },
  questionNumberTextWrong: {
    color: 'white',
    fontWeight: 'bold',
  },
  completeContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  completeQuizButton: {
    backgroundColor: '#27AE60',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    borderRadius: 15,
  },
  completeQuizButtonDisabled: {
    backgroundColor: '#95A5A6',
  },
  completeQuizButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  completeQuizButtonTextDisabled: {
    color: '#999',
  },
});

export default NewQuizScreen;
