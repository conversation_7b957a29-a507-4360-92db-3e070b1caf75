ninja: Entering directory `C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\.cxx\Debug\5o2b6o6q\x86'
[0/2] Re-checking globbed directories...
[1/50] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[2/50] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o
[3/50] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o
[4/50] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[5/50] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[6/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/EventEmitters.cpp.o
[7/50] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[8/50] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[9/50] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[10/50] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
[11/50] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[12/50] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o
[13/50] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[14/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/72f994d6b4d4ae1617cc1e36268935c9/safeareacontext/RNCSafeAreaViewState.cpp.o
[15/50] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o
[16/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/72f994d6b4d4ae1617cc1e36268935c9/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[17/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/ComponentDescriptors.cpp.o
[18/50] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o
[19/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/States.cpp.o
[20/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/safeareacontextJSI-generated.cpp.o
[21/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2925fb9a73caffbf95f70fc9e227ae4e/codegen/jni/safeareacontext-generated.cpp.o
[22/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[23/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[24/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/ShadowNodes.cpp.o
[25/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/62033d5dc1295738601f79d2e301905f/renderer/components/safeareacontext/Props.cpp.o
[26/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/States.cpp.o
[27/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[28/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[29/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[30/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[31/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3bba978b2157660a9a8f04f82fd394ad/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[32/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[33/50] Linking CXX shared library C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\build\intermediates\cxx\Debug\5o2b6o6q\obj\x86\libreact_codegen_safeareacontext.so
[34/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0e8eb62b16777f5f0cc7c33df312f9ed/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[35/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/11247e6eb8d2ed237e1fbab4e82a70d7/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[36/50] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o
[37/50] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o
[38/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[39/50] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o
[40/50] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o
[41/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[42/50] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[43/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
[44/50] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o
[45/50] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o
[46/50] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o
[47/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[48/50] Linking CXX shared library C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\build\intermediates\cxx\Debug\5o2b6o6q\obj\x86\libreact_codegen_rnscreens.so
[49/50] Building CXX object CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[50/50] Linking CXX shared library C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\build\intermediates\cxx\Debug\5o2b6o6q\obj\x86\libappmodules.so
