import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import ProgressBar from './ProgressBar';
import { DailyCardProps } from '../types';

const { width } = Dimensions.get('window');

const DailyCard: React.FC<DailyCardProps> = ({
  day,
  title,
  completed,
  locked,
  progress,
  onPress,
}) => {
  const getCardGradient = () => {
    if (locked) {
      return ['#BDC3C7', '#95A5A6']; // Gri tonları
    } else if (completed) {
      return ['#00CEFF', '#0984E3']; // <PERSON>vi tonları (ta<PERSON>mış)
    } else {
      return ['#6C5CE7', '#A29BFE']; // Mor tonları (aktif)
    }
  };

  const getIconName = () => {
    if (locked) {
      return 'lock';
    } else if (completed) {
      return 'check-circle';
    } else {
      return 'play-circle-filled';
    }
  };

  const getIconColor = () => {
    if (locked) {
      return '#7F8C8D';
    } else if (completed) {
      return '#27AE60';
    } else {
      return '#FFFFFF';
    }
  };

  const handlePress = () => {
    if (!locked) {
      onPress();
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, locked && styles.lockedContainer]}
      onPress={handlePress}
      disabled={locked}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={getCardGradient()}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.content}>
          {/* Sol taraf - Gün numarası */}
          <View style={styles.leftSection}>
            <View style={styles.dayCircle}>
              <Text style={styles.dayNumber}>{day}</Text>
            </View>
          </View>

          {/* Orta kısım - İçerik */}
          <View style={styles.middleSection}>
            <Text style={[styles.title, locked && styles.lockedText]}>
              {title}
            </Text>
            
            {!locked && (
              <View style={styles.progressContainer}>
                <ProgressBar
                  progress={progress}
                  color="rgba(255,255,255,0.9)"
                  backgroundColor="rgba(255,255,255,0.3)"
                  height={6}
                />
                <Text style={styles.progressText}>
                  {completed ? 'Tamamlandı' : 'Devam et'}
                </Text>
              </View>
            )}

            {locked && (
              <Text style={styles.lockedMessage}>
                Önceki günü tamamla
              </Text>
            )}
          </View>

          {/* Sağ taraf - İkon */}
          <View style={styles.rightSection}>
            <MaterialIcons
              name={getIconName()}
              size={32}
              color={getIconColor()}
            />
          </View>
        </View>

        {/* Alt kısım - Durum çubuğu */}
        {!locked && (
          <View style={styles.statusBar}>
            <View style={styles.statusIndicator}>
              <View style={[
                styles.statusDot,
                completed ? styles.completedDot : styles.activeDot
              ]} />
              <Text style={styles.statusText}>
                {completed ? 'Tamamlandı' : 'Aktif'}
              </Text>
            </View>
          </View>
        )}
      </LinearGradient>

      {/* Kilit overlay */}
      {locked && (
        <View style={styles.lockOverlay}>
          <MaterialIcons name="lock" size={24} color="rgba(255,255,255,0.8)" />
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 15,
    borderRadius: 15,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  lockedContainer: {
    opacity: 0.7,
  },
  gradient: {
    borderRadius: 15,
    overflow: 'hidden',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    minHeight: 80,
  },
  leftSection: {
    marginRight: 15,
  },
  dayCircle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  dayNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  middleSection: {
    flex: 1,
    marginRight: 15,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginBottom: 8,
  },
  lockedText: {
    color: 'rgba(255,255,255,0.7)',
  },
  progressContainer: {
    marginTop: 5,
  },
  progressText: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 5,
  },
  lockedMessage: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.6)',
    fontStyle: 'italic',
  },
  rightSection: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusBar: {
    paddingHorizontal: 20,
    paddingBottom: 15,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  completedDot: {
    backgroundColor: '#27AE60',
  },
  activeDot: {
    backgroundColor: '#F39C12',
  },
  statusText: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    fontWeight: '500',
  },
  lockOverlay: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderRadius: 15,
    padding: 5,
  },
});

export default DailyCard;
