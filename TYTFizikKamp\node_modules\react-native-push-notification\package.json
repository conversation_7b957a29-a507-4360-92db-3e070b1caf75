{"name": "react-native-push-notification", "version": "8.1.1", "description": "React Native Local and Remote Notifications", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["react-component", "react-native", "ios", "android", "notifications", "push", "apns", "firebase"], "bugs": {"url": "https://github.com/zo0r/react-native-push-notification/issues"}, "repository": {"type": "git", "url": "git+ssh://**************:zo0r/react-native-push-notification.git"}, "peerDependencies": {"@react-native-community/push-notification-ios": "^1.10.1", "react-native": ">=0.33"}, "author": "zo0r <http://zo0r.me>", "license": "MIT"}