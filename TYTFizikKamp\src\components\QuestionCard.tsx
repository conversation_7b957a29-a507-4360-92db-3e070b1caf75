import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import { QuestionCardProps } from '../types';

const { width } = Dimensions.get('window');

const QuestionCard: React.FC<QuestionCardProps> = ({
  question,
  onAnswer,
  disabled = false,
}) => {
  const [selectedOption, setSelectedOption] = useState<string>('');
  const [isAnswered, setIsAnswered] = useState(false);
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Soru değiştiğinde state'leri sıfırla
    setSelectedOption('');
    setIsAnswered(false);

    // Kart animasyonu
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, [question.id]);

  const handleOptionPress = (option: string) => {
    if (disabled || isAnswered) return;

    setSelectedOption(option);
    setIsAnswered(true);

    // Cevap kontrolü
    const optionLetter = option.charAt(0); // A, B, C, D
    const isCorrect = optionLetter === question.cevap;

    // Kısa bir gecikme ile cevabı gönder
    setTimeout(() => {
      onAnswer(question.id, optionLetter, isCorrect);
    }, 300);
  };

  const getOptionStyle = (option: string) => {
    const optionLetter = option.charAt(0);

    // Eğer disabled ise veya henüz cevap verilmemişse normal stil
    if (disabled || !isAnswered) {
      return styles.option;
    }

    if (optionLetter === selectedOption) {
      return optionLetter === question.cevap
        ? [styles.option, styles.correctOption]
        : [styles.option, styles.wrongOption];
    }

    if (optionLetter === question.cevap) {
      return [styles.option, styles.correctOption];
    }

    return [styles.option, styles.disabledOption];
  };

  const getOptionIcon = (option: string) => {
    const optionLetter = option.charAt(0);
    
    if (!isAnswered) {
      return null;
    }

    if (optionLetter === question.cevap) {
      return <MaterialIcons name="check-circle" size={24} color="#27AE60" />;
    }

    if (optionLetter === selectedOption && optionLetter !== question.cevap) {
      return <MaterialIcons name="cancel" size={24} color="#E74C3C" />;
    }

    return null;
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ scale: scaleAnim }],
          opacity: fadeAnim,
        },
      ]}
    >
      <View style={styles.card}>
        {/* Soru Numarası */}
        <View style={styles.questionHeader}>
          <View style={styles.questionNumber}>
            <Text style={styles.questionNumberText}>{question.id}</Text>
          </View>
          <View style={styles.questionTypeIndicator}>
            <MaterialIcons name="quiz" size={20} color="#6C5CE7" />
            <Text style={styles.questionTypeText}>Çoktan Seçmeli</Text>
          </View>
        </View>

        {/* Soru Metni */}
        <View style={styles.questionTextContainer}>
          <Text style={styles.questionText}>{question.soru}</Text>
        </View>

        {/* Seçenekler */}
        <View style={styles.optionsContainer}>
          {question.secenekler.map((option, index) => (
            <TouchableOpacity
              key={index}
              style={getOptionStyle(option)}
              onPress={() => handleOptionPress(option)}
              disabled={disabled || isAnswered}
              activeOpacity={0.8}
            >
              <View style={styles.optionContent}>
                <View style={styles.optionLeft}>
                  <View style={styles.optionLetter}>
                    <Text style={styles.optionLetterText}>
                      {option.charAt(0)}
                    </Text>
                  </View>
                  <Text style={styles.optionText}>
                    {option.substring(3)} {/* "A) " kısmını çıkar */}
                  </Text>
                </View>
                
                <View style={styles.optionRight}>
                  {getOptionIcon(option)}
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Alt Bilgi */}
        <View style={styles.footer}>
          <View style={styles.footerLeft}>
            <MaterialIcons name="help-outline" size={16} color="#7F8C8D" />
            <Text style={styles.footerText}>Bir seçenek işaretle</Text>
          </View>
          
          {isAnswered && (
            <View style={styles.footerRight}>
              <MaterialIcons name="check" size={16} color="#27AE60" />
              <Text style={styles.answeredText}>Cevaplandı</Text>
            </View>
          )}
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
  },
  card: {
    backgroundColor: 'rgba(255,255,255,0.95)',
    borderRadius: 20,
    padding: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  questionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  questionNumber: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#6C5CE7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  questionNumberText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  questionTypeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  questionTypeText: {
    color: '#6C5CE7',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  questionTextContainer: {
    marginBottom: 25,
    paddingHorizontal: 10,
  },
  questionText: {
    fontSize: 18,
    color: '#2C3E50',
    lineHeight: 26,
    fontWeight: '500',
    textAlign: 'left',
  },
  optionsContainer: {
    marginBottom: 20,
  },
  option: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  correctOption: {
    backgroundColor: '#D4EDDA',
    borderColor: '#27AE60',
  },
  wrongOption: {
    backgroundColor: '#F8D7DA',
    borderColor: '#E74C3C',
  },
  disabledOption: {
    opacity: 0.6,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionLetter: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#6C5CE7',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  optionLetterText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  optionText: {
    fontSize: 16,
    color: '#2C3E50',
    flex: 1,
    lineHeight: 22,
  },
  optionRight: {
    marginLeft: 10,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
  footerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footerText: {
    color: '#7F8C8D',
    fontSize: 12,
    marginLeft: 4,
  },
  footerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  answeredText: {
    color: '#27AE60',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
});

export default QuestionCard;
