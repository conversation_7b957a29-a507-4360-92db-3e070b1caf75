import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface BackReferenceCardProps {
  question: string;
  answer: string;
  hint: string;
}

const BackReferenceCard: React.FC<BackReferenceCardProps> = ({
  question,
  answer,
  hint,
}) => {
  const [isRevealed, setIsRevealed] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));

  const handleReveal = () => {
    setIsRevealed(true);
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  const toggleHint = () => {
    setShowHint(!showHint);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <MaterialIcons name="history" size={24} color="#6C5CE7" />
        <Text style={styles.headerText}>Dünkü Konuyu Hatırla</Text>
      </View>

      <View style={styles.questionContainer}>
        <Text style={styles.questionText}>{question}</Text>
      </View>

      {!isRevealed ? (
        <View style={styles.actionContainer}>
          <TouchableOpacity
            style={styles.hintButton}
            onPress={toggleHint}
          >
            <MaterialIcons name="lightbulb-outline" size={20} color="#F39C12" />
            <Text style={styles.hintButtonText}>İpucu</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.revealButton}
            onPress={handleReveal}
          >
            <MaterialIcons name="visibility" size={20} color="white" />
            <Text style={styles.revealButtonText}>Cevabı Göster</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <Animated.View
          style={[styles.answerContainer, { opacity: fadeAnim }]}
        >
          <View style={styles.answerHeader}>
            <MaterialIcons name="check-circle" size={20} color="#27AE60" />
            <Text style={styles.answerLabel}>Cevap:</Text>
          </View>
          <Text style={styles.answerText}>{answer}</Text>
        </Animated.View>
      )}

      {showHint && (
        <View style={styles.hintContainer}>
          <View style={styles.hintHeader}>
            <MaterialIcons name="lightbulb" size={18} color="#F39C12" />
            <Text style={styles.hintLabel}>İpucu:</Text>
          </View>
          <Text style={styles.hintText}>{hint}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(255,255,255,0.95)',
    borderRadius: 15,
    padding: 20,
    marginHorizontal: 20,
    marginBottom: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  headerText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6C5CE7',
    marginLeft: 8,
  },
  questionContainer: {
    backgroundColor: '#F8F9FA',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    borderLeftWidth: 4,
    borderLeftColor: '#6C5CE7',
  },
  questionText: {
    fontSize: 16,
    color: '#2C3E50',
    fontWeight: '500',
    lineHeight: 22,
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  hintButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3CD',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#F39C12',
  },
  hintButtonText: {
    color: '#F39C12',
    fontWeight: '600',
    marginLeft: 6,
    fontSize: 14,
  },
  revealButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#6C5CE7',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
  },
  revealButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 6,
    fontSize: 14,
  },
  answerContainer: {
    backgroundColor: '#D4EDDA',
    borderRadius: 10,
    padding: 15,
    borderLeftWidth: 4,
    borderLeftColor: '#27AE60',
  },
  answerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  answerLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#27AE60',
    marginLeft: 6,
  },
  answerText: {
    fontSize: 16,
    color: '#155724',
    fontWeight: '500',
    lineHeight: 22,
  },
  hintContainer: {
    backgroundColor: '#FFF3CD',
    borderRadius: 10,
    padding: 15,
    marginTop: 15,
    borderLeftWidth: 4,
    borderLeftColor: '#F39C12',
  },
  hintHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  hintLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#F39C12',
    marginLeft: 6,
  },
  hintText: {
    fontSize: 14,
    color: '#856404',
    lineHeight: 20,
    fontStyle: 'italic',
  },
});

export default BackReferenceCard;
