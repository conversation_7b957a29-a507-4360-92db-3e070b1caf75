import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  RefreshControl,
  Alert,
  Dimensions,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';

import DailyCard from '../components/DailyCard';
import ProgressBar from '../components/ProgressBar';
import MotivationCard from '../components/MotivationCard';
import StorageService, { UserProgress } from '../services/StorageService';
import TimeValidationService from '../services/TimeValidationService';
import lessonsData from '../data/lessons.json';
import { Lesson } from '../types';

const { width } = Dimensions.get('window');

interface HomeScreenProps {
  navigation: any;
}

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const [userProgress, setUserProgress] = useState<UserProgress | null>(null);
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  // Veri yükleme
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Kullanıcı ilerlemesini yükle
      const progress = await StorageService.getUserProgress();
      setUserProgress(progress);

      // Ders verilerini yükle ve kilitleme durumlarını güncelle
      const updatedLessons = await Promise.all(
        lessonsData.map(async (lesson: any) => {
          const accessCheck = await TimeValidationService.canAccessDay(lesson.gun);
          return {
            ...lesson,
            isUnlocked: accessCheck.canAccess,
            isCompleted: progress.completed_days.includes(lesson.gun)
          };
        })
      );

      setLessons(updatedLessons);
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // Sayfa odaklandığında veri yenile
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [])
  );

  // Pull-to-refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Günlük karta tıklama
  const handleDayPress = async (dayId: number) => {
    try {
      const accessCheck = await TimeValidationService.canAccessDay(dayId);
      
      if (!accessCheck.canAccess) {
        let message = accessCheck.reason || 'Bu güne erişemezsiniz.';
        
        if (accessCheck.nextAvailableTime) {
          const remainingTime = TimeValidationService.calculateRemainingTime(
            accessCheck.nextAvailableTime
          );
          message += `\nKalan süre: ${remainingTime}`;
        }
        
        Alert.alert('Erişim Engellendi', message);
        return;
      }

      // Güne erişim izni varsa detay sayfasına git
      navigation.navigate('DayDetail', { dayId });
    } catch (error) {
      console.error('Error handling day press:', error);
      Alert.alert('Hata', 'Bir hata oluştu.');
    }
  };

  // İlerleme yüzdesi hesapla
  const calculateProgress = (): number => {
    if (!userProgress) return 0;
    return (userProgress.completed_days.length / 14) * 100;
  };

  // Motivasyon mesajı seç
  const getMotivationMessage = (): string => {
    if (!userProgress) return 'Başlamaya hazır mısın? 🚀';
    
    const completedCount = userProgress.completed_days.length;
    
    if (completedCount === 0) {
      return 'İlk adımı atmaya hazır mısın? 🌟';
    } else if (completedCount < 5) {
      return `Harika gidiyorsun! ${completedCount}/14 gün tamamlandı 💪`;
    } else if (completedCount < 10) {
      return `Yarı yoldasın! Devam et! ${completedCount}/14 🔥`;
    } else if (completedCount < 14) {
      return `Neredeyse bitirdin! Son spurt! ${completedCount}/14 ⚡`;
    } else {
      return 'Tebrikler! Kampı tamamladın! 🎉';
    }
  };

  if (loading) {
    return (
      <LinearGradient
        colors={['#6C5CE7', '#A29BFE']}
        style={styles.loadingContainer}
      >
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={['#6C5CE7', '#A29BFE', '#F5F6FA']}
      style={styles.container}
    >
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>TYT Fizik Kampı</Text>
          <Text style={styles.subtitle}>14 Günde Fizik Ustası Ol!</Text>
        </View>

        {/* Progress Section */}
        <View style={styles.progressSection}>
          <Text style={styles.progressTitle}>Genel İlerleme</Text>
          <ProgressBar
            progress={calculateProgress()}
            color="#00CEFF"
            backgroundColor="rgba(255,255,255,0.3)"
            height={12}
          />
          <Text style={styles.progressText}>
            {userProgress?.completed_days.length || 0}/14 gün tamamlandı
          </Text>
        </View>

        {/* Motivation Card */}
        <MotivationCard
          message={getMotivationMessage()}
          streak={userProgress?.current_streak || 0}
          totalQuestions={userProgress?.total_questions_solved || 0}
        />

        {/* Daily Cards */}
        <View style={styles.daysContainer}>
          <Text style={styles.daysTitle}>Günlük Dersler</Text>
          
          {lessons.map((lesson) => (
            <DailyCard
              key={lesson.id}
              day={lesson.gun}
              title={lesson.konu}
              completed={lesson.isCompleted}
              locked={!lesson.isUnlocked}
              progress={lesson.isCompleted ? 100 : 0}
              onPress={() => handleDayPress(lesson.gun)}
            />
          ))}
        </View>

        {/* Stats Section */}
        {userProgress && (
          <View style={styles.statsSection}>
            <Text style={styles.statsTitle}>İstatistikler</Text>
            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <Text style={styles.statNumber}>{userProgress.current_streak}</Text>
                <Text style={styles.statLabel}>Güncel Seri</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statNumber}>{userProgress.best_streak}</Text>
                <Text style={styles.statLabel}>En İyi Seri</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statNumber}>{userProgress.total_questions_solved}</Text>
                <Text style={styles.statLabel}>Çözülen Soru</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statNumber}>{userProgress.badges_earned.length}</Text>
                <Text style={styles.statLabel}>Rozet</Text>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.9)',
    textAlign: 'center',
  },
  progressSection: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 15,
    padding: 20,
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    marginBottom: 15,
    textAlign: 'center',
  },
  progressText: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    textAlign: 'center',
    marginTop: 10,
  },
  daysContainer: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  daysTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 15,
  },
  statsSection: {
    marginHorizontal: 20,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 15,
    padding: 20,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    marginBottom: 15,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: (width - 60) / 2 - 10,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginBottom: 10,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#00CEFF',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    textAlign: 'center',
  },
});

export default HomeScreen;
