-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\node_modules\@react-native-async-storage\async-storage\android\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\node_modules\@react-native-async-storage\async-storage\android\src\main\AndroidManifest.xml:2:1-5:12
	package
		ADDED from C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\node_modules\@react-native-async-storage\async-storage\android\src\main\AndroidManifest.xml:3:11-58
		INJECTED from C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\node_modules\@react-native-async-storage\async-storage\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\node_modules\@react-native-async-storage\async-storage\android\src\main\AndroidManifest.xml:2:11-69
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\node_modules\@react-native-async-storage\async-storage\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\node_modules\@react-native-async-storage\async-storage\android\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\node_modules\@react-native-async-storage\async-storage\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\node_modules\@react-native-async-storage\async-storage\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\node_modules\@react-native-async-storage\async-storage\android\src\main\AndroidManifest.xml
