﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 14
VisualStudioVersion = 14.0.25420.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RNSoundModule", "RNSoundModule\RNSoundModule.csproj", "{7B8A9860-75EC-434D-AC70-C6135600C0C7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ReactNative", "..\..\node_modules\react-native-windows\ReactWindows\ReactNative\ReactNative.csproj", "{C7673AD5-E3AA-468C-A5FD-FA38154E205C}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ChakraBridge", "..\..\node_modules\react-native-windows\ReactWindows\ChakraBridge\ChakraBridge.vcxproj", "{4B72C796-16D5-4E3A-81C0-3E36F531E578}"
EndProject
Global
	GlobalSection(SharedMSBuildProjectFiles) = preSolution
		..\..\node_modules\react-native-windows\ReactWindows\ReactNative.Shared\ReactNative.Shared.projitems*{c7673ad5-e3aa-468c-a5fd-fa38154e205c}*SharedItemsImports = 4
	EndGlobalSection
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|ARM = Debug|ARM
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|ARM = Release|ARM
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Debug|Any CPU.ActiveCfg = Debug|ARM
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Debug|Any CPU.Build.0 = Debug|ARM
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Debug|ARM.ActiveCfg = Debug|ARM
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Debug|ARM.Build.0 = Debug|ARM
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Debug|x64.ActiveCfg = Debug|x64
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Debug|x64.Build.0 = Debug|x64
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Debug|x86.ActiveCfg = Debug|x86
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Debug|x86.Build.0 = Debug|x86
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Release|Any CPU.Build.0 = Release|Any CPU
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Release|ARM.ActiveCfg = Release|ARM
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Release|ARM.Build.0 = Release|ARM
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Release|x64.ActiveCfg = Release|x64
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Release|x64.Build.0 = Release|x64
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Release|x86.ActiveCfg = Release|x86
		{7B8A9860-75EC-434D-AC70-C6135600C0C7}.Release|x86.Build.0 = Release|x86
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Debug|Any CPU.ActiveCfg = Debug|ARM
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Debug|ARM.ActiveCfg = Debug|ARM
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Debug|ARM.Build.0 = Debug|ARM
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Debug|x64.ActiveCfg = Debug|x64
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Debug|x64.Build.0 = Debug|x64
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Debug|x86.ActiveCfg = Debug|x86
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Debug|x86.Build.0 = Debug|x86
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Release|Any CPU.ActiveCfg = Release|x86
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Release|ARM.ActiveCfg = Release|ARM
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Release|ARM.Build.0 = Release|ARM
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Release|x64.ActiveCfg = Release|x64
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Release|x64.Build.0 = Release|x64
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Release|x86.ActiveCfg = Release|x86
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Release|x86.Build.0 = Release|x86
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Debug|Any CPU.ActiveCfg = Debug|ARM
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Debug|ARM.ActiveCfg = Debug|ARM
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Debug|ARM.Build.0 = Debug|ARM
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Debug|x64.ActiveCfg = Debug|x64
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Debug|x64.Build.0 = Debug|x64
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Debug|x86.ActiveCfg = Debug|Win32
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Debug|x86.Build.0 = Debug|Win32
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Release|Any CPU.ActiveCfg = Release|Win32
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Release|ARM.ActiveCfg = Release|ARM
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Release|ARM.Build.0 = Release|ARM
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Release|x64.ActiveCfg = Release|x64
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Release|x64.Build.0 = Release|x64
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Release|x86.ActiveCfg = Release|Win32
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Release|x86.Build.0 = Release|Win32
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
