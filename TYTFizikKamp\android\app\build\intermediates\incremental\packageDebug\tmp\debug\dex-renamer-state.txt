#Mon Jul 07 19:25:12 TRT 2025
path.4=15/classes.dex
path.3=14/classes.dex
path.2=12/classes.dex
path.1=10/classes.dex
path.8=5/classes.dex
path.7=3/classes.dex
path.6=2/classes.dex
path.5=1/classes.dex
path.0=classes.dex
base.4=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\15\\classes.dex
base.3=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\14\\classes.dex
base.2=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\12\\classes.dex
base.1=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\10\\classes.dex
base.0=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
path.9=6/classes.dex
base.9=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\6\\classes.dex
base.8=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\5\\classes.dex
base.7=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\3\\classes.dex
base.6=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\2\\classes.dex
base.5=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\1\\classes.dex
renamed.9=classes10.dex
renamed.8=classes9.dex
renamed.15=classes16.dex
renamed.14=classes15.dex
renamed.13=classes14.dex
renamed.12=classes13.dex
renamed.11=classes12.dex
renamed.10=classes11.dex
base.15=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
base.14=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\2\\classes.dex
renamed.3=classes4.dex
path.12=0/classes.dex
renamed.2=classes3.dex
path.13=14/classes.dex
renamed.1=classes2.dex
path.10=7/classes.dex
renamed.0=classes.dex
path.11=9/classes.dex
renamed.7=classes8.dex
renamed.6=classes7.dex
renamed.5=classes6.dex
path.14=2/classes.dex
renamed.4=classes5.dex
path.15=classes2.dex
base.13=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\14\\classes.dex
base.12=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.11=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\9\\classes.dex
base.10=C\:\\Users\\Admin\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\7\\classes.dex
