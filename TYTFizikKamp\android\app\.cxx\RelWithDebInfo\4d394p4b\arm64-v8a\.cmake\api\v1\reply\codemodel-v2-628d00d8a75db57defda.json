{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-RelWithDebInfo-3e09e0ca1fe2b703b71d.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-RelWithDebInfo-7a81399c9ede693dc485.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "RNHapticFeedbackSpec_autolinked_build", "jsonFile": "directory-RNHapticFeedbackSpec_autolinked_build-RelWithDebInfo-aebed6db4a187e68a65e.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-RelWithDebInfo-1cae503c636aeeca2c2f.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [6]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-RelWithDebInfo-3c64789e63dd758249c0.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [5]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-RelWithDebInfo-eedf7f2828bc8424009d.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [2]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-RelWithDebInfo-1486b1629c9af82bffd6.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_RNHapticFeedbackSpec::@a83561f277f6afbb326c", "jsonFile": "target-react_codegen_RNHapticFeedbackSpec-RelWithDebInfo-cdea388aeeac6241de4e.json", "name": "react_codegen_RNHapticFeedbackSpec", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-RelWithDebInfo-6fe5ce71f094c35b838b.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-RelWithDebInfo-79976d86c8462568b1cf.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-RelWithDebInfo-af929c8afcc489ec0aa3.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-RelWithDebInfo-96753f098da4f2b756d1.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-RelWithDebInfo-a91ff9ad3e1a806810c9.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/RelWithDebInfo/4d394p4b/arm64-v8a", "source": "C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}