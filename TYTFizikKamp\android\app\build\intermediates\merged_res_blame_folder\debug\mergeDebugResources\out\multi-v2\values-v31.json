{"logs": [{"outputFile": "com.tytfizikkamp.app-mergeDebugResources-45:/values-v31/values-v31.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3e7df312ad5b5ee0d6592d754d6c3af1\\transformed\\material-1.12.0\\res\\values-v31\\values-v31.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,277,342,408", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,149,243,336,430,524,618,712,806,900,994,1088,1181,1274,1376,1478,1579,1681,1783,1885,1987,2089,2191,2293,2395,2496,2597,2690,2783,2875,2968,3061,3154,3247,3340,3433,3526,3619,3711,3803,3898,3993,4087,4182,4277,4372,4467,4562,4657,4752,4847,4941,5035,5129,5223,5316,5410,5504,5598,5692,5786,5880,5974,6068,6161,6254,6364,6475,6583,6691,6797,6900,7013,7120,7237,7340,7459,7564,7679,7787,7903,8003,8113,8217,8331,8438,8553,8671,8794,8920,9042,9166,9277,9393,9495,9607,9719,9831,9940,10049,10156,10261,10375,10484,10602,10706,10826,10933,11049,11158,11275,11376,11487,11592,11707,11816,11932,12051,12175,12302,12425,12552,12665,12782,12885,12998,13102,13214,13322,13438,13544,13658,13759,13864,13969,14078,14181,14288,14379,14490,14587,14704,14797,14910,15009,15128,15219,15312,15407,15500,15593,15686,15779,15872,15965,16058,16151,16244,16337,16444,16553,16664,16773,16882,16991,17100,17209,17318,17427,17536,17645,17754,17845,17938,18033,18126,18219,18312,18405,18498,18591,18684,18777,18870,18963,19058,19155,19254,19351,19448,19545,19642,19739,19836,19933,20030,20127,20224,20317,20412,20509,20604,20699,20794,20889,20984,21079,21174,21269,21364,21459,27071,32713,38445", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,276,341,407,473", "endColumns": "93,93,92,93,93,93,93,93,93,93,93,92,92,101,101,100,101,101,101,101,101,101,101,101,100,100,92,92,91,92,92,92,92,92,92,92,92,91,91,94,94,93,94,94,94,94,94,94,94,94,93,93,93,93,92,93,93,93,93,93,93,93,93,92,92,109,110,107,107,105,102,112,106,116,102,118,104,114,107,115,99,109,103,113,106,114,117,122,125,121,123,110,115,101,111,111,111,108,108,106,104,113,108,117,103,119,106,115,108,116,100,110,104,114,108,115,118,123,126,122,126,112,116,102,112,103,111,107,115,105,113,100,104,104,108,102,106,90,110,96,116,92,112,98,118,90,92,94,92,92,92,92,92,92,92,92,92,92,106,108,110,108,108,108,108,108,108,108,108,108,108,90,92,94,92,92,92,92,92,92,92,92,92,92,94,96,98,96,96,96,96,96,96,96,96,96,96,92,94,96,94,94,94,94,94,94,94,94,94,94,10,10,10,10", "endOffsets": "144,238,331,425,519,613,707,801,895,989,1083,1176,1269,1371,1473,1574,1676,1778,1880,1982,2084,2186,2288,2390,2491,2592,2685,2778,2870,2963,3056,3149,3242,3335,3428,3521,3614,3706,3798,3893,3988,4082,4177,4272,4367,4462,4557,4652,4747,4842,4936,5030,5124,5218,5311,5405,5499,5593,5687,5781,5875,5969,6063,6156,6249,6359,6470,6578,6686,6792,6895,7008,7115,7232,7335,7454,7559,7674,7782,7898,7998,8108,8212,8326,8433,8548,8666,8789,8915,9037,9161,9272,9388,9490,9602,9714,9826,9935,10044,10151,10256,10370,10479,10597,10701,10821,10928,11044,11153,11270,11371,11482,11587,11702,11811,11927,12046,12170,12297,12420,12547,12660,12777,12880,12993,13097,13209,13317,13433,13539,13653,13754,13859,13964,14073,14176,14283,14374,14485,14582,14699,14792,14905,15004,15123,15214,15307,15402,15495,15588,15681,15774,15867,15960,16053,16146,16239,16332,16439,16548,16659,16768,16877,16986,17095,17204,17313,17422,17531,17640,17749,17840,17933,18028,18121,18214,18307,18400,18493,18586,18679,18772,18865,18958,19053,19150,19249,19346,19443,19540,19637,19734,19831,19928,20025,20122,20219,20312,20407,20504,20599,20694,20789,20884,20979,21074,21169,21264,21359,21454,27066,32708,38440,44202"}}]}]}