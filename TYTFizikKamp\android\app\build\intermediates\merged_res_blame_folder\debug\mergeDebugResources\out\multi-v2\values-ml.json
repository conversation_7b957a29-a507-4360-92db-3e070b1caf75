{"logs": [{"outputFile": "com.tytfizikkamp.app-mergeDebugResources-41:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\625ed137c6a3f5343b71917adedb437c\\transformed\\appcompat-1.7.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,428,534,645,736,841,963,1041,1116,1207,1300,1401,1495,1595,1689,1784,1883,1974,2065,2147,2256,2360,2459,2571,2683,2804,2969,10823", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "423,529,640,731,836,958,1036,1111,1202,1295,1396,1490,1590,1684,1779,1878,1969,2060,2142,2251,2355,2454,2566,2678,2799,2964,3065,10901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ec4e31fa8d0cf74d3d6955f9c39b3c31\\transformed\\core-1.16.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "39,40,41,42,43,44,45,137", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3597,3699,3802,3904,4008,4111,4212,11803", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3694,3797,3899,4003,4106,4207,4329,11899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,207,282,352,435,504,571,645,720,801,885,954,1034,1116,1196,1278,1364,1442,1515,1587,1683,1756,1836,1904", "endColumns": "71,79,74,69,82,68,66,73,74,80,83,68,79,81,79,81,85,77,72,71,95,72,79,67,72", "endOffsets": "122,202,277,347,430,499,566,640,715,796,880,949,1029,1111,1191,1273,1359,1437,1510,1582,1678,1751,1831,1899,1972"}, "to": {"startLines": "33,49,53,55,56,58,72,73,74,121,122,123,124,129,130,131,132,133,134,135,136,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3070,4630,4936,5077,5147,5290,6367,6434,6508,10509,10590,10674,10743,11154,11236,11316,11398,11484,11562,11635,11707,11904,11977,12057,12125", "endColumns": "71,79,74,69,82,68,66,73,74,80,83,68,79,81,79,81,85,77,72,71,95,72,79,67,72", "endOffsets": "3137,4705,5006,5142,5225,5354,6429,6503,6578,10585,10669,10738,10818,11231,11311,11393,11479,11557,11630,11702,11798,11972,12052,12120,12193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3e7df312ad5b5ee0d6592d754d6c3af1\\transformed\\material-1.12.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,351,434,521,627,726,820,930,1022,1084,1149,1248,1314,1374,1476,1538,1614,1672,1750,1815,1869,1986,2050,2114,2168,2248,2382,2468,2555,2658,2754,2843,2979,3064,3152,3304,3399,3482,3540,3592,3658,3737,3819,3890,3977,4053,4130,4207,4278,4388,4495,4575,4672,4772,4846,4927,5032,5090,5178,5245,5336,5428,5490,5554,5617,5686,5789,5896,6001,6106,6168,6224,6308,6402,6480", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,82,86,105,98,93,109,91,61,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,86,102,95,88,135,84,87,151,94,82,57,51,65,78,81,70,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,106,104,104,61,55,83,93,77,75", "endOffsets": "266,346,429,516,622,721,815,925,1017,1079,1144,1243,1309,1369,1471,1533,1609,1667,1745,1810,1864,1981,2045,2109,2163,2243,2377,2463,2550,2653,2749,2838,2974,3059,3147,3299,3394,3477,3535,3587,3653,3732,3814,3885,3972,4048,4125,4202,4273,4383,4490,4570,4667,4767,4841,4922,5027,5085,5173,5240,5331,5423,5485,5549,5612,5681,5784,5891,5996,6101,6163,6219,6303,6397,6475,6551"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,50,51,52,54,57,59,60,61,62,63,64,65,66,67,68,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3142,3222,3305,3392,3498,4334,4428,4538,4710,4772,4837,5011,5230,5359,5461,5523,5599,5657,5735,5800,5854,5971,6035,6099,6153,6233,6583,6669,6756,6859,6955,7044,7180,7265,7353,7505,7600,7683,7741,7793,7859,7938,8020,8091,8178,8254,8331,8408,8479,8589,8696,8776,8873,8973,9047,9128,9233,9291,9379,9446,9537,9629,9691,9755,9818,9887,9990,10097,10202,10307,10369,10425,10906,11000,11078", "endLines": "5,34,35,36,37,38,46,47,48,50,51,52,54,57,59,60,61,62,63,64,65,66,67,68,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,126,127,128", "endColumns": "12,79,82,86,105,98,93,109,91,61,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,86,102,95,88,135,84,87,151,94,82,57,51,65,78,81,70,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,106,104,104,61,55,83,93,77,75", "endOffsets": "316,3217,3300,3387,3493,3592,4423,4533,4625,4767,4832,4931,5072,5285,5456,5518,5594,5652,5730,5795,5849,5966,6030,6094,6148,6228,6362,6664,6751,6854,6950,7039,7175,7260,7348,7500,7595,7678,7736,7788,7854,7933,8015,8086,8173,8249,8326,8403,8474,8584,8691,8771,8868,8968,9042,9123,9228,9286,9374,9441,9532,9624,9686,9750,9813,9882,9985,10092,10197,10302,10364,10420,10504,10995,11073,11149"}}]}]}