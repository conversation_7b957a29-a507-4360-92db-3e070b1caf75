<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res"><file name="ic_notification" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="rn_edit_text_material" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable\rn_edit_text_material.xml" qualifiers="" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backiconmask" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_backiconmask.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="error" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\raw\error.mp3" qualifiers="" type="raw"/><file name="keep" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\raw\keep.xml" qualifiers="" type="raw"/><file name="notification" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\raw\notification.mp3" qualifiers="" type="raw"/><file name="pomodoro" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\raw\pomodoro.mp3" qualifiers="" type="raw"/><file name="success" path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\raw\success.mp3" qualifiers="" type="raw"/><file path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">TYTFizikKamp</string></file><file path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\build\generated\res\resValues\debug"><file path="C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\android\app\build\generated\res\resValues\debug\values\gradleResValues.xml" qualifiers=""><integer name="react_native_dev_server_port">8081</integer><string name="react_native_dev_server_ip" translatable="false">*************</string></file></source></dataSet><mergedItems/></merger>