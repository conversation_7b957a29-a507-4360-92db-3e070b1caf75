{"logs": [{"outputFile": "com.tytfizikkamp.app-mergeDebugResources-45:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e932dc8d24e50c2cb3b8fbb16fc5745d\\transformed\\firebase-messaging-21.1.0\\res\\values-sw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "207", "endColumns": "103", "endOffsets": "310"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5033", "endColumns": "107", "endOffsets": "5136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,210,281,350,433,502,570,649,734,817,900,972,1062,1152,1231,1314,1398,1480,1556,1632,1719,1794,1877,1952", "endColumns": "68,85,70,68,82,68,67,78,84,82,82,71,89,89,78,82,83,81,75,75,86,74,82,74,77", "endOffsets": "119,205,276,345,428,497,565,644,729,812,895,967,1057,1147,1226,1309,1393,1475,1551,1627,1714,1789,1872,1947,2025"}, "to": {"startLines": "33,49,55,57,58,60,74,75,76,123,124,125,126,131,132,133,134,135,136,137,138,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3005,4517,5141,5285,5354,5500,6547,6615,6694,10608,10691,10774,10846,11265,11355,11434,11517,11601,11683,11759,11835,12023,12098,12181,12256", "endColumns": "68,85,70,68,82,68,67,78,84,82,82,71,89,89,78,82,83,81,75,75,86,74,82,74,77", "endOffsets": "3069,4598,5207,5349,5432,5564,6610,6689,6774,10686,10769,10841,10931,11350,11429,11512,11596,11678,11754,11830,11917,12093,12176,12251,12329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d6862beb0c03f816962150b27b444ce5\\transformed\\play-services-basement-17.0.0\\res\\values-sw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "204", "endOffsets": "451"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4603", "endColumns": "204", "endOffsets": "4803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\625ed137c6a3f5343b71917adedb437c\\transformed\\appcompat-1.7.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,412,511,619,709,814,931,1014,1096,1187,1280,1375,1469,1569,1662,1757,1851,1942,2033,2115,2216,2324,2423,2530,2642,2746,2908,10936", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "407,506,614,704,809,926,1009,1091,1182,1275,1370,1464,1564,1657,1752,1846,1937,2028,2110,2211,2319,2418,2525,2637,2741,2903,3000,11014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ec4e31fa8d0cf74d3d6955f9c39b3c31\\transformed\\core-1.16.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "39,40,41,42,43,44,45,139", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3483,3577,3679,3776,3877,3984,4091,11922", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "3572,3674,3771,3872,3979,4086,4201,12018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3e7df312ad5b5ee0d6592d754d6c3af1\\transformed\\material-1.12.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,335,409,482,579,668,767,896,979,1044,1112,1204,1277,1340,1426,1488,1551,1616,1684,1747,1801,1933,1990,2052,2106,2180,2318,2399,2479,2582,2666,2746,2878,2963,3050,3191,3279,3358,3412,3465,3531,3603,3685,3756,3841,3913,3988,4059,4132,4238,4335,4409,4504,4601,4675,4760,4860,4913,4998,5066,5154,5244,5306,5370,5433,5500,5617,5729,5840,5951,6009,6066,6147,6232,6313", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,73,72,96,88,98,128,82,64,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,102,83,79,131,84,86,140,87,78,53,52,65,71,81,70,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80,84,80,79", "endOffsets": "254,330,404,477,574,663,762,891,974,1039,1107,1199,1272,1335,1421,1483,1546,1611,1679,1742,1796,1928,1985,2047,2101,2175,2313,2394,2474,2577,2661,2741,2873,2958,3045,3186,3274,3353,3407,3460,3526,3598,3680,3751,3836,3908,3983,4054,4127,4233,4330,4404,4499,4596,4670,4755,4855,4908,4993,5061,5149,5239,5301,5365,5428,5495,5612,5724,5835,5946,6004,6061,6142,6227,6308,6388"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,51,52,53,56,59,61,62,63,64,65,66,67,68,69,70,71,72,73,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3074,3150,3224,3297,3394,4206,4305,4434,4808,4873,4941,5212,5437,5569,5655,5717,5780,5845,5913,5976,6030,6162,6219,6281,6335,6409,6779,6860,6940,7043,7127,7207,7339,7424,7511,7652,7740,7819,7873,7926,7992,8064,8146,8217,8302,8374,8449,8520,8593,8699,8796,8870,8965,9062,9136,9221,9321,9374,9459,9527,9615,9705,9767,9831,9894,9961,10078,10190,10301,10412,10470,10527,11019,11104,11185", "endLines": "5,34,35,36,37,38,46,47,48,51,52,53,56,59,61,62,63,64,65,66,67,68,69,70,71,72,73,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,128,129,130", "endColumns": "12,75,73,72,96,88,98,128,82,64,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,102,83,79,131,84,86,140,87,78,53,52,65,71,81,70,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80,84,80,79", "endOffsets": "304,3145,3219,3292,3389,3478,4300,4429,4512,4868,4936,5028,5280,5495,5650,5712,5775,5840,5908,5971,6025,6157,6214,6276,6330,6404,6542,6855,6935,7038,7122,7202,7334,7419,7506,7647,7735,7814,7868,7921,7987,8059,8141,8212,8297,8369,8444,8515,8588,8694,8791,8865,8960,9057,9131,9216,9316,9369,9454,9522,9610,9700,9762,9826,9889,9956,10073,10185,10296,10407,10465,10522,10603,11099,11180,11260"}}]}]}