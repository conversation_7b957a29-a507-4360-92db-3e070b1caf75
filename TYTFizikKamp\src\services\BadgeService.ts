import AsyncStorage from '@react-native-async-storage/async-storage';
import badgesData from '../data/badges.json';
import { Badge, UserProgress } from '../types';

export interface BadgeProgress {
  badgeId: string;
  progress: number;
  isEarned: boolean;
  earnedDate?: Date;
}

class BadgeService {
  private static readonly BADGE_PROGRESS_KEY = 'badge_progress';

  static async getBadgeProgress(): Promise<BadgeProgress[]> {
    try {
      const data = await AsyncStorage.getItem(this.BADGE_PROGRESS_KEY);
      if (data) {
        const progress = JSON.parse(data);
        return progress.map((p: any) => ({
          ...p,
          earnedDate: p.earnedDate ? new Date(p.earnedDate) : undefined,
        }));
      }
      
      // İlk kez kullanım - tüm rozetler için başlangıç durumu
      const initialProgress = badgesData.map(badge => ({
        badgeId: badge.id,
        progress: 0,
        isEarned: false,
      }));
      
      await this.saveBadgeProgress(initialProgress);
      return initialProgress;
    } catch (error) {
      console.error('Error getting badge progress:', error);
      return [];
    }
  }

  static async saveBadgeProgress(progress: BadgeProgress[]): Promise<void> {
    try {
      await AsyncStorage.setItem(this.BADGE_PROGRESS_KEY, JSON.stringify(progress));
    } catch (error) {
      console.error('Error saving badge progress:', error);
    }
  }

  static async checkAndAwardBadges(userProgress: UserProgress): Promise<Badge[]> {
    try {
      const badgeProgress = await this.getBadgeProgress();
      const newlyEarnedBadges: Badge[] = [];

      for (const badge of badgesData) {
        const progress = badgeProgress.find(p => p.badgeId === badge.id);
        if (!progress || progress.isEarned) continue;

        const shouldAward = await this.checkBadgeCondition(badge, userProgress);
        
        if (shouldAward) {
          // Rozeti kazandı
          progress.isEarned = true;
          progress.earnedDate = new Date();
          progress.progress = badge.requirement;
          
          newlyEarnedBadges.push(badge as Badge);
        } else {
          // İlerleme güncelle
          progress.progress = await this.calculateBadgeProgress(badge, userProgress);
        }
      }

      await this.saveBadgeProgress(badgeProgress);
      return newlyEarnedBadges;
    } catch (error) {
      console.error('Error checking badges:', error);
      return [];
    }
  }

  private static async checkBadgeCondition(badge: any, userProgress: UserProgress): Promise<boolean> {
    switch (badge.type) {
      case 'completion':
        return this.checkCompletionBadge(badge, userProgress);
      
      case 'streak':
        return this.checkStreakBadge(badge, userProgress);
      
      case 'score':
        return this.checkScoreBadge(badge, userProgress);
      
      case 'special':
        return this.checkSpecialBadge(badge, userProgress);
      
      default:
        return false;
    }
  }

  private static checkCompletionBadge(badge: any, userProgress: UserProgress): boolean {
    switch (badge.id) {
      case 'first_day':
        return userProgress.completed_days.length >= 1;
      
      case 'knowledge_seeker':
        return userProgress.total_questions_answered >= 50;
      
      case 'scholar':
        return userProgress.total_questions_answered >= 100;
      
      case 'physics_master':
        return userProgress.completed_days.length >= 14;
      
      default:
        return false;
    }
  }

  private static checkStreakBadge(badge: any, userProgress: UserProgress): boolean {
    return userProgress.current_streak >= badge.requirement;
  }

  private static checkScoreBadge(badge: any, userProgress: UserProgress): boolean {
    switch (badge.id) {
      case 'perfect_score':
        // Son günde %100 başarı
        const lastDay = userProgress.completed_days[userProgress.completed_days.length - 1];
        return lastDay ? this.getDaySuccessRate(lastDay, userProgress) === 100 : false;
      
      case 'genius':
        // Son 5 günde %90+ başarı
        return this.checkConsecutiveHighScore(userProgress, 5, 90);
      
      case 'perfectionist':
        // Son 3 günde %100 başarı
        return this.checkConsecutiveHighScore(userProgress, 3, 100);
      
      default:
        return false;
    }
  }

  private static checkSpecialBadge(badge: any, userProgress: UserProgress): boolean {
    const now = new Date();
    const hour = now.getHours();

    switch (badge.id) {
      case 'speed_demon':
        // 5 dakikada tüm soruları çözme kontrolü (bu özel bir durum, quiz sırasında kontrol edilmeli)
        return false; // Quiz ekranında kontrol edilecek
      
      case 'comeback_king':
        // Yanlış cevaptan sonra 5 doğru cevap (özel takip gerekli)
        return false; // Quiz sırasında takip edilecek
      
      case 'night_owl':
        return hour >= 22 || hour <= 6;
      
      case 'early_bird':
        return hour >= 6 && hour <= 8;
      
      case 'dedicated':
        // 30 dakika çalışma süresi (özel takip gerekli)
        return false; // Zaman takibi ile kontrol edilecek
      
      default:
        return false;
    }
  }

  private static getDaySuccessRate(dayId: number, userProgress: UserProgress): number {
    const dayProgress = userProgress.day_progress[dayId];
    if (!dayProgress || dayProgress.questionsAnswered.length === 0) return 0;

    const correctAnswers = dayProgress.questionsAnswered.filter(q => q.isCorrect).length;
    return Math.round((correctAnswers / dayProgress.questionsAnswered.length) * 100);
  }

  private static checkConsecutiveHighScore(userProgress: UserProgress, days: number, minScore: number): boolean {
    const recentDays = userProgress.completed_days.slice(-days);
    if (recentDays.length < days) return false;

    return recentDays.every(dayId => {
      const successRate = this.getDaySuccessRate(dayId, userProgress);
      return successRate >= minScore;
    });
  }

  private static async calculateBadgeProgress(badge: any, userProgress: UserProgress): Promise<number> {
    switch (badge.type) {
      case 'completion':
        switch (badge.id) {
          case 'knowledge_seeker':
            return Math.min(userProgress.total_questions_answered, badge.requirement);
          case 'scholar':
            return Math.min(userProgress.total_questions_answered, badge.requirement);
          case 'physics_master':
            return Math.min(userProgress.completed_days.length, badge.requirement);
          default:
            return 0;
        }
      
      case 'streak':
        return Math.min(userProgress.current_streak, badge.requirement);
      
      case 'score':
        // Skor rozetleri için özel hesaplama
        return 0;
      
      case 'special':
        // Özel rozetler için özel hesaplama
        return 0;
      
      default:
        return 0;
    }
  }

  static async getEarnedBadges(): Promise<Badge[]> {
    try {
      const badgeProgress = await this.getBadgeProgress();
      const earnedBadgeIds = badgeProgress
        .filter(p => p.isEarned)
        .map(p => p.badgeId);

      return badgesData.filter(badge => earnedBadgeIds.includes(badge.id)) as Badge[];
    } catch (error) {
      console.error('Error getting earned badges:', error);
      return [];
    }
  }

  static async getBadgeById(badgeId: string): Promise<Badge | null> {
    const badge = badgesData.find(b => b.id === badgeId);
    return badge ? (badge as Badge) : null;
  }

  static async getTotalBadgePoints(): Promise<number> {
    try {
      const earnedBadges = await this.getEarnedBadges();
      return earnedBadges.reduce((total, badge) => total + badge.points, 0);
    } catch (error) {
      console.error('Error calculating total badge points:', error);
      return 0;
    }
  }

  static async getBadgeStats(): Promise<{
    total: number;
    earned: number;
    percentage: number;
    totalPoints: number;
  }> {
    try {
      const earnedBadges = await this.getEarnedBadges();
      const totalBadges = badgesData.length;
      const earnedCount = earnedBadges.length;
      const percentage = Math.round((earnedCount / totalBadges) * 100);
      const totalPoints = earnedBadges.reduce((sum, badge) => sum + badge.points, 0);

      return {
        total: totalBadges,
        earned: earnedCount,
        percentage,
        totalPoints,
      };
    } catch (error) {
      console.error('Error getting badge stats:', error);
      return { total: 0, earned: 0, percentage: 0, totalPoints: 0 };
    }
  }
}

export default BadgeService;
