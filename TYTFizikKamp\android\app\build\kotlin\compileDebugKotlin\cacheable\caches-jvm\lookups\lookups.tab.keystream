  Application android.app  DefaultReactActivityDelegate android.app.Activity  
fabricEnabled android.app.Activity  Boolean android.app.Application  BuildConfig android.app.Application  DefaultReactNativeHost android.app.Application  List android.app.Application  PackageList android.app.Application  ReactPackage android.app.Application  String android.app.Application  apply android.app.Application  getDefaultReactHost android.app.Application  loadReactNative android.app.Application  onCreate android.app.Application  Context android.content  Boolean android.content.Context  BuildConfig android.content.Context  DefaultReactActivityDelegate android.content.Context  DefaultReactNativeHost android.content.Context  List android.content.Context  PackageList android.content.Context  ReactPackage android.content.Context  String android.content.Context  apply android.content.Context  
fabricEnabled android.content.Context  getDefaultReactHost android.content.Context  loadReactNative android.content.Context  Boolean android.content.ContextWrapper  BuildConfig android.content.ContextWrapper  DefaultReactActivityDelegate android.content.ContextWrapper  DefaultReactNativeHost android.content.ContextWrapper  List android.content.ContextWrapper  PackageList android.content.ContextWrapper  ReactPackage android.content.ContextWrapper  String android.content.ContextWrapper  applicationContext android.content.ContextWrapper  apply android.content.ContextWrapper  
fabricEnabled android.content.ContextWrapper  getDefaultReactHost android.content.ContextWrapper  loadReactNative android.content.ContextWrapper  DefaultReactActivityDelegate  android.view.ContextThemeWrapper  
fabricEnabled  android.view.ContextThemeWrapper  DefaultReactActivityDelegate #androidx.activity.ComponentActivity  
fabricEnabled #androidx.activity.ComponentActivity  DefaultReactActivityDelegate (androidx.appcompat.app.AppCompatActivity  
fabricEnabled (androidx.appcompat.app.AppCompatActivity  DefaultReactActivityDelegate #androidx.core.app.ComponentActivity  ReactActivityDelegate #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  
fabricEnabled #androidx.core.app.ComponentActivity  DefaultReactActivityDelegate &androidx.fragment.app.FragmentActivity  
fabricEnabled &androidx.fragment.app.FragmentActivity  PackageList com.facebook.react  
ReactActivity com.facebook.react  ReactActivityDelegate com.facebook.react  ReactApplication com.facebook.react  	ReactHost com.facebook.react  ReactNativeHost com.facebook.react  ReactPackage com.facebook.react  packages com.facebook.react.PackageList  DefaultReactActivityDelegate  com.facebook.react.ReactActivity  
fabricEnabled  com.facebook.react.ReactActivity  loadReactNative 3com.facebook.react.ReactNativeApplicationEntryPoint  BuildConfig "com.facebook.react.ReactNativeHost  PackageList "com.facebook.react.ReactNativeHost  apply "com.facebook.react.ReactNativeHost  DefaultReactActivityDelegate com.facebook.react.defaults  DefaultReactNativeHost com.facebook.react.defaults  
fabricEnabled <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  getDefaultReactHost ,com.facebook.react.defaults.DefaultReactHost  Application com.tytfizikkamp  Boolean com.tytfizikkamp  BuildConfig com.tytfizikkamp  DefaultReactActivityDelegate com.tytfizikkamp  DefaultReactNativeHost com.tytfizikkamp  List com.tytfizikkamp  MainActivity com.tytfizikkamp  MainApplication com.tytfizikkamp  PackageList com.tytfizikkamp  
ReactActivity com.tytfizikkamp  ReactActivityDelegate com.tytfizikkamp  ReactApplication com.tytfizikkamp  	ReactHost com.tytfizikkamp  ReactNativeHost com.tytfizikkamp  ReactPackage com.tytfizikkamp  String com.tytfizikkamp  apply com.tytfizikkamp  
fabricEnabled com.tytfizikkamp  getDefaultReactHost com.tytfizikkamp  loadReactNative com.tytfizikkamp  DEBUG com.tytfizikkamp.BuildConfig  IS_HERMES_ENABLED com.tytfizikkamp.BuildConfig  IS_NEW_ARCHITECTURE_ENABLED com.tytfizikkamp.BuildConfig  DefaultReactActivityDelegate com.tytfizikkamp.MainActivity  
fabricEnabled com.tytfizikkamp.MainActivity  mainComponentName com.tytfizikkamp.MainActivity  BuildConfig  com.tytfizikkamp.MainApplication  PackageList  com.tytfizikkamp.MainApplication  applicationContext  com.tytfizikkamp.MainApplication  apply  com.tytfizikkamp.MainApplication  getDefaultReactHost  com.tytfizikkamp.MainApplication  loadReactNative  com.tytfizikkamp.MainApplication  reactNativeHost  com.tytfizikkamp.MainApplication  	ArrayList 	java.util  apply java.util.ArrayList  	Function1 kotlin  apply kotlin  List kotlin.collections                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      