CMake Warning in C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/RelWithDebInfo/4d394p4b/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/./

  has 203 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/RelWithDebInfo/4d394p4b/arm64-v8a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/./

  has 189 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/RelWithDebInfo/4d394p4b/arm64-v8a/safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/./

  has 185 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    C_/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


