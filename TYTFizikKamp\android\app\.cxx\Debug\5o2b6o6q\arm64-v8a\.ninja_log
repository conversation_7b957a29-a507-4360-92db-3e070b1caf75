# ninja log v5
241	12604	0	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/RNHapticFeedbackSpecJSI-generated.cpp.o	211ee8fb0354eb72
1	33	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/arm64-v8a/CMakeFiles/cmake.verify_globs	27be81503547e0d5
971	34459	7735953228146469	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	15cfbbbcff70664e
1145	34610	7735953228146469	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	6302e7f92e52297
1119	34958	7735953247029610	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	c88524a05449df8c
77	1620	7736084264754828	build.ninja	27c6577bba56e8e8
262	18865	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	68372169cdf8145a
1005	34652	7735953244384993	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	7427433174bfe4c0
55	12552	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	9995d3a519c708fd
34655	73758	7735953635223927	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/ShadowNodes.cpp.o	95356a989d338222
1165	38708	7735953284294258	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	5adf73c78235184e
1052	36824	7735953258334880	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	7c109dd88166e14
146	38065	7735953277760474	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	452580129231ac9b
1074	38133	7735953278092897	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	2fdd6e674fc904d6
1039	38082	7735953278038008	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	cf890a73dfdf2d62
34612	72588	7735953625231951	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/122f554796c7013a174ee08c2d9db3af/jni/safeareacontext-generated.cpp.o	b1430e114493e03a
270	36462	7736083379379932	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	3d7589dd538cf545
987	37252	7735953266893395	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	6941dcd115cd339b
1061	37451	7735953269996913	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	a49ae7608d688ebf
1196	38117	7735953278813977	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewState.cpp.o	afcf4d6f68e85105
78791	79006	7735953689635095	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/arm64-v8a/libreact_codegen_rnscreens.so	d2f4f6752c85f1ea
278	15822	7736083174895939	CMakeFiles/appmodules.dir/OnLoad.cpp.o	3f81cf880ed7e7d5
72451	76172	7735953661397048	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	f905e4325ab47198
34967	71012	7735953609278524	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/States.cpp.o	54d99c20f84f3700
1097	47285	7735953311760292	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	67b32a6fc0a4e898
47263	72521	7735953624625308	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3bba978b2157660a9a8f04f82fd394ad/renderer/components/rnscreens/RNSScreenState.cpp.o	4696394f466f4ec5
34486	71843	7735953617937952	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/EventEmitters.cpp.o	b63583766d6ca029
72521	75687	7735953656423578	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	6e3ca755116f190a
36870	72450	7735953622928598	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/safeareacontextJSI-generated.cpp.o	5bf4ce6d7b06fa8d
1206	70214	7735953415026816	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewShadowNode.cpp.o	daabf2f5d3928834
36463	37146	7736083388568289	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/arm64-v8a/libappmodules.so	99028da2e1ed19c6
1222	45359	7735953295426670	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/Props.cpp.o	6dfc8e7f6c328315
70291	73459	7735953633940738	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/States.cpp.o	b76403b638d28ed0
37087	72618	7735953625377209	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	607f3a9e56779fc1
38118	74456	7735953643475032	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	56e9d26d5328bc7b
47288	76658	7735953665685943	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/EventEmitters.cpp.o	f50340998a10fdec
38065	74218	7735953640957141	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	a30d6e20551dbf27
224	14317	7736083159681142	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ShadowNodes.cpp.o	bf5d259aa138d8ab
1214	70290	7735953571873881	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/ComponentDescriptors.cpp.o	41a2cf964d83772e
37254	75023	7735953648189582	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSScreenShadowNode.cpp.o	efbc18fffdb9090e
247	13419	7736083150286982	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/RNHapticFeedbackSpec-generated.cpp.o	353be4a20ff8ae7a
38084	75107	7735953650589840	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSModalScreenShadowNode.cpp.o	3f8702353aae2d4e
37453	72414	7735953623093244	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	ce4889edf44e2676
38134	74565	7735953645059689	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	36720ed011632bfe
73759	75615	7735953654171878	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/arm64-v8a/libreact_codegen_safeareacontext.so	e36e7731b87222bd
73916	76854	7735953668197585	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	f3975a2d455f2c96
71012	76358	7735953663201015	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	3381930b37fe8ca8
216	8041	7736083096472587	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/7f9d75c760005477a9a92acb25e45305/EventEmitters.cpp.o	aa752b2e1e4613e5
73460	77375	7735953673456611	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/rnscreensJSI-generated.cpp.o	54edb6b43dde1163
74219	77759	7735953677266371	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	a693f8aa6b595e29
72619	76926	7735953668942411	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	d59965b1e6cea02d
74457	77734	7735953677008330	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o	3cc2e7944a2c3d31
70237	74981	7735953648584149	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d2daae902f606f04fd11325c49840ca/react/renderer/components/rnscreens/ShadowNodes.cpp.o	5a036e4b8c562333
72588	77078	7735953670498297	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	196519aaffac512e
72421	77974	7735953679353108	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/Props.cpp.o	582c017bbe3744dd
71843	78791	7735953687299894	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/ComponentDescriptors.cpp.o	d6e434a3aa2fdb77
0	34	0	clean	783ee98371267ada
255	4839	7736083063819436	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/States.cpp.o	f073054b6e23acf6
228	11921	7736083135081059	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/Props.cpp.o	833703b2559555d
236	14984	7736083166463152	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/7f9d75c760005477a9a92acb25e45305/ComponentDescriptors.cpp.o	8a470bd14841064
1	52	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/arm64-v8a/CMakeFiles/cmake.verify_globs	27be81503547e0d5
437	26840	7736084651180414	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	15cfbbbcff70664e
475	26953	7736084650971429	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	6302e7f92e52297
26969	28977	7736084677626212	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/7f9d75c760005477a9a92acb25e45305/EventEmitters.cpp.o	aa752b2e1e4613e5
26864	30273	7736084687792738	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/7f9d75c760005477a9a92acb25e45305/ComponentDescriptors.cpp.o	8a470bd14841064
453	30423	7736084687553851	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	c88524a05449df8c
394	30655	7736084691649076	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	7427433174bfe4c0
30430	32016	7736084708016530	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/States.cpp.o	f073054b6e23acf6
30323	33085	7736084718681995	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewState.cpp.o	afcf4d6f68e85105
30657	33130	7736084719155135	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ShadowNodes.cpp.o	bf5d259aa138d8ab
33130	34772	7736084733891201	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/States.cpp.o	54d99c20f84f3700
33085	35111	0	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/RNHapticFeedbackSpecJSI-generated.cpp.o	211ee8fb0354eb72
34784	36714	7736084754960684	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/safeareacontextJSI-generated.cpp.o	5bf4ce6d7b06fa8d
35112	37533	7736084763142785	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/Props.cpp.o	6dfc8e7f6c328315
500	37843	7736084763122845	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/Props.cpp.o	833703b2559555d
28980	38289	7736084768220626	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewShadowNode.cpp.o	daabf2f5d3928834
402	38617	7736084772060124	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	6941dcd115cd339b
32017	38808	7736084772671466	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/ComponentDescriptors.cpp.o	41a2cf964d83772e
483	38881	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	9995d3a519c708fd
414	39086	7736084777135486	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	7c109dd88166e14
36715	39263	7736084780427680	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/ShadowNodes.cpp.o	95356a989d338222
37533	39950	7736084787292523	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/122f554796c7013a174ee08c2d9db3af/jni/safeareacontext-generated.cpp.o	b1430e114493e03a
37875	40130	7736084788171991	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/EventEmitters.cpp.o	b63583766d6ca029
492	40195	7736084788233415	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	5adf73c78235184e
40132	40701	7736084794512880	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/arm64-v8a/libreact_codegen_safeareacontext.so	e36e7731b87222bd
421	40944	7736084796029601	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	a49ae7608d688ebf
512	41547	7736084799052883	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/RNHapticFeedbackSpec-generated.cpp.o	353be4a20ff8ae7a
429	43978	7736084820557391	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	cf890a73dfdf2d62
468	44092	7736084819878636	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	2fdd6e674fc904d6
385	45307	7736084838317365	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	452580129231ac9b
38294	45481	7736084840416090	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	56e9d26d5328bc7b
44097	46484	7736084852699323	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d2daae902f606f04fd11325c49840ca/react/renderer/components/rnscreens/ShadowNodes.cpp.o	5a036e4b8c562333
45336	46998	7736084857722611	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/States.cpp.o	b76403b638d28ed0
41556	47171	7736084857533500	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	607f3a9e56779fc1
45482	47693	7736084864693662	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/rnscreensJSI-generated.cpp.o	54edb6b43dde1163
368	48460	7736084869511133	CMakeFiles/appmodules.dir/OnLoad.cpp.o	3f81cf880ed7e7d5
46484	48665	7736084874451086	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o	3cc2e7944a2c3d31
39093	49231	7736084878445286	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	ce4889edf44e2676
46998	49356	7736084881384664	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	a693f8aa6b595e29
47172	49508	7736084882914005	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	d59965b1e6cea02d
43982	49756	7736084881927719	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/Props.cpp.o	582c017bbe3744dd
48461	50080	7736084888535834	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	f3975a2d455f2c96
47694	50129	7736084889055619	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	196519aaffac512e
48666	50625	7736084894093147	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	f905e4325ab47198
49237	50941	7736084897228928	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	6e3ca755116f190a
38618	53013	7736084916316558	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	a30d6e20551dbf27
38815	53867	7736084925005479	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSModalScreenShadowNode.cpp.o	3f8702353aae2d4e
40951	54018	7736084926944466	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3bba978b2157660a9a8f04f82fd394ad/renderer/components/rnscreens/RNSScreenState.cpp.o	4696394f466f4ec5
38882	54104	7736084927614405	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSScreenShadowNode.cpp.o	efbc18fffdb9090e
460	54863	7736084934731965	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	67b32a6fc0a4e898
444	57338	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	68372169cdf8145a
39951	58693	7736084974115841	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	36720ed011632bfe
40702	59403	7736084981168403	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	3381930b37fe8ca8
39263	61529	7736085002101755	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/EventEmitters.cpp.o	f50340998a10fdec
40195	66977	7736085056338190	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/ComponentDescriptors.cpp.o	d6e434a3aa2fdb77
66978	67287	7736085060556483	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/arm64-v8a/libreact_codegen_rnscreens.so	d2f4f6752c85f1ea
65	74959	7736085135333522	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	3d7589dd538cf545
74960	75511	7736085142712055	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/arm64-v8a/libappmodules.so	99028da2e1ed19c6
4	43	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/arm64-v8a/CMakeFiles/cmake.verify_globs	27be81503547e0d5
315	2369	0	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/RNHapticFeedbackSpecJSI-generated.cpp.o	211ee8fb0354eb72
302	2378	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	9995d3a519c708fd
41	4016	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	68372169cdf8145a
4016	4563	7736088985639001	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/arm64-v8a/libappmodules.so	99028da2e1ed19c6
