import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

const { width } = Dimensions.get('window');

interface MotivationCardProps {
  message: string;
  streak: number;
  totalQuestions: number;
}

const MotivationCard: React.FC<MotivationCardProps> = ({
  message,
  streak,
  totalQuestions,
}) => {
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Kart animasyonu
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const getMotivationIcon = () => {
    if (streak >= 7) return 'local-fire-department';
    if (streak >= 3) return 'trending-up';
    if (totalQuestions >= 50) return 'star';
    return 'emoji-events';
  };

  const getStreakColor = () => {
    if (streak >= 7) return '#E74C3C';
    if (streak >= 3) return '#F39C12';
    return '#00CEFF';
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ scale: scaleAnim }],
          opacity: opacityAnim,
        },
      ]}
    >
      <LinearGradient
        colors={['#00CEFF', '#0984E3', '#6C5CE7']}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.content}>
          {/* Ana mesaj */}
          <View style={styles.messageSection}>
            <Icon
              name={getMotivationIcon()}
              size={28}
              color="white"
              style={styles.messageIcon}
            />
            <Text style={styles.messageText}>{message}</Text>
          </View>

          {/* İstatistik kartları */}
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <View style={[styles.statIconContainer, { backgroundColor: getStreakColor() }]}>
                <MaterialIcons name="local-fire-department" size={20} color="white" />
              </View>
              <Text style={styles.statNumber}>{streak}</Text>
              <Text style={styles.statLabel}>Günlük Seri</Text>
            </View>

            <View style={styles.statDivider} />

            <View style={styles.statItem}>
              <View style={[styles.statIconContainer, { backgroundColor: '#27AE60' }]}>
                <MaterialIcons name="quiz" size={20} color="white" />
              </View>
              <Text style={styles.statNumber}>{totalQuestions}</Text>
              <Text style={styles.statLabel}>Çözülen Soru</Text>
            </View>
          </View>

          {/* Motivasyon çubuğu */}
          <View style={styles.motivationBar}>
            <View style={styles.motivationProgress}>
              <View
                style={[
                  styles.motivationFill,
                  { width: `${Math.min((streak / 14) * 100, 100)}%` }
                ]}
              />
            </View>
            <Text style={styles.motivationBarText}>
              {streak < 14 ? `${14 - streak} gün kaldı!` : 'Tebrikler! 🎉'}
            </Text>
          </View>
        </View>

        {/* Dekoratif elementler */}
        <View style={styles.decorativeElements}>
          <View style={[styles.circle, styles.circle1]} />
          <View style={[styles.circle, styles.circle2]} />
          <View style={[styles.circle, styles.circle3]} />
        </View>
      </LinearGradient>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 20,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  gradient: {
    borderRadius: 20,
    overflow: 'hidden',
    position: 'relative',
  },
  content: {
    padding: 20,
    zIndex: 2,
  },
  messageSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  messageIcon: {
    marginRight: 12,
  },
  messageText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    lineHeight: 22,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    marginBottom: 20,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 15,
    paddingVertical: 15,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: 'rgba(255,255,255,0.3)',
  },
  motivationBar: {
    alignItems: 'center',
  },
  motivationProgress: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  motivationFill: {
    height: '100%',
    backgroundColor: '#F39C12',
    borderRadius: 4,
  },
  motivationBarText: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.9)',
    fontWeight: '500',
  },
  decorativeElements: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  circle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 50,
  },
  circle1: {
    width: 100,
    height: 100,
    top: -50,
    right: -30,
  },
  circle2: {
    width: 60,
    height: 60,
    bottom: -20,
    left: -20,
  },
  circle3: {
    width: 40,
    height: 40,
    top: 20,
    left: 20,
  },
});

export default MotivationCard;
