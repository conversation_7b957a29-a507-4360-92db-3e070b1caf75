import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface BadgeStatsCardProps {
  stats: {
    total: number;
    earned: number;
    percentage: number;
    totalPoints: number;
  };
}

const BadgeStatsCard: React.FC<BadgeStatsCardProps> = ({ stats }) => {
  const progressAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const pointsAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Kart giriş animasyonu
    Animated.spring(scaleAnim, {
      toValue: 1,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    }).start();

    // İlerleme çubuğu animasyonu
    Animated.timing(progressAnim, {
      toValue: stats.percentage,
      duration: 1500,
      useNativeDriver: false,
    }).start();

    // Puan animasyonu
    Animated.timing(pointsAnim, {
      toValue: stats.totalPoints,
      duration: 2000,
      useNativeDriver: false,
    }).start();
  }, [stats]);

  const getProgressColor = () => {
    if (stats.percentage >= 80) return '#27AE60';
    if (stats.percentage >= 60) return '#F39C12';
    if (stats.percentage >= 40) return '#E67E22';
    return '#E74C3C';
  };

  const getMotivationMessage = () => {
    if (stats.percentage >= 90) return 'Muhteşem! Neredeyse tüm rozetleri topladın! 🏆';
    if (stats.percentage >= 70) return 'Harika gidiyorsun! Devam et! 🚀';
    if (stats.percentage >= 50) return 'İyi ilerleme! Yarı yoldasın! 💪';
    if (stats.percentage >= 25) return 'Güzel başlangıç! Daha fazlası için çalış! 📚';
    return 'Rozet avına başla! İlk rozetini kazan! 🎯';
  };

  const getAchievementLevel = () => {
    if (stats.percentage >= 90) return 'Efsane';
    if (stats.percentage >= 70) return 'Usta';
    if (stats.percentage >= 50) return 'Uzman';
    if (stats.percentage >= 25) return 'Gelişen';
    return 'Başlangıç';
  };

  const getLevelIcon = () => {
    if (stats.percentage >= 90) return 'emoji-events';
    if (stats.percentage >= 70) return 'military-tech';
    if (stats.percentage >= 50) return 'school';
    if (stats.percentage >= 25) return 'trending-up';
    return 'flag';
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <LinearGradient
        colors={['rgba(255,255,255,0.95)', 'rgba(255,255,255,0.85)']}
        style={styles.card}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <MaterialIcons name="emoji-events" size={24} color="#6C5CE7" />
            <Text style={styles.headerTitle}>Rozet İstatistiklerin</Text>
          </View>

          <View style={styles.levelBadge}>
            <MaterialIcons name={getLevelIcon()} size={16} color="white" />
            <Text style={styles.levelText}>{getAchievementLevel()}</Text>
          </View>
        </View>

        {/* Main Stats */}
        <View style={styles.mainStats}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{stats.earned}</Text>
            <Text style={styles.statLabel}>Kazanılan</Text>
          </View>
          
          <View style={styles.statDivider} />
          
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{stats.total}</Text>
            <Text style={styles.statLabel}>Toplam</Text>
          </View>
          
          <View style={styles.statDivider} />
          
          <View style={styles.statItem}>
            <Animated.Text style={styles.statNumber}>
              {pointsAnim.interpolate({
                inputRange: [0, stats.totalPoints || 1],
                outputRange: ['0', stats.totalPoints.toString()],
                extrapolate: 'clamp',
              })}
            </Animated.Text>
            <Text style={styles.statLabel}>Puan</Text>
          </View>
        </View>

        {/* Progress Section */}
        <View style={styles.progressSection}>
          <View style={styles.progressHeader}>
            <Text style={styles.progressTitle}>Tamamlanma Oranı</Text>
            <Text style={styles.progressPercentage}>{stats.percentage}%</Text>
          </View>
          
          <View style={styles.progressBarContainer}>
            <View style={styles.progressBarBackground}>
              <Animated.View
                style={[
                  styles.progressBarFill,
                  {
                    width: progressAnim.interpolate({
                      inputRange: [0, 100],
                      outputRange: ['0%', '100%'],
                      extrapolate: 'clamp',
                    }),
                    backgroundColor: getProgressColor(),
                  },
                ]}
              />
            </View>
          </View>
        </View>

        {/* Motivation Message */}
        <View style={styles.motivationSection}>
          <LinearGradient
            colors={['#6C5CE7', '#A29BFE']}
            style={styles.motivationCard}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <MaterialIcons name="auto-awesome" size={20} color="white" />
            <Text style={styles.motivationText}>{getMotivationMessage()}</Text>
          </LinearGradient>
        </View>

        {/* Quick Stats */}
        <View style={styles.quickStats}>
          <View style={styles.quickStatItem}>
            <MaterialIcons name="local-fire-department" size={16} color="#E74C3C" />
            <Text style={styles.quickStatText}>
              {Math.floor(stats.earned / 3)} Seri Rozeti
            </Text>
          </View>

          <View style={styles.quickStatItem}>
            <MaterialIcons name="trending-up" size={16} color="#F39C12" />
            <Text style={styles.quickStatText}>
              {Math.floor(stats.earned / 5)} Başarı Rozeti
            </Text>
          </View>
        </View>
      </LinearGradient>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 10,
  },
  card: {
    borderRadius: 20,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginLeft: 8,
  },
  levelBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#6C5CE7',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  levelText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  mainStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginBottom: 25,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#6C5CE7',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#7F8C8D',
    fontWeight: '600',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#E9ECEF',
  },
  progressSection: {
    marginBottom: 20,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  progressTitle: {
    fontSize: 14,
    color: '#2C3E50',
    fontWeight: '600',
  },
  progressPercentage: {
    fontSize: 16,
    color: '#6C5CE7',
    fontWeight: 'bold',
  },
  progressBarContainer: {
    marginBottom: 5,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: '#E9ECEF',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  motivationSection: {
    marginBottom: 15,
  },
  motivationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 12,
  },
  motivationText: {
    flex: 1,
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 10,
    lineHeight: 20,
  },
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  quickStatItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quickStatText: {
    fontSize: 12,
    color: '#7F8C8D',
    marginLeft: 6,
    fontWeight: '500',
  },
});

export default BadgeStatsCard;
