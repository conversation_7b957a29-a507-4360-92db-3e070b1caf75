{"version": 3, "names": ["NativeHapticFeedback", "HapticFeedbackTypes", "defaultOptions", "enableVibrateFallback", "ignoreAndroidSystemSettings", "RNHapticFeedback", "trigger", "type", "selection", "options", "console", "warn"], "sourceRoot": "../../src", "sources": ["index.ts"], "mappings": ";;AAAA,OAAOA,oBAAoB,MAAM,oCAAoC;AACrE,SAASC,mBAAmB,QAAQ,SAAS;AAG7C,MAAMC,cAAc,GAAG;EACrBC,qBAAqB,EAAE,KAAK;EAC5BC,2BAA2B,EAAE;AAC/B,CAAC;AAED,MAAMC,gBAAgB,GAAG;EACvBC,OAAOA,CACLC,IAEuB,GAAGN,mBAAmB,CAACO,SAAS,EACvDC,OAAsB,GAAG,CAAC,CAAC,EAC3B;IACA,IAAI;MACFT,oBAAoB,CAACM,OAAO,CAACC,IAAI,EAAE;QAAE,GAAGL,cAAc;QAAE,GAAGO;MAAQ,CAAC,CAAC;IACvE,CAAC,CAAC,MAAM;MACNC,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;IAC9D;EACF;AACF,CAAC;AAED,cAAc,SAAS;AACvB,OAAO,MAAM;EAAEL;AAAQ,CAAC,GAAGD,gBAAgB;AAC3C,eAAeA,gBAAgB", "ignoreList": []}