import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import StorageService, { UserProgress } from '../services/StorageService';
import lessonsData from '../data/lessons.json';
import { Lesson } from '../types';

const { width } = Dimensions.get('window');

interface TopicAnalysis {
  topic: string;
  totalQuestions: number;
  correctAnswers: number;
  wrongAnswers: number;
  successRate: number;
  needsImprovement: boolean;
}

interface WeeklyProgress {
  week: number;
  daysCompleted: number;
  totalQuestions: number;
  correctAnswers: number;
  averageScore: number;
}

const AnalyticsScreen = () => {
  const [userProgress, setUserProgress] = useState<UserProgress | null>(null);
  const [topicAnalysis, setTopicAnalysis] = useState<TopicAnalysis[]>([]);
  const [weeklyProgress, setWeeklyProgress] = useState<WeeklyProgress[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'topics' | 'weekly' | 'recommendations'>('overview');

  useFocusEffect(
    React.useCallback(() => {
      loadAnalyticsData();
    }, [])
  );

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      const progress = await StorageService.getUserProgress();
      setUserProgress(progress);

      // Konu bazlı analiz
      const topicStats = analyzeTopicPerformance(progress);
      setTopicAnalysis(topicStats);

      // Haftalık ilerleme
      const weeklyStats = analyzeWeeklyProgress(progress);
      setWeeklyProgress(weeklyStats);
    } catch (error) {
      console.error('Analytics data loading error:', error);
      Alert.alert('Hata', 'Analiz verileri yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const analyzeTopicPerformance = (progress: UserProgress): TopicAnalysis[] => {
    const topicStats: { [topic: string]: { correct: number; wrong: number; total: number } } = {};

    // Quiz sonuçlarından konu bazlı istatistikleri çıkar
    Object.values(progress.quiz_results || {}).forEach(result => {
      const lesson = lessonsData.find(l => l.gun === result.dayId);
      if (lesson) {
        const topic = lesson.konu;
        if (!topicStats[topic]) {
          topicStats[topic] = { correct: 0, wrong: 0, total: 0 };
        }
        topicStats[topic].correct += result.correctAnswers;
        topicStats[topic].wrong += result.wrongAnswers;
        topicStats[topic].total += result.totalQuestions;
      }
    });

    return Object.entries(topicStats).map(([topic, stats]) => ({
      topic,
      totalQuestions: stats.total,
      correctAnswers: stats.correct,
      wrongAnswers: stats.wrong,
      successRate: stats.total > 0 ? (stats.correct / stats.total) * 100 : 0,
      needsImprovement: stats.total > 0 && (stats.correct / stats.total) < 0.7,
    })).sort((a, b) => a.successRate - b.successRate);
  };

  const analyzeWeeklyProgress = (progress: UserProgress): WeeklyProgress[] => {
    const weeks: WeeklyProgress[] = [];
    
    for (let week = 1; week <= 2; week++) {
      const startDay = (week - 1) * 7 + 1;
      const endDay = week * 7;
      
      let daysCompleted = 0;
      let totalQuestions = 0;
      let correctAnswers = 0;
      
      for (let day = startDay; day <= endDay && day <= 14; day++) {
        if (progress.completed_days.includes(day)) {
          daysCompleted++;
        }
        
        const result = progress.quiz_results?.[day];
        if (result) {
          totalQuestions += result.totalQuestions;
          correctAnswers += result.correctAnswers;
        }
      }
      
      weeks.push({
        week,
        daysCompleted,
        totalQuestions,
        correctAnswers,
        averageScore: totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0,
      });
    }
    
    return weeks;
  };

  const getRecommendations = (): string[] => {
    const recommendations: string[] = [];
    
    if (!userProgress) return recommendations;

    // Başarı oranı düşük konular için öneriler
    const weakTopics = topicAnalysis.filter(t => t.needsImprovement);
    if (weakTopics.length > 0) {
      recommendations.push(`🎯 ${weakTopics[0].topic} konusunda daha fazla çalışma yapmalısın. Başarı oranın: %${weakTopics[0].successRate.toFixed(1)}`);
    }

    // Streak önerisi
    if (userProgress.current_streak < 3) {
      recommendations.push('🔥 Günlük çalışma alışkanlığını geliştir. 3 günlük seri yapmaya odaklan!');
    }

    // Yanlış sorular önerisi
    const totalWrongQuestions = Object.values(userProgress.wrong_questions || {}).reduce((sum, questions) => sum + questions.length, 0);
    if (totalWrongQuestions > 10) {
      recommendations.push('📚 Yanlış yaptığın soruları tekrar çözmeyi unutma. Toplam yanlış soru sayın: ' + totalWrongQuestions);
    }

    // Genel başarı önerisi
    const overallSuccess = userProgress.total_questions_answered > 0 ? 
      (userProgress.total_correct_answers / userProgress.total_questions_answered) * 100 : 0;
    
    if (overallSuccess < 80) {
      recommendations.push('💪 Genel başarı oranını artırmak için daha dikkatli çalış. Mevcut oran: %' + overallSuccess.toFixed(1));
    }

    // Motivasyon mesajları
    if (userProgress.completed_days.length >= 7) {
      recommendations.push('🌟 Harika gidiyorsun! Bir haftayı tamamladın. Bu tempoda devam et!');
    }

    return recommendations.slice(0, 4); // En fazla 4 öneri göster
  };

  if (loading) {
    return (
      <LinearGradient colors={['#6C5CE7', '#A29BFE']} style={styles.loadingContainer}>
        <MaterialIcons name="analytics" size={48} color="white" />
        <Text style={styles.loadingText}>Analiz Verileri Yükleniyor...</Text>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient colors={['#6C5CE7', '#A29BFE', '#F5F6FA']} style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <MaterialIcons name="analytics" size={28} color="white" />
        <Text style={styles.headerTitle}>Rehberlik & Analiz</Text>
        <Text style={styles.headerSubtitle}>Performansını İncele</Text>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabScroll}>
          {[
            { key: 'overview', label: 'Genel', icon: 'dashboard' },
            { key: 'topics', label: 'Konular', icon: 'topic' },
            { key: 'weekly', label: 'Haftalık', icon: 'date-range' },
            { key: 'recommendations', label: 'Öneriler', icon: 'lightbulb' },
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[styles.tab, selectedTab === tab.key && styles.activeTab]}
              onPress={() => setSelectedTab(tab.key as any)}
            >
              <MaterialIcons 
                name={tab.icon} 
                size={20} 
                color={selectedTab === tab.key ? '#6C5CE7' : '#666'} 
              />
              <Text style={[styles.tabText, selectedTab === tab.key && styles.activeTabText]}>
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {selectedTab === 'overview' && renderOverview()}
        {selectedTab === 'topics' && renderTopics()}
        {selectedTab === 'weekly' && renderWeekly()}
        {selectedTab === 'recommendations' && renderRecommendations()}
      </ScrollView>
    </LinearGradient>
  );

  function renderOverview() {
    if (!userProgress) return null;

    const overallSuccess = userProgress.total_questions_answered > 0 ? 
      (userProgress.total_correct_answers / userProgress.total_questions_answered) * 100 : 0;

    return (
      <View style={styles.overviewContainer}>
        {/* Genel İstatistikler */}
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <MaterialIcons name="school" size={32} color="#6C5CE7" />
            <Text style={styles.statNumber}>{userProgress.completed_days.length}</Text>
            <Text style={styles.statLabel}>Tamamlanan Gün</Text>
          </View>
          
          <View style={styles.statCard}>
            <MaterialIcons name="quiz" size={32} color="#00CEFF" />
            <Text style={styles.statNumber}>{userProgress.total_questions_answered}</Text>
            <Text style={styles.statLabel}>Çözülen Soru</Text>
          </View>
          
          <View style={styles.statCard}>
            <MaterialIcons name="trending-up" size={32} color="#27AE60" />
            <Text style={styles.statNumber}>%{overallSuccess.toFixed(1)}</Text>
            <Text style={styles.statLabel}>Başarı Oranı</Text>
          </View>
          
          <View style={styles.statCard}>
            <MaterialIcons name="local-fire-department" size={32} color="#FF6B6B" />
            <Text style={styles.statNumber}>{userProgress.current_streak}</Text>
            <Text style={styles.statLabel}>Günlük Seri</Text>
          </View>
        </View>

        {/* İlerleme Çubuğu */}
        <View style={styles.progressCard}>
          <Text style={styles.progressTitle}>Kamp İlerlemen</Text>
          <View style={styles.progressBarContainer}>
            <View style={[styles.progressBar, { width: `${(userProgress.completed_days.length / 14) * 100}%` }]} />
          </View>
          <Text style={styles.progressText}>
            {userProgress.completed_days.length}/14 Gün Tamamlandı
          </Text>
        </View>
      </View>
    );
  }

  function renderTopics() {
    return (
      <View style={styles.topicsContainer}>
        <Text style={styles.sectionTitle}>Konu Bazlı Performans</Text>

        {topicAnalysis.length === 0 ? (
          <View style={styles.emptyState}>
            <MaterialIcons name="topic" size={48} color="#ccc" />
            <Text style={styles.emptyText}>Henüz konu analizi yapılacak veri yok</Text>
          </View>
        ) : (
          topicAnalysis.map((topic, index) => (
            <View key={index} style={[styles.topicCard, topic.needsImprovement && styles.topicCardWarning]}>
              <View style={styles.topicHeader}>
                <Text style={styles.topicName}>{topic.topic}</Text>
                {topic.needsImprovement && (
                  <MaterialIcons name="warning" size={20} color="#FF6B6B" />
                )}
              </View>

              <View style={styles.topicStats}>
                <View style={styles.topicStat}>
                  <Text style={styles.topicStatNumber}>{topic.correctAnswers}</Text>
                  <Text style={styles.topicStatLabel}>Doğru</Text>
                </View>
                <View style={styles.topicStat}>
                  <Text style={styles.topicStatNumber}>{topic.wrongAnswers}</Text>
                  <Text style={styles.topicStatLabel}>Yanlış</Text>
                </View>
                <View style={styles.topicStat}>
                  <Text style={styles.topicStatNumber}>%{topic.successRate.toFixed(1)}</Text>
                  <Text style={styles.topicStatLabel}>Başarı</Text>
                </View>
              </View>

              <View style={styles.topicProgressContainer}>
                <View style={[
                  styles.topicProgressBar,
                  {
                    width: `${topic.successRate}%`,
                    backgroundColor: topic.successRate >= 70 ? '#27AE60' : topic.successRate >= 50 ? '#F39C12' : '#E74C3C'
                  }
                ]} />
              </View>
            </View>
          ))
        )}
      </View>
    );
  }

  function renderWeekly() {
    return (
      <View style={styles.weeklyContainer}>
        <Text style={styles.sectionTitle}>Haftalık İlerleme</Text>

        {weeklyProgress.map((week, index) => (
          <View key={index} style={styles.weekCard}>
            <View style={styles.weekHeader}>
              <MaterialIcons name="date-range" size={24} color="#6C5CE7" />
              <Text style={styles.weekTitle}>{week.week}. Hafta</Text>
              <Text style={styles.weekDays}>{week.daysCompleted}/7 Gün</Text>
            </View>

            <View style={styles.weekStats}>
              <View style={styles.weekStat}>
                <Text style={styles.weekStatNumber}>{week.totalQuestions}</Text>
                <Text style={styles.weekStatLabel}>Toplam Soru</Text>
              </View>
              <View style={styles.weekStat}>
                <Text style={styles.weekStatNumber}>{week.correctAnswers}</Text>
                <Text style={styles.weekStatLabel}>Doğru Cevap</Text>
              </View>
              <View style={styles.weekStat}>
                <Text style={styles.weekStatNumber}>%{week.averageScore.toFixed(1)}</Text>
                <Text style={styles.weekStatLabel}>Ortalama</Text>
              </View>
            </View>

            <View style={styles.weekProgressContainer}>
              <View style={[styles.weekProgressBar, { width: `${(week.daysCompleted / 7) * 100}%` }]} />
            </View>
          </View>
        ))}
      </View>
    );
  }

  function renderRecommendations() {
    const recommendations = getRecommendations();

    return (
      <View style={styles.recommendationsContainer}>
        <Text style={styles.sectionTitle}>Kişisel Öneriler</Text>

        {recommendations.length === 0 ? (
          <View style={styles.emptyState}>
            <MaterialIcons name="lightbulb" size={48} color="#ccc" />
            <Text style={styles.emptyText}>Harika! Şu an için özel önerin yok.</Text>
          </View>
        ) : (
          recommendations.map((recommendation, index) => (
            <View key={index} style={styles.recommendationCard}>
              <MaterialIcons name="lightbulb" size={24} color="#F39C12" />
              <Text style={styles.recommendationText}>{recommendation}</Text>
            </View>
          ))
        )}

        {/* Genel Motivasyon Kartları */}
        <View style={styles.motivationSection}>
          <Text style={styles.motivationTitle}>💪 Motivasyon</Text>

          <View style={styles.motivationCard}>
            <MaterialIcons name="emoji-events" size={32} color="#FFD700" />
            <Text style={styles.motivationText}>
              Her gün biraz daha ilerle! Küçük adımlar büyük başarıları getirir.
            </Text>
          </View>

          <View style={styles.motivationCard}>
            <MaterialIcons name="trending-up" size={32} color="#27AE60" />
            <Text style={styles.motivationText}>
              Yanlış yaptığın sorular, öğrenme fırsatların! Onları tekrar çözmeyi unutma.
            </Text>
          </View>

          <View style={styles.motivationCard}>
            <MaterialIcons name="schedule" size={32} color="#6C5CE7" />
            <Text style={styles.motivationText}>
              Düzenli çalışma, başarının anahtarı. Pomodoro tekniğini dene!
            </Text>
          </View>

          {/* Akıllı Öneriler */}
          <View style={styles.smartRecommendations}>
            <Text style={styles.smartTitle}>🤖 Akıllı Öneriler</Text>

            {topicAnalysis.length > 0 && (
              <View style={styles.smartCard}>
                <MaterialIcons name="psychology" size={24} color="#9B59B6" />
                <View style={styles.smartContent}>
                  <Text style={styles.smartText}>
                    En zor konun: <Text style={styles.smartHighlight}>{topicAnalysis[0]?.topic}</Text>
                  </Text>
                  <Text style={styles.smartSubtext}>
                    Bu konuya daha fazla zaman ayır ve video dersini tekrar izle.
                  </Text>
                </View>
              </View>
            )}

            {userProgress && userProgress.current_streak >= 3 && (
              <View style={styles.smartCard}>
                <MaterialIcons name="local-fire-department" size={24} color="#E67E22" />
                <View style={styles.smartContent}>
                  <Text style={styles.smartText}>
                    Harika serin var! <Text style={styles.smartHighlight}>{userProgress.current_streak} gün</Text>
                  </Text>
                  <Text style={styles.smartSubtext}>
                    Bu momentum'u kaybetme. Yarın da çalışmayı unutma!
                  </Text>
                </View>
              </View>
            )}

            {userProgress && userProgress.total_questions_answered > 50 && (
              <View style={styles.smartCard}>
                <MaterialIcons name="insights" size={24} color="#3498DB" />
                <View style={styles.smartContent}>
                  <Text style={styles.smartText}>
                    Çok aktifsin! <Text style={styles.smartHighlight}>{userProgress.total_questions_answered} soru</Text>
                  </Text>
                  <Text style={styles.smartSubtext}>
                    Bu tempoda devam edersen hedefine kolayca ulaşırsın.
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    fontSize: 16,
    marginTop: 16,
    fontWeight: '600',
  },
  header: {
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
    alignItems: 'center',
  },
  headerTitle: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginTop: 4,
  },
  tabContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  tabScroll: {
    flexDirection: 'row',
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
  },
  activeTab: {
    backgroundColor: 'white',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tabText: {
    marginLeft: 6,
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#6C5CE7',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  // Overview Styles
  overviewContainer: {
    paddingBottom: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: 'white',
    width: (width - 60) / 2,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    textAlign: 'center',
  },
  progressCard: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#6C5CE7',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  // Topics Styles
  topicsContainer: {
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 16,
    textAlign: 'center',
  },
  topicCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  topicCardWarning: {
    borderLeftWidth: 4,
    borderLeftColor: '#FF6B6B',
  },
  topicHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  topicName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  topicStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
  },
  topicStat: {
    alignItems: 'center',
  },
  topicStatNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  topicStatLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  topicProgressContainer: {
    height: 6,
    backgroundColor: '#E0E0E0',
    borderRadius: 3,
  },
  topicProgressBar: {
    height: '100%',
    borderRadius: 3,
  },
  // Weekly Styles
  weeklyContainer: {
    paddingBottom: 20,
  },
  weekCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  weekHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  weekTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
    flex: 1,
  },
  weekDays: {
    fontSize: 14,
    color: '#6C5CE7',
    fontWeight: '500',
  },
  weekStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
  },
  weekStat: {
    alignItems: 'center',
  },
  weekStatNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  weekStatLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  weekProgressContainer: {
    height: 6,
    backgroundColor: '#E0E0E0',
    borderRadius: 3,
  },
  weekProgressBar: {
    height: '100%',
    backgroundColor: '#6C5CE7',
    borderRadius: 3,
  },
  // Recommendations Styles
  recommendationsContainer: {
    paddingBottom: 20,
  },
  recommendationCard: {
    backgroundColor: 'white',
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  recommendationText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    marginLeft: 12,
    lineHeight: 20,
  },
  motivationSection: {
    marginTop: 20,
  },
  motivationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 12,
    textAlign: 'center',
  },
  motivationCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    alignItems: 'center',
  },
  motivationText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    marginLeft: 12,
    lineHeight: 20,
    fontStyle: 'italic',
  },
  // Empty State
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 16,
    marginTop: 12,
    textAlign: 'center',
  },
  // Smart Recommendations
  smartRecommendations: {
    marginTop: 20,
  },
  smartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 12,
    textAlign: 'center',
  },
  smartCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  smartContent: {
    flex: 1,
    marginLeft: 12,
  },
  smartText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
    marginBottom: 4,
  },
  smartHighlight: {
    color: '#6C5CE7',
    fontWeight: 'bold',
  },
  smartSubtext: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
    fontStyle: 'italic',
  },
});

export default AnalyticsScreen;
