"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.HapticFeedbackTypes = void 0;
let HapticFeedbackTypes = exports.HapticFeedbackTypes = /*#__PURE__*/function (HapticFeedbackTypes) {
  HapticFeedbackTypes["selection"] = "selection";
  HapticFeedbackTypes["impactLight"] = "impactLight";
  HapticFeedbackTypes["impactMedium"] = "impactMedium";
  HapticFeedbackTypes["impactHeavy"] = "impactHeavy";
  HapticFeedbackTypes["rigid"] = "rigid";
  HapticFeedbackTypes["soft"] = "soft";
  HapticFeedbackTypes["notificationSuccess"] = "notificationSuccess";
  HapticFeedbackTypes["notificationWarning"] = "notificationWarning";
  HapticFeedbackTypes["notificationError"] = "notificationError";
  HapticFeedbackTypes["clockTick"] = "clockTick";
  HapticFeedbackTypes["contextClick"] = "contextClick";
  HapticFeedbackTypes["keyboardPress"] = "keyboardPress";
  HapticFeedbackTypes["keyboardRelease"] = "keyboardRelease";
  HapticFeedbackTypes["keyboardTap"] = "keyboardTap";
  HapticFeedbackTypes["longPress"] = "longPress";
  HapticFeedbackTypes["textHandleMove"] = "textHandleMove";
  HapticFeedbackTypes["virtualKey"] = "virtualKey";
  HapticFeedbackTypes["virtualKeyRelease"] = "virtualKeyRelease";
  HapticFeedbackTypes["effectClick"] = "effectClick";
  HapticFeedbackTypes["effectDoubleClick"] = "effectDoubleClick";
  HapticFeedbackTypes["effectHeavyClick"] = "effectHeavyClick";
  HapticFeedbackTypes["effectTick"] = "effectTick";
  return HapticFeedbackTypes;
}({});
//# sourceMappingURL=types.js.map