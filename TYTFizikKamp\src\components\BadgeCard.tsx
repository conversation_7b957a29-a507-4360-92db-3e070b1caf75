import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import { Badge } from '../types';
import { BadgeProgress } from '../services/BadgeService';

const { width } = Dimensions.get('window');
const cardWidth = (width - 60) / 2; // 2 sütun, padding hesabı

interface BadgeCardProps {
  badge: Badge;
  progress?: BadgeProgress;
  onPress: () => void;
}

const BadgeCard: React.FC<BadgeCardProps> = ({ badge, progress, onPress }) => {
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Kart giriş animasyonu
    Animated.spring(scaleAnim, {
      toValue: 1,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    }).start();

    // Kazanılan rozetler için parıltı efekti
    if (progress?.isEarned) {
      const glowAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(glowAnim, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(glowAnim, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
          }),
        ])
      );
      glowAnimation.start();
    }
  }, [progress?.isEarned]);

  const getProgressPercentage = (): number => {
    if (!progress) return 0;
    return Math.min((progress.progress / badge.requirement) * 100, 100);
  };

  const getCardStyle = () => {
    if (progress?.isEarned) {
      return [styles.card, styles.earnedCard];
    }
    return [styles.card, styles.lockedCard];
  };

  const getGradientColors = () => {
    if (progress?.isEarned) {
      switch (badge.type) {
        case 'completion':
          return ['#27AE60', '#2ECC71'];
        case 'streak':
          return ['#E74C3C', '#FF6B6B'];
        case 'score':
          return ['#F39C12', '#F1C40F'];
        case 'special':
          return ['#9B59B6', '#8E44AD'];
        default:
          return ['#3498DB', '#2980B9'];
      }
    }
    return ['#BDC3C7', '#95A5A6'];
  };

  const getBadgeTypeIcon = () => {
    switch (badge.type) {
      case 'completion':
        return 'check-circle';
      case 'streak':
        return 'local-fire-department';
      case 'score':
        return 'trending-up';
      case 'special':
        return 'auto-awesome';
      default:
        return 'emoji-events';
    }
  };

  const formatEarnedDate = (): string => {
    if (!progress?.earnedDate) return '';
    const date = new Date(progress.earnedDate);
    return date.toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'short',
    });
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <TouchableOpacity
        style={getCardStyle()}
        onPress={onPress}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={getGradientColors()}
          style={styles.cardGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {/* Parıltı Efekti */}
          {progress?.isEarned && (
            <Animated.View
              style={[
                styles.glowEffect,
                {
                  opacity: glowAnim,
                },
              ]}
            />
          )}

          {/* Badge Icon */}
          <View style={styles.iconContainer}>
            <Text style={styles.badgeIcon}>{badge.icon}</Text>
            {progress?.isEarned && (
              <View style={styles.earnedIndicator}>
                <MaterialIcons name="check" size={12} color="white" />
              </View>
            )}
          </View>

          {/* Badge Info */}
          <View style={styles.badgeInfo}>
            <Text style={styles.badgeName} numberOfLines={2}>
              {badge.name}
            </Text>
            <Text style={styles.badgeDescription} numberOfLines={2}>
              {badge.description}
            </Text>
          </View>

          {/* Progress Bar */}
          {!progress?.isEarned && progress && (
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    { width: `${getProgressPercentage()}%` },
                  ]}
                />
              </View>
              <Text style={styles.progressText}>
                {progress.progress}/{badge.requirement}
              </Text>
            </View>
          )}

          {/* Earned Date */}
          {progress?.isEarned && (
            <View style={styles.earnedInfo}>
              <MaterialIcons name="schedule" size={12} color="rgba(255,255,255,0.8)" />
              <Text style={styles.earnedDate}>{formatEarnedDate()}</Text>
            </View>
          )}

          {/* Points */}
          <View style={styles.pointsContainer}>
            <MaterialIcons name="star" size={14} color="#F1C40F" />
            <Text style={styles.pointsText}>{badge.points}</Text>
          </View>

          {/* Badge Type Indicator */}
          <View style={styles.typeIndicator}>
            <MaterialIcons
              name={getBadgeTypeIcon()}
              size={16}
              color="rgba(255,255,255,0.8)"
            />
          </View>

          {/* Lock Overlay */}
          {!progress?.isEarned && (
            <View style={styles.lockOverlay}>
              <MaterialIcons name="lock" size={20} color="rgba(255,255,255,0.6)" />
            </View>
          )}
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: cardWidth,
    marginBottom: 16,
  },
  card: {
    borderRadius: 15,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  earnedCard: {
    elevation: 5,
    shadowOpacity: 0.3,
  },
  lockedCard: {
    opacity: 0.7,
  },
  cardGradient: {
    padding: 16,
    minHeight: 160,
    position: 'relative',
  },
  glowEffect: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 15,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 12,
    position: 'relative',
  },
  badgeIcon: {
    fontSize: 32,
  },
  earnedIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#27AE60',
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeInfo: {
    alignItems: 'center',
    marginBottom: 12,
  },
  badgeName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 4,
  },
  badgeDescription: {
    fontSize: 11,
    color: 'rgba(255,255,255,0.9)',
    textAlign: 'center',
    lineHeight: 14,
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    backgroundColor: 'white',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 10,
    color: 'rgba(255,255,255,0.8)',
    textAlign: 'center',
  },
  earnedInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  earnedDate: {
    fontSize: 10,
    color: 'rgba(255,255,255,0.8)',
    marginLeft: 4,
  },
  pointsContainer: {
    position: 'absolute',
    top: 12,
    right: 12,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.2)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  pointsText: {
    fontSize: 10,
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 2,
  },
  typeIndicator: {
    position: 'absolute',
    top: 12,
    left: 12,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  lockOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 15,
  },
});

export default BadgeCard;
