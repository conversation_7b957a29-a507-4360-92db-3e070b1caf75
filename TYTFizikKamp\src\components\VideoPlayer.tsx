import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

const { width } = Dimensions.get('window');

interface VideoPlayerProps {
  videoUrl: string;
  onPress: () => void;
  thumbnail?: string;
  title?: string;
  duration?: string;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  videoUrl,
  onPress,
  thumbnail,
  title = "Video Ders",
  duration = "15:30",
}) => {
  const [imageError, setImageError] = useState(false);

  const getVideoId = (url: string): string => {
    const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
    return match ? match[1] : '';
  };

  const getThumbnailUrl = (): string => {
    if (thumbnail && !imageError) return thumbnail;
    
    const videoId = getVideoId(videoUrl);
    if (videoId) {
      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
    }
    
    return '';
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <View style={styles.videoContainer}>
        {/* Thumbnail */}
        <View style={styles.thumbnailContainer}>
          {getThumbnailUrl() ? (
            <Image
              source={{ uri: getThumbnailUrl() }}
              style={styles.thumbnail}
              onError={() => setImageError(true)}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.placeholderThumbnail}>
              <MaterialIcons name="play-circle-filled" size={60} color="rgba(255,255,255,0.8)" />
            </View>
          )}
          
          {/* Play Button Overlay */}
          <View style={styles.playOverlay}>
            <LinearGradient
              colors={['rgba(0,0,0,0.3)', 'rgba(0,0,0,0.6)']}
              style={styles.overlayGradient}
            >
              <View style={styles.playButton}>
                <MaterialIcons name="play-arrow" size={40} color="white" />
              </View>
            </LinearGradient>
          </View>

          {/* Duration Badge */}
          <View style={styles.durationBadge}>
            <Text style={styles.durationText}>{duration}</Text>
          </View>
        </View>

        {/* Video Info */}
        <View style={styles.videoInfo}>
          <View style={styles.videoHeader}>
            <MaterialIcons name="play-circle-outline" size={24} color="#6C5CE7" />
            <Text style={styles.videoTitle}>{title}</Text>
          </View>
          
          <View style={styles.videoMeta}>
            <View style={styles.metaItem}>
              <MaterialIcons name="schedule" size={16} color="#7F8C8D" />
              <Text style={styles.metaText}>{duration}</Text>
            </View>
            
            <View style={styles.metaItem}>
              <MaterialIcons name="hd" size={16} color="#7F8C8D" />
              <Text style={styles.metaText}>HD Kalite</Text>
            </View>
          </View>

          <Text style={styles.watchText}>Videoyu izlemek için tıkla</Text>
        </View>
      </View>

      {/* Action Indicators */}
      <View style={styles.actionIndicators}>
        <View style={styles.indicator}>
          <MaterialIcons name="touch-app" size={20} color="#00CEFF" />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(255,255,255,0.95)',
    borderRadius: 15,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  videoContainer: {
    position: 'relative',
  },
  thumbnailContainer: {
    width: '100%',
    height: 200,
    position: 'relative',
    backgroundColor: '#000',
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  placeholderThumbnail: {
    width: '100%',
    height: '100%',
    backgroundColor: '#34495E',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  overlayGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(108, 92, 231, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  durationBadge: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: 'rgba(0,0,0,0.8)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  durationText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  videoInfo: {
    padding: 16,
  },
  videoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  videoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C3E50',
    marginLeft: 8,
    flex: 1,
  },
  videoMeta: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  metaText: {
    fontSize: 12,
    color: '#7F8C8D',
    marginLeft: 4,
  },
  watchText: {
    fontSize: 14,
    color: '#6C5CE7',
    fontWeight: '500',
    textAlign: 'center',
  },
  actionIndicators: {
    position: 'absolute',
    top: 10,
    left: 10,
  },
  indicator: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255,255,255,0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
});

export default VideoPlayer;
