apply plugin: 'com.android.library'

def DEFAULT_COMPILE_SDK_VERSION             = 26
def DEFAULT_BUILD_TOOLS_VERSION             = "26.0.2"
def DEFAULT_TARGET_SDK_VERSION              = 26

android {
    compileSdkVersion rootProject.hasProperty('compileSdkVersion') ? rootProject.compileSdkVersion : DEFAULT_COMPILE_SDK_VERSION
    buildToolsVersion rootProject.hasProperty('buildToolsVersion') ? rootProject.buildToolsVersion : DEFAULT_BUILD_TOOLS_VERSION

  defaultConfig {
    minSdkVersion 16
    targetSdkVersion rootProject.hasProperty('targetSdkVersion') ? rootProject.targetSdkVersion : DEFAULT_TARGET_SDK_VERSION
    versionCode 1
    versionName "1.0"
    ndk {
      abiFilters "armeabi-v7a", "x86"
    }
  }
}

dependencies {
  compileOnly 'com.facebook.react:react-native:+'
}