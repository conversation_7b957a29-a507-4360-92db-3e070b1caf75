# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 48ms
  generate-prefab-packages
    exec-prefab 2798ms
    [gap of 127ms]
  generate-prefab-packages completed in 2932ms
  execute-generate-process
    [gap of 20ms]
    exec-configure 11953ms
    [gap of 182ms]
  execute-generate-process completed in 12155ms
  [gap of 168ms]
  write-metadata-json-to-file 39ms
generate_cxx_metadata completed in 15370ms

