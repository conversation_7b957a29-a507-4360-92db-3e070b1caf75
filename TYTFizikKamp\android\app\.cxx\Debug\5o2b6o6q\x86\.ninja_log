# ninja log v5
7247	11794	7734407545532941	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	68c28154df0f800a
3	37	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86/CMakeFiles/cmake.verify_globs	bd4379764f1cfde4
9179	16564	7734407593408051	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/ComponentDescriptors.cpp.o	3ea19a6bb548673a
6159	12092	7734407548595038	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/ShadowNodes.cpp.o	3958802218f6c458
231	6159	7734407489058167	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	e12595e6304471de
264	7849	7734407506273726	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/72f994d6b4d4ae1617cc1e36268935c9/safeareacontext/RNCSafeAreaViewState.cpp.o	74d2175769205f06
189	6295	7734407490779806	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	c5ddd76f59905204
225	6266	7734407490479797	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	b8fa295fdd34ef61
184	5949	7734407486362042	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	941c1e45fec645f9
12236	15444	7734407582410664	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	bb63a1c8c67f090c
193	6939	7734407497184693	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	63d70d8f0e7fc89e
202	7246	7734407500003804	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	5fe556bdf83b026
207	7310	7734407500054294	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	ecc342708ed150c4
239	7323	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	517b3ed1de2e3d77
7640	12865	7734407556423669	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	fce44a0299b1f502
247	7390	7734407501667918	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	63c7de76a21f60c9
30	7640	7734407503920343	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	41762e27a70e4f
197	7590	7734407503821193	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	328ae48e2c99dc86
10936	19860	7734407626163671	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	8129f37e9673171d
11191	15799	7734407585976596	CMakeFiles/appmodules.dir/OnLoad.cpp.o	41bd02e1cae99110
12402	15604	7734407584039294	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	69c67b2140553f82
255	7613	7734407503920343	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	51ea157edd4e9c34
7850	14045	7734407568193927	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/11247e6eb8d2ed237e1fbab4e82a70d7/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	b9263bf40ecb3db
271	9179	7734407519464593	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/72f994d6b4d4ae1617cc1e36268935c9/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	3362ad819f1af562
213	9307	7734407520529315	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o	c5ae5f5311aa23bc
219	8912	7734407516818372	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	e2342280feeedaf
6939	10313	7734407530252717	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/States.cpp.o	174d6a2385d50598
6267	10936	7734407537040098	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/safeareacontextJSI-generated.cpp.o	564ef381e0c8abf1
285	7057	7734407498380108	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/EventEmitters.cpp.o	dc0e73e4b1d5ed30
7390	12925	7734407556944622	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSModalScreenShadowNode.cpp.o	a8c066212ad8a272
8913	13167	7734407559517573	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0e8eb62b16777f5f0cc7c33df312f9ed/react/renderer/components/rnscreens/RNSScreenState.cpp.o	b6120e8f43e54eb
19860	20336	7734407631249280	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86/libappmodules.so	12845fb80aaad5c4
6295	11190	7734407539666107	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2925fb9a73caffbf95f70fc9e227ae4e/codegen/jni/safeareacontext-generated.cpp.o	c39d39c43d045ffd
7324	11901	7734407546746665	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	4c37ee8813cb5826
7613	12837	7734407556252389	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	28fdf4c266a13b6c
11901	14955	7734407577541809	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	722b0a9882c565e0
7057	12402	7734407551733113	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	2e00035f102180b2
5952	12177	7734407549533185	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/62033d5dc1295738601f79d2e301905f/renderer/components/safeareacontext/Props.cpp.o	5ecadddc40390a4d
7311	12487	7734407552411648	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	c51c5ef5cfac274e
7590	12879	7734407556453564	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3bba978b2157660a9a8f04f82fd394ad/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	10dda0d23a5433d1
0	33	0	clean	783ee98371267ada
278	9200	7734407519014556	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/ComponentDescriptors.cpp.o	ea41b979feb026a6
12177	13033	7734407557751308	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86/libreact_codegen_safeareacontext.so	66480d6895b2f546
9308	15809	7734407585846314	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	ce6572ef6de62933
9200	12236	7734407550184133	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/States.cpp.o	a9decd5943f47b44
10314	15691	7734407584899766	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	2b1626164dff2cea
12837	15191	7734407579799896	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	88708a4cb341cfd9
12092	15613	7734407584105768	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	a7a3674fdf0946d0
12487	15956	7734407587565746	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	decd886f93f154b3
12866	15865	7734407586661018	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o	94ce051deb4de4db
11794	15834	7734407586285249	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	a0e0edaf1024819f
16564	16752	7734407595418675	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86/libreact_codegen_rnscreens.so	320f80b2ac286dbf
0	30	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86/CMakeFiles/cmake.verify_globs	bd4379764f1cfde4
181	9125	7734432346007295	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	941c1e45fec645f9
230	9207	7734432345831965	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	e12595e6304471de
201	9229	7734432347690209	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	b8fa295fdd34ef61
9	9249	7734432347515608	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	c5ddd76f59905204
271	9260	7734432352402631	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/EventEmitters.cpp.o	dc0e73e4b1d5ed30
222	9642	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	517b3ed1de2e3d77
188	9663	7734432356270669	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	5fe556bdf83b026
194	9692	7734432356450435	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	ecc342708ed150c4
238	9758	7734432357259304	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	63c7de76a21f60c9
171	9926	7734432358834868	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	63d70d8f0e7fc89e
246	9975	7734432359737642	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	51ea157edd4e9c34
165	10006	7734432359747599	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	328ae48e2c99dc86
262	10110	7734432360874867	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/72f994d6b4d4ae1617cc1e36268935c9/safeareacontext/RNCSafeAreaViewState.cpp.o	74d2175769205f06
175	10384	7734432363425061	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	41762e27a70e4f
282	10603	7734432365872516	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/62033d5dc1295738601f79d2e301905f/renderer/components/safeareacontext/Props.cpp.o	5ecadddc40390a4d
208	11271	7734432371924959	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	e2342280feeedaf
254	11609	7734432375910426	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/72f994d6b4d4ae1617cc1e36268935c9/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	3362ad819f1af562
215	11835	7734432377415365	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o	c5ae5f5311aa23bc
9250	13084	7734432389757134	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/States.cpp.o	174d6a2385d50598
10604	14292	7734432401819023	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/States.cpp.o	a9decd5943f47b44
9643	15114	7734432406567128	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	4c37ee8813cb5826
9758	15126	7734432406713874	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	68c28154df0f800a
9208	15142	7734432407682580	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/safeareacontextJSI-generated.cpp.o	564ef381e0c8abf1
9230	15155	7734432407909503	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/2925fb9a73caffbf95f70fc9e227ae4e/codegen/jni/safeareacontext-generated.cpp.o	c39d39c43d045ffd
9261	15179	7734432410871874	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/ShadowNodes.cpp.o	3958802218f6c458
9927	15231	7734432411609431	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	28fdf4c266a13b6c
9664	15323	7734432412775009	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSModalScreenShadowNode.cpp.o	a8c066212ad8a272
9976	15367	7734432413180608	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	2e00035f102180b2
9692	15401	7734432413511155	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3bba978b2157660a9a8f04f82fd394ad/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	10dda0d23a5433d1
10007	15470	7734432414429286	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	c51c5ef5cfac274e
10111	15639	7734432415924252	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	fce44a0299b1f502
9128	16840	7734432427254405	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/ComponentDescriptors.cpp.o	ea41b979feb026a6
11835	17350	7734432433128075	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	2b1626164dff2cea
16840	17781	7734432436691719	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86/libreact_codegen_safeareacontext.so	66480d6895b2f546
11271	17913	7734432438640318	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/11247e6eb8d2ed237e1fbab4e82a70d7/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	b9263bf40ecb3db
15401	18209	7734432442182355	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	88708a4cb341cfd9
15143	18243	7734432442440870	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	722b0a9882c565e0
11609	18267	7734432442538236	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	ce6572ef6de62933
13087	18348	7734432443549501	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0e8eb62b16777f5f0cc7c33df312f9ed/react/renderer/components/rnscreens/RNSScreenState.cpp.o	b6120e8f43e54eb
15183	18593	7734432445989104	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	bb63a1c8c67f090c
15236	18604	7734432446139876	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	69c67b2140553f82
10385	18635	7734432446109567	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/ComponentDescriptors.cpp.o	3ea19a6bb548673a
15126	18664	7734432446705156	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	a7a3674fdf0946d0
15368	18780	7734432447882798	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o	94ce051deb4de4db
18635	18852	7734432448425650	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86/libreact_codegen_rnscreens.so	320f80b2ac286dbf
15323	18970	7734432449777091	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	decd886f93f154b3
14294	18996	7734432449756398	CMakeFiles/appmodules.dir/OnLoad.cpp.o	41bd02e1cae99110
15155	19071	7734432450789563	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	a0e0edaf1024819f
15115	23155	7734432491155324	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	8129f37e9673171d
23155	23662	7734432496543269	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86/libappmodules.so	12845fb80aaad5c4
1	45	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86/CMakeFiles/cmake.verify_globs	bd4379764f1cfde4
11	1775	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	517b3ed1de2e3d77
1775	2219	7734433980363042	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86/libappmodules.so	12845fb80aaad5c4
2	40	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86/CMakeFiles/cmake.verify_globs	bd4379764f1cfde4
0	49	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86/CMakeFiles/cmake.verify_globs	bd4379764f1cfde4
11	2396	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	517b3ed1de2e3d77
2397	3124	7734437112072075	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86/libappmodules.so	12845fb80aaad5c4
1	37	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86/CMakeFiles/cmake.verify_globs	bd4379764f1cfde4
19	2078	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	517b3ed1de2e3d77
2078	2546	7734438031135806	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86/libappmodules.so	12845fb80aaad5c4
1	48	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86/CMakeFiles/cmake.verify_globs	bd4379764f1cfde4
15	2060	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	517b3ed1de2e3d77
2060	2539	7734438540256847	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86/libappmodules.so	12845fb80aaad5c4
2	37	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86/CMakeFiles/cmake.verify_globs	bd4379764f1cfde4
15	1889	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	517b3ed1de2e3d77
1889	2349	7734443382880078	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86/libappmodules.so	12845fb80aaad5c4
2	38	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86/CMakeFiles/cmake.verify_globs	bd4379764f1cfde4
17	1796	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	517b3ed1de2e3d77
1796	2246	7734449542690703	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86/libappmodules.so	12845fb80aaad5c4
2	40	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86/CMakeFiles/cmake.verify_globs	bd4379764f1cfde4
14	1847	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	517b3ed1de2e3d77
1847	2310	7734450082683461	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86/libappmodules.so	12845fb80aaad5c4
1	40	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86/CMakeFiles/cmake.verify_globs	bd4379764f1cfde4
10	1771	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	517b3ed1de2e3d77
1772	2216	7734450422628620	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/x86/libappmodules.so	12845fb80aaad5c4
