# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 509ms
  generate-prefab-packages
    [gap of 23ms]
    exec-prefab 1284ms
    [gap of 29ms]
  generate-prefab-packages completed in 1336ms
  execute-generate-process
    exec-configure 3184ms
    [gap of 444ms]
  execute-generate-process completed in 3630ms
  [gap of 73ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 5581ms

