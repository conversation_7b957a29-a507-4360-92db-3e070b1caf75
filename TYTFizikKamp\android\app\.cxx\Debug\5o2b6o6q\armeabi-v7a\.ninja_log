# ninja log v5
394	6859	7735953828303803	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewShadowNode.cpp.o	c15ee8ccb7258870
1	40	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/armeabi-v7a/CMakeFiles/cmake.verify_globs	8ffef1f3d322adb7
381	3822	7735953797483413	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	a245547fab90f661
330	4548	7735953805012405	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	5a2f613695f0af8a
41	1202	7736085211348050	build.ninja	2ac3fa7fa6067b70
15	4146	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	ee1bb106f06a719d
298	4091	7735953800599366	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	76189a92c655b7bf
292	3553	7735953795212546	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	d42d8573a3dd8a99
183	2293	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	e6e526e27cb182e9
314	4768	7735953807190938	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	afa2d93e51a7dc07
322	5031	7735953809575014	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	958314ebe66daccc
285	4595	7735953805366124	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	c17885b8fea8971e
355	5165	7735953811341579	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	b70ea2d2bb34a2c4
4091	8886	7735953848183162	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/ShadowNodes.cpp.o	8ad3ecb428e1cb72
372	5381	7735953813450612	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	c9c7b0027c726edb
48	5410	7735953813381822	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	7f00b8c957deb9c2
5872	11242	7735953872018017	CMakeFiles/appmodules.dir/OnLoad.cpp.o	2f6b3abc9b7e019a
190	2318	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	2a67ce48bf5ec03d
8933	12717	7735953887070442	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	71b4f4233085d75a
305	5363	7735953812909724	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	7ac57e0aadbe490f
3822	8013	7735953839341935	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/122f554796c7013a174ee08c2d9db3af/jni/safeareacontext-generated.cpp.o	2e5621ae5158533a
5602	17351	7735953932583990	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	e0bfc9f2ab890c08
4548	7798	7735953837013919	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/States.cpp.o	9801f6c24e43db02
3553	7581	7735953835505373	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/EventEmitters.cpp.o	83c2ea12d0fd7ec7
9505	12734	7735953887257982	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	63b4f9c4d37b7fb4
464	5872	7735953818123589	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/Props.cpp.o	ab501d31e9232666
337	6381	7735953823093791	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	3304d65d6b6118e9
7799	12067	7735953880451417	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3bba978b2157660a9a8f04f82fd394ad/renderer/components/rnscreens/RNSScreenState.cpp.o	d58f7d6f895a49f6
4595	8500	7735953844671783	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/safeareacontextJSI-generated.cpp.o	88aac8f4b2e8b573
4777	8932	7735953848928701	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	9f36b3e40f2676fd
411	5602	7735953815428337	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewState.cpp.o	99fb15bddb2fe77b
6974	10311	7735953862322957	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d2daae902f606f04fd11325c49840ca/react/renderer/components/rnscreens/States.cpp.o	418f7d43ffa196e5
5031	9731	7735953857045950	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	6060d1b2f6a2310d
7581	13641	7735953896180552	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/EventEmitters.cpp.o	24dff3fb5af98018
5363	10423	7735953863744845	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/916645878bb7abfd2658caabb8c5b1ed/RNSScreenStackHeaderSubviewShadowNode.cpp.o	5539efa09717e274
5382	9505	7735953854732143	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	71d23bff2f47ad2
5411	10273	7735953861536223	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSModalScreenShadowNode.cpp.o	a68b8faacbedba05
6382	11342	7735953873060885	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/ShadowNodes.cpp.o	99d6ec848f484052
5165	10240	7735953861536223	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSScreenShadowNode.cpp.o	63ecff45876944d5
430	6777	7735953826652211	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/ComponentDescriptors.cpp.o	ffd68370c5ae9a75
4769	9949	7735953859239238	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/916645878bb7abfd2658caabb8c5b1ed/RNSScreenStackHeaderConfigShadowNode.cpp.o	a7c0a91e3bfd9538
9950	12679	7735953886694492	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	a0f7258db7bcc7f5
8013	13049	7735953890427447	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	7d5643c32b88a3bd
8892	9600	7735953854712008	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libreact_codegen_safeareacontext.so	5dfef72318aa73ab
9731	13250	7735953892190933	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/rnscreensJSI-generated.cpp.o	b72ce16ab0aa9666
14312	14519	7735953905013931	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libreact_codegen_rnscreens.so	21be0b29de88a7f2
9600	13339	7735953893333224	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	fdbcac360ad84274
6777	12822	7735953887965927	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/Props.cpp.o	6d8ca6ed804c6fc5
10276	13723	7735953897159807	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	1917e03e9bca56fb
8501	12983	7735953889764289	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/ComponentDescriptors.cpp.o	a4952e2b9de7335f
6859	14312	7735953902809171	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/ComponentDescriptors.cpp.o	26d2ec23e42b94bd
4146	4632	7736062312841985	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libappmodules.so	4ce2ecdb6b0f3c03
0	30	0	clean	783ee98371267ada
1	42	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/armeabi-v7a/CMakeFiles/cmake.verify_globs	8ffef1f3d322adb7
236	12154	7736085331806324	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	d42d8573a3dd8a99
286	12249	7736085331715512	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	a245547fab90f661
12163	14012	7736085354402167	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/7f9d75c760005477a9a92acb25e45305/EventEmitters.cpp.o	111ce08aac21e02
260	14530	7736085357882558	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	5a2f613695f0af8a
12261	14708	7736085361323787	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/7f9d75c760005477a9a92acb25e45305/ComponentDescriptors.cpp.o	66b51d7f2f6126cd
14012	16000	7736085374232491	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/Props.cpp.o	8ce742bb624ccde3
210	16846	7736085380789455	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	76189a92c655b7bf
14531	16949	7736085382448758	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/7f9d75c760005477a9a92acb25e45305/ShadowNodes.cpp.o	25b3e839699b1cc9
14709	17081	7736085383870437	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewState.cpp.o	99fb15bddb2fe77b
16881	18387	7736085398158734	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/States.cpp.o	bc74d892a1cc2dcd
16000	19173	7736085404454568	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewShadowNode.cpp.o	c15ee8ccb7258870
17092	19599	7736085410228916	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/ShadowNodes.cpp.o	8ad3ecb428e1cb72
18387	19892	7736085413204156	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/States.cpp.o	9801f6c24e43db02
340	21241	0	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/RNHapticFeedbackSpecJSI-generated.cpp.o	35551888acbc9275
19190	21317	7736085425714116	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/safeareacontextJSI-generated.cpp.o	88aac8f4b2e8b573
19599	21340	7736085427307611	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/EventEmitters.cpp.o	83c2ea12d0fd7ec7
297	21936	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	e6e526e27cb182e9
245	22215	7736085434214478	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	afa2d93e51a7dc07
222	22513	7736085437718930	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	c17885b8fea8971e
19892	22580	7736085438500732	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/Props.cpp.o	ab501d31e9232666
16950	23111	7736085442726861	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/ComponentDescriptors.cpp.o	ffd68370c5ae9a75
319	23664	7736085449489646	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/RNHapticFeedbackSpec-generated.cpp.o	e4b9e9d4b767401f
21242	23698	7736085450609128	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/122f554796c7013a174ee08c2d9db3af/jni/safeareacontext-generated.cpp.o	2e5621ae5158533a
331	23944	7736085452450804	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	b70ea2d2bb34a2c4
252	23993	7736085452791461	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	958314ebe66daccc
23698	24131	7736085455313746	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libreact_codegen_safeareacontext.so	5dfef72318aa73ab
21318	24233	7736085456511695	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/916645878bb7abfd2658caabb8c5b1ed/RNSScreenStackHeaderConfigShadowNode.cpp.o	a7c0a91e3bfd9538
21341	24247	7736085456616448	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSModalScreenShadowNode.cpp.o	a68b8faacbedba05
278	24749	7736085461220562	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	c9c7b0027c726edb
230	24881	7736085462379872	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	7ac57e0aadbe490f
22514	25236	7736085466552194	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	9f36b3e40f2676fd
216	25569	7736085469188556	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	7f00b8c957deb9c2
22223	25687	7736085470453130	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	6060d1b2f6a2310d
21936	25905	7736085472482057	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSScreenShadowNode.cpp.o	63ecff45876944d5
25	27103	7736085484116499	CMakeFiles/appmodules.dir/OnLoad.cpp.o	b83d9aabf46d6824
24757	27435	7736085488572296	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	63b4f9c4d37b7fb4
25236	27458	7736085488747303	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	a0f7258db7bcc7f5
22588	27693	7736085489905799	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/916645878bb7abfd2658caabb8c5b1ed/RNSScreenStackHeaderSubviewShadowNode.cpp.o	5539efa09717e274
23945	27986	7736085492854524	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	71d23bff2f47ad2
25587	28320	7736085497334957	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	71b4f4233085d75a
25693	28511	7736085499242529	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/rnscreensJSI-generated.cpp.o	b72ce16ab0aa9666
24247	28722	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	2a67ce48bf5ec03d
25908	28796	7736085501130220	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	fdbcac360ad84274
24131	29033	7736085503459575	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3bba978b2157660a9a8f04f82fd394ad/renderer/components/rnscreens/RNSScreenState.cpp.o	d58f7d6f895a49f6
27436	29055	7736085504813375	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d2daae902f606f04fd11325c49840ca/react/renderer/components/rnscreens/States.cpp.o	418f7d43ffa196e5
27108	29630	7736085510501964	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/ShadowNodes.cpp.o	99d6ec848f484052
27458	29841	7736085512679860	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/ComponentDescriptors.cpp.o	a4952e2b9de7335f
24884	30136	7736085514807201	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	1917e03e9bca56fb
306	30449	7736085516900256	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	3304d65d6b6118e9
268	34417	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	ee1bb106f06a719d
23665	34429	7736085557507945	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	7d5643c32b88a3bd
24233	36542	7736085578318114	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/Props.cpp.o	6d8ca6ed804c6fc5
23994	38516	7736085598281369	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/EventEmitters.cpp.o	24dff3fb5af98018
23112	41922	7736085632218567	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/ComponentDescriptors.cpp.o	26d2ec23e42b94bd
41923	42144	7736085635547698	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libreact_codegen_rnscreens.so	21be0b29de88a7f2
203	53111	7736085742969369	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	98ad7ab0063bdce6
53112	53662	7736085750706815	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libappmodules.so	85f2f4f15be557f9
1	32	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/armeabi-v7a/CMakeFiles/cmake.verify_globs	8ffef1f3d322adb7
183	2020	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	2a67ce48bf5ec03d
176	2026	0	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/RNHapticFeedbackSpecJSI-generated.cpp.o	35551888acbc9275
164	2039	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	e6e526e27cb182e9
10	3107	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	ee1bb106f06a719d
3107	3559	7736089024179311	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libappmodules.so	85f2f4f15be557f9
1	39	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/armeabi-v7a/CMakeFiles/cmake.verify_globs	8ffef1f3d322adb7
171	2085	0	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/RNHapticFeedbackSpecJSI-generated.cpp.o	35551888acbc9275
167	2126	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	e6e526e27cb182e9
177	2130	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	2a67ce48bf5ec03d
9	3379	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	ee1bb106f06a719d
3380	3852	7736092528770359	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libappmodules.so	85f2f4f15be557f9
