# ninja log v5
356	13364	7734407278087430	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewShadowNode.cpp.o	c15ee8ccb7258870
16	82	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/armeabi-v7a/CMakeFiles/cmake.verify_globs	8ffef1f3d322adb7
330	10592	7734407246815072	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	a245547fab90f661
314	10581	7734407249267980	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	5a2f613695f0af8a
306	13646	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	ee1bb106f06a719d
233	10654	7734407251200011	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	76189a92c655b7bf
270	10503	7734407246752496	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	d42d8573a3dd8a99
346	11426	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	e6e526e27cb182e9
290	11404	7734407258429383	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	afa2d93e51a7dc07
280	11415	7734407258643101	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	958314ebe66daccc
262	11152	7734407256350531	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	c17885b8fea8971e
338	11391	7734407258672995	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	b70ea2d2bb34a2c4
11153	16896	7734407313548612	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/ShadowNodes.cpp.o	8ad3ecb428e1cb72
300	11846	7734407263277600	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	c9c7b0027c726edb
59	12402	7734407268703562	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	7f00b8c957deb9c2
12402	18267	7734407327267317	CMakeFiles/appmodules.dir/OnLoad.cpp.o	2f6b3abc9b7e019a
17120	20631	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	2a67ce48bf5ec03d
17083	20525	7734407350396725	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	71b4f4233085d75a
255	11836	7734407262859911	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	7ac57e0aadbe490f
10654	15817	7734407302829689	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/122f554796c7013a174ee08c2d9db3af/jni/safeareacontext-generated.cpp.o	2e5621ae5158533a
12596	23953	7734407384061316	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	e0bfc9f2ab890c08
10593	14368	7734407288524565	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/States.cpp.o	9801f6c24e43db02
10516	15372	7734407298215284	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/EventEmitters.cpp.o	83c2ea12d0fd7ec7
16986	20313	7734407348241391	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	63b4f9c4d37b7fb4
374	12596	7734407270174333	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/Props.cpp.o	ab501d31e9232666
321	13174	7734407275545829	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	3304d65d6b6118e9
15668	20068	7734407345757575	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3bba978b2157660a9a8f04f82fd394ad/renderer/components/rnscreens/RNSScreenState.cpp.o	d58f7d6f895a49f6
10582	15667	7734407300555105	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/safeareacontextJSI-generated.cpp.o	88aac8f4b2e8b573
11392	15885	7734407301978124	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	9f36b3e40f2676fd
367	11725	7734407262178190	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewState.cpp.o	99fb15bddb2fe77b
13364	16986	7734407314455662	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d2daae902f606f04fd11325c49840ca/react/renderer/components/rnscreens/States.cpp.o	418f7d43ffa196e5
11415	17082	7734407315151746	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	6060d1b2f6a2310d
13646	20664	7734407351508977	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/EventEmitters.cpp.o	24dff3fb5af98018
11847	17500	7734407319692366	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/916645878bb7abfd2658caabb8c5b1ed/RNSScreenStackHeaderSubviewShadowNode.cpp.o	5539efa09717e274
11427	16048	7734407304873023	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	71d23bff2f47ad2
11404	17095	7734407315161709	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSModalScreenShadowNode.cpp.o	a68b8faacbedba05
14369	19629	7734407341153606	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/ShadowNodes.cpp.o	99d6ec848f484052
11726	17256	7734407317466944	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSScreenShadowNode.cpp.o	63ecff45876944d5
383	13537	7734407279779346	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/ComponentDescriptors.cpp.o	ffd68370c5ae9a75
11836	17119	7734407316119252	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/916645878bb7abfd2658caabb8c5b1ed/RNSScreenStackHeaderConfigShadowNode.cpp.o	a7c0a91e3bfd9538
17257	19969	7734407344606981	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	a0f7258db7bcc7f5
13538	19471	7734407339666944	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	7d5643c32b88a3bd
16896	18210	7734407324748397	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libreact_codegen_safeareacontext.so	5dfef72318aa73ab
15817	20041	7734407345464149	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/rnscreensJSI-generated.cpp.o	b72ce16ab0aa9666
22580	22794	7734407372925290	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libreact_codegen_rnscreens.so	21be0b29de88a7f2
15886	20279	7734407347871794	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	fdbcac360ad84274
13175	20032	7734407345170726	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/Props.cpp.o	6d8ca6ed804c6fc5
17095	20856	7734407353656304	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	1917e03e9bca56fb
16049	20583	7734407350912848	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/ComponentDescriptors.cpp.o	a4952e2b9de7335f
15373	22579	7734407370634976	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/ComponentDescriptors.cpp.o	26d2ec23e42b94bd
23953	24459	7734407389512697	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libappmodules.so	4ce2ecdb6b0f3c03
1	36	0	clean	783ee98371267ada
2	55	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/armeabi-v7a/CMakeFiles/cmake.verify_globs	8ffef1f3d322adb7
388	10186	7734431785250951	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	d42d8573a3dd8a99
456	10371	7734431788061468	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	a245547fab90f661
370	10651	7734431791017810	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	76189a92c655b7bf
409	10875	7734431793400277	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	5a2f613695f0af8a
357	11268	7734431796770002	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	c17885b8fea8971e
488	11294	7734431797674480	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/EventEmitters.cpp.o	83c2ea12d0fd7ec7
442	11661	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	e6e526e27cb182e9
396	11722	7734431801178918	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	afa2d93e51a7dc07
472	11835	7734431802904958	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewState.cpp.o	99fb15bddb2fe77b
402	11901	7734431803303678	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	958314ebe66daccc
449	11945	7734431804262057	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	b70ea2d2bb34a2c4
425	12035	7734431804931891	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	c9c7b0027c726edb
44	12060	7734431804862084	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	7f00b8c957deb9c2
382	12495	7734431809696572	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	7ac57e0aadbe490f
480	13523	7734431819110520	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/ComponentDescriptors.cpp.o	ffd68370c5ae9a75
418	13588	7734431819694300	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	3304d65d6b6118e9
464	13624	7734431820853061	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/cd8323a85879d5bd064a8196cd25b5e6/RNCSafeAreaViewShadowNode.cpp.o	c15ee8ccb7258870
434	13866	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	ee1bb106f06a719d
11268	14795	7734431832223905	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/States.cpp.o	9801f6c24e43db02
10875	15953	7734431844281342	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/122f554796c7013a174ee08c2d9db3af/jni/safeareacontext-generated.cpp.o	2e5621ae5158533a
10372	15983	7734431844281342	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/124ba7c4e4a74044bc36b9fdbad60e13/safeareacontextJSI-generated.cpp.o	88aac8f4b2e8b573
10652	16502	7734431849608055	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/83bf4ddbfbdb8ad58bae843c69c91af0/safeareacontext/ShadowNodes.cpp.o	8ad3ecb428e1cb72
12037	16521	7734431849628155	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	71d23bff2f47ad2
11723	16531	7734431850175927	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	9f36b3e40f2676fd
10192	17259	7734431856071442	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3f427bb2421a519cc9b4064988b2a05f/components/safeareacontext/Props.cpp.o	ab501d31e9232666
11946	17346	7734431858049129	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8a6ddb5a54ca9276a2c3566235219933/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	6060d1b2f6a2310d
13624	17418	7734431858304621	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d2daae902f606f04fd11325c49840ca/react/renderer/components/rnscreens/States.cpp.o	418f7d43ffa196e5
11901	17453	7734431858439781	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/916645878bb7abfd2658caabb8c5b1ed/RNSScreenStackHeaderSubviewShadowNode.cpp.o	5539efa09717e274
11661	17462	7734431858449804	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSModalScreenShadowNode.cpp.o	a68b8faacbedba05
11835	17473	7734431859365392	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/916645878bb7abfd2658caabb8c5b1ed/RNSScreenStackHeaderConfigShadowNode.cpp.o	a7c0a91e3bfd9538
11298	17486	7734431859334531	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5c69d5b48f533dee703d78324487dd3c/components/rnscreens/RNSScreenShadowNode.cpp.o	63ecff45876944d5
12061	18220	7734431864546324	CMakeFiles/appmodules.dir/OnLoad.cpp.o	2f6b3abc9b7e019a
13866	18623	7734431870908433	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3bba978b2157660a9a8f04f82fd394ad/renderer/components/rnscreens/RNSScreenState.cpp.o	d58f7d6f895a49f6
17260	18653	7734431870519547	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libreact_codegen_safeareacontext.so	5dfef72318aa73ab
13588	20211	7734431884627187	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	7d5643c32b88a3bd
14799	20905	7734431893846559	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/ShadowNodes.cpp.o	99d6ec848f484052
17463	20928	7734431894244070	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	a0f7258db7bcc7f5
16532	21164	7734431896324232	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/rnscreensJSI-generated.cpp.o	b72ce16ab0aa9666
17347	21351	7734431897946103	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	63b4f9c4d37b7fb4
13524	21356	7734431896815132	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a1c78db5a0e76f3fe1756b74aceb4eb5/jni/react/renderer/components/rnscreens/Props.cpp.o	6d8ca6ed804c6fc5
16503	21561	7734431900237744	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	fdbcac360ad84274
16521	21659	7734431901423488	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/ComponentDescriptors.cpp.o	a4952e2b9de7335f
17474	21659	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	2a67ce48bf5ec03d
17427	21804	7734431902966931	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	71b4f4233085d75a
17453	22130	7734431906061320	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	1917e03e9bca56fb
15953	22899	7734431913693885	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97980dd7a14377b8ce199f54d726612b/renderer/components/rnscreens/EventEmitters.cpp.o	24dff3fb5af98018
15984	24162	7734431926274804	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/93c3ad1a21fe4e0c5f387f6414ab8793/components/rnscreens/ComponentDescriptors.cpp.o	26d2ec23e42b94bd
24162	24508	7734431929854742	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libreact_codegen_rnscreens.so	21be0b29de88a7f2
2	53	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/armeabi-v7a/CMakeFiles/cmake.verify_globs	8ffef1f3d322adb7
179	2378	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	2a67ce48bf5ec03d
166	2385	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	e6e526e27cb182e9
10	3537	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	ee1bb106f06a719d
171	7411	7734432234519756	CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	e0bfc9f2ab890c08
7411	8019	7734432241008798	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libappmodules.so	4ce2ecdb6b0f3c03
1	54	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/armeabi-v7a/CMakeFiles/cmake.verify_globs	8ffef1f3d322adb7
178	2304	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	2a67ce48bf5ec03d
173	2304	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	e6e526e27cb182e9
16	3421	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	ee1bb106f06a719d
3421	4099	7734433956892196	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libappmodules.so	4ce2ecdb6b0f3c03
3	56	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/armeabi-v7a/CMakeFiles/cmake.verify_globs	8ffef1f3d322adb7
193	2347	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	2a67ce48bf5ec03d
188	2378	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	e6e526e27cb182e9
24	3772	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	ee1bb106f06a719d
3772	4257	7734436831769131	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libappmodules.so	4ce2ecdb6b0f3c03
1	54	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/armeabi-v7a/CMakeFiles/cmake.verify_globs	8ffef1f3d322adb7
173	2316	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	2a67ce48bf5ec03d
168	2317	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	e6e526e27cb182e9
11	3493	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	ee1bb106f06a719d
3493	4164	7734437079444906	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libappmodules.so	4ce2ecdb6b0f3c03
2	57	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/armeabi-v7a/CMakeFiles/cmake.verify_globs	8ffef1f3d322adb7
184	2276	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	e6e526e27cb182e9
191	2426	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	2a67ce48bf5ec03d
19	3623	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	ee1bb106f06a719d
3623	4093	7734438004176013	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libappmodules.so	4ce2ecdb6b0f3c03
1	66	0	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/armeabi-v7a/CMakeFiles/cmake.verify_globs	8ffef1f3d322adb7
170	3646	0	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	2a67ce48bf5ec03d
166	3646	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	e6e526e27cb182e9
10	4850	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	ee1bb106f06a719d
4851	5489	7734438513459181	C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/build/intermediates/cxx/Debug/5o2b6o6q/obj/armeabi-v7a/libappmodules.so	4ce2ecdb6b0f3c03
