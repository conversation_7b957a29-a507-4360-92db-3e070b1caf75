[{"id": 1, "gun": 1, "konu": "Fizik Bilimine Giriş", "video_link": "htt0ps://youtu.be/abc123", "ozet": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ını inceleyen bilim dalıdır. Temel kavramlar: enerji, hareket, kuvvet, madde ve bunların etkileşimleri. Fizik bilimi sayesinde evrendeki olayları anlayabilir ve teknolojik gelişmelere katkıda bulunabiliriz.", "gercek_hayat": "Telefonla konuşmak → Ses dalgalarının fiziği! Arabanın frenlenmesi → Sürtünme kuvveti!", "geri_baglanti": null, "sorular": [{"id": 1, "soru": "Aşağıdakilerden hangisi fizik biliminin konusudur?", "secenekler": ["A) Şiir yaz<PERSON>", "B) <PERSON><PERSON><PERSON> ve hareket", "C) <PERSON><PERSON><PERSON>", "D) <PERSON><PERSON><PERSON>"], "cevap": "B", "aciklama": "<PERSON><PERSON><PERSON>, enerji ve hareket gibi doğa olaylarını inceler.", "isAnswered": false}, {"id": 2, "soru": "Fizik biliminin temel amacı nedir?", "secenekler": ["A) Para kazanmak", "B) Doğa olaylarını anlamak", "C) <PERSON><PERSON> yapmak", "D) S<PERSON> ya<PERSON>ak"], "cevap": "B", "aciklama": "Fizik biliminin temel amacı doğa olaylarını anlamak ve açıklamaktır.", "isAnswered": false}, {"id": 3, "soru": "Hangi teknoloji fizik biliminin bir ürünüdür?", "secenekler": ["A) Cep telefonu", "B) Şiir kitabı", "C) <PERSON><PERSON><PERSON> tab<PERSON>u", "D) Müzik aleti"], "cevap": "A", "aciklama": "Cep telefonu, elektromanyetik dalgalar gibi fizik prensipleriyle çalışır.", "isAnswered": false}, {"id": 4, "soru": "Fizik biliminde ölçüm neden önemlidir?", "secenekler": ["A) Zaman geçirmek için", "B) <PERSON><PERSON> elde etmek için", "C) Eğlenmek için", "D) Yarışmak için"], "cevap": "B", "aciklama": "Ölçümler sayesinde fiziksel olayları kesin sayılarla ifade edebiliriz.", "isAnswered": false}, {"id": 5, "soru": "Fizik bilimi hangi alanlarla ilişkilidir?", "secenekler": ["A) <PERSON><PERSON><PERSON>", "B) <PERSON><PERSON><PERSON>k, kimya, biyo<PERSON>ji", "C) <PERSON><PERSON><PERSON>h", "D) <PERSON><PERSON><PERSON> sanat"], "cevap": "B", "aciklama": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, kimya ve biyoloji gibi birçok bilim dalıyla yakından ilişkilidir.", "isAnswered": false}], "motivasyon": "🚀 İlk adımı attın! Evreni anlamaya başlıyorsun.", "karnesi": {"cozulen_soru": 0, "zorluk": "<PERSON><PERSON> ✅", "geri_bildirim": "Temelleri kavramaya ba<PERSON>ladın!"}, "isUnlocked": true, "isCompleted": false}, {"id": 2, "gun": 2, "konu": "Madde ve Özellikleri", "video_link": "https://youtu.be/xyz456", "ozet": "Maddenin halleri: kat<PERSON>, sı<PERSON><PERSON>, gaz ve plazma. Her halin kendine özgü özellikleri vardır. Özkütle, es<PERSON><PERSON><PERSON>, sertlik gibi fiziksel özellikler maddeleri birbirinden ayırır.", "gercek_hayat": "Buzun suda yüzmesi → Özkütle farkı! Lastik topun zıplaması → Esneklik özelliği!", "geri_baglanti": {"soru": "<PERSON>zik bilimi neyi inceler?", "cevap": "Do<PERSON>a o<PERSON>larını", "ipucu": "Dünkü konunun temel tanımıydı!"}, "sorular": [{"id": 6, "soru": "<PERSON>şağ<PERSON>dakilerden hangisi maddenin hallerinden değildir?", "secenekler": ["A) Katı", "B) Sıvı", "C) Gaz", "D) Işık"], "cevap": "D", "aciklama": "<PERSON><PERSON><PERSON><PERSON>, ener<PERSON><PERSON>. <PERSON><PERSON><PERSON> halleri katı, sı<PERSON><PERSON>, gaz ve plazmadır.", "isAnswered": false}, {"id": 7, "soru": "Özkütle nedir?", "secenekler": ["A) <PERSON><PERSON><PERSON>", "B) <PERSON><PERSON><PERSON> haci<PERSON>de bulunan k<PERSON>tle", "C) <PERSON><PERSON>in s<PERSON>lığı", "D) <PERSON><PERSON><PERSON>"], "cevap": "B", "aciklama": "<PERSON><PERSON><PERSON><PERSON><PERSON>, birim hacimde bulunan kütle miktarıdır (d = m/V).", "isAnswered": false}, {"id": 8, "soru": "<PERSON>i madde halinde <PERSON> en düzenli dizilmiştir?", "secenekler": ["A) Gaz", "B) Sıvı", "C) Katı", "D) Plazma"], "cevap": "C", "aciklama": "Kat<PERSON> halde moleküller en düzenli şekilde dizilmiştir.", "isAnswered": false}, {"id": 9, "soru": "Suyun 0°C'de donması hangi özelliğinin değişmesidir?", "secenekler": ["A) <PERSON><PERSON><PERSON><PERSON>", "B) <PERSON>i", "C) <PERSON><PERSON>", "D) Kokusu"], "cevap": "B", "aciklama": "Su 0°C'de sıvı halden katı hale geçer.", "isAnswered": false}, {"id": 10, "soru": "Esneklik özelliği nedir?", "secenekler": ["A) <PERSON><PERSON><PERSON><PERSON><PERSON>", "B) Deformasyondan sonra eski haline dönme", "C) Renk değiştirme", "D) Büyüme <PERSON>"], "cevap": "B", "aciklama": "<PERSON><PERSON><PERSON><PERSON><PERSON>, maddenin deformasyona uğradıktan sonra eski haline dönebilme özelliğidir.", "isAnswered": false}], "motivasyon": "🔬 Bilim insanı gibi düşünmeye başladın!", "karnesi": {"cozulen_soru": 0, "zorluk": "Orta 🟡", "geri_bildirim": "Madde konusunda biraz daha pratik yapmalısın."}, "isUnlocked": false, "isCompleted": false}, {"id": 3, "gun": 3, "konu": "<PERSON><PERSON><PERSON>", "video_link": "https://youtu.be/def789", "ozet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, vida, kama ve çıkr<PERSON>k basit makinelerdir. <PERSON>u makineler iş kolaylığı sağlar ve günlük hayatımızda sıkça kullanılır.", "gercek_hayat": "Bisiklet pedalı → Kaldıraç örneği! Merdiven → Eğik düzlem örneği!", "geri_baglanti": {"soru": "<PERSON><PERSON><PERSON> kaç hali vardır?", "cevap": "4 (<PERSON><PERSON>, sıvı, gaz, plazma)", "ipucu": "Dünkü konuyu hatırla!"}, "sorular": [{"id": 11, "soru": "Aşağıdakilerden hangisi basit makine değildir?", "secenekler": ["A) Kaldıraç", "B) Vida", "C) Bil<PERSON><PERSON><PERSON>", "D) Makara"], "cevap": "C", "aciklama": "Bilgisayar basit makine sınıfında değildir. Basit makineler: ka<PERSON><PERSON><PERSON><PERSON>, ma<PERSON>, <PERSON><PERSON><PERSON>, vida, kama, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "isAnswered": false}, {"id": 12, "soru": "<PERSON><PERSON><PERSON>rac<PERSON>n amacı nedir?", "secenekler": ["A) Süsleme", "B) Az kuvvetle çok iş yapmak", "C) Zaman geçirmek", "D) <PERSON><PERSON>"], "cevap": "B", "aciklama": "Ka<PERSON><PERSON><PERSON>ç, az kuvvet uygulayarak çok iş yapmamızı sağlar.", "isAnswered": false}, {"id": 13, "soru": "<PERSON><PERSON><PERSON><PERSON>n faydası nedir?", "secenekler": ["A) Renk değiştirme", "B) Kuvvet yönünü değiştirme", "C) <PERSON><PERSON>", "D) Işık verme"], "cevap": "B", "aciklama": "Ma<PERSON>, kuv<PERSON>in yönünü değiştirerek işimizi kolaylaştırır.", "isAnswered": false}, {"id": 14, "soru": "<PERSON><PERSON><PERSON> düzlem nerede kullanılır?", "secenekler": ["A) <PERSON><PERSON><PERSON>", "B) <PERSON><PERSON> ve merdivenler", "C) <PERSON><PERSON><PERSON>", "D) <PERSON><PERSON><PERSON>"], "cevap": "B", "aciklama": "<PERSON><PERSON><PERSON> düzlem rampa ve merdivenlerde kullanılarak yüksek yerlere çıkmayı kolaylaştırır.", "isAnswered": false}, {"id": 15, "soru": "Vida hangi basit makin<PERSON>rin bi<PERSON>imid<PERSON>?", "secenekler": ["A) Kaldıraç + Makara", "B) Eğik düzle<PERSON> + Silindir", "C) Kama + Çıkrık", "D) <PERSON><PERSON><PERSON> e<PERSON>"], "cevap": "B", "aciklama": "<PERSON><PERSON>, e<PERSON><PERSON> dü<PERSON>min silindir etrafına sarılmı<PERSON> halidir.", "isAnswered": false}], "motivasyon": "💡 Artık etrafındaki makineleri fark edeceksin!", "karnesi": {"cozulen_soru": 0, "zorluk": "<PERSON><PERSON> 🔴", "geri_bildirim": "Kaldıraçları tekrar et."}, "isUnlocked": false, "isCompleted": false}]