import StorageService from './StorageService';

export interface ValidationRules {
  max_daily_access: number;
  unlock_order: 'sequential';
  time_between_days: string; // "24h" format
  min_hours_between_completions: number;
}

class TimeValidationService {
  private static readonly VALIDATION_RULES: ValidationRules = {
    max_daily_access: 1,
    unlock_order: 'sequential',
    time_between_days: '24h',
    min_hours_between_completions: 6
  };

  // Kullanıcının belirli bir güne erişip erişemeyeceğini kontrol et
  static async canAccessDay(dayId: number): Promise<{
    canAccess: boolean;
    reason?: string;
    nextAvailableTime?: Date;
  }> {
    try {
      const userProgress = await StorageService.getUserProgress();
      const now = new Date();
      const lastAccessTime = new Date(userProgress.last_activity);

      // İlk gün her zaman erişilebilir
      if (dayId === 1) {
        return { canAccess: true };
      }

      // <PERSON>ı<PERSON><PERSON> eri<PERSON><PERSON> kontrolü
      const maxAccessibleDay = Math.max(...userProgress.completed_days, 1) + 1;
      if (dayId > maxAccessibleDay) {
        return {
          canAccess: false,
          reason: `Önce ${dayId - 1}. günü tamamlamalısın!`
        };
      }

      // Eğer gün zaten tamamlanmışsa erişilebilir (tekrar için)
      if (userProgress.completed_days.includes(dayId)) {
        return { canAccess: true };
      }

      // Aynı gün içinde farklı gün erişim kontrolü
      if (this.isSameDay(now, lastAccessTime)) {
        const lastActiveDay = userProgress.completed_days[userProgress.completed_days.length - 1] || 0;
        if (lastActiveDay !== dayId && !userProgress.completed_days.includes(lastActiveDay)) {
          return {
            canAccess: false,
            reason: 'Aynı gün içinde farklı bir günü açamazsın!'
          };
        }
      }

      // Minimum bekleme süresi kontrolü
      const hoursSinceLastAccess = (now.getTime() - lastAccessTime.getTime()) / (1000 * 60 * 60);
      if (hoursSinceLastAccess < this.VALIDATION_RULES.min_hours_between_completions) {
        const nextAvailableTime = new Date(
          lastAccessTime.getTime() + 
          (this.VALIDATION_RULES.min_hours_between_completions * 60 * 60 * 1000)
        );
        
        return {
          canAccess: false,
          reason: `${this.VALIDATION_RULES.min_hours_between_completions} saat beklemelisin!`,
          nextAvailableTime
        };
      }

      return { canAccess: true };
    } catch (error) {
      console.error('Error checking day access:', error);
      return {
        canAccess: false,
        reason: 'Bir hata oluştu!'
      };
    }
  }

  // Yeni gün tamamlama kontrolü
  static async canCompleteNewDay(): Promise<{
    canComplete: boolean;
    reason?: string;
    nextAvailableTime?: Date;
  }> {
    try {
      const userProgress = await StorageService.getUserProgress();
      const now = new Date();
      const lastAccessTime = new Date(userProgress.last_activity);

      // Aynı gün kontrolü - 6 saat kuralı
      const hoursSinceLastCompletion = (now.getTime() - lastAccessTime.getTime()) / (1000 * 60 * 60);
      
      if (hoursSinceLastCompletion < this.VALIDATION_RULES.min_hours_between_completions) {
        const nextAvailableTime = new Date(
          lastAccessTime.getTime() + 
          (this.VALIDATION_RULES.min_hours_between_completions * 60 * 60 * 1000)
        );
        
        return {
          canComplete: false,
          reason: `Yeni gün için ${this.VALIDATION_RULES.min_hours_between_completions} saat beklemelisin!`,
          nextAvailableTime
        };
      }

      return { canComplete: true };
    } catch (error) {
      console.error('Error checking completion:', error);
      return {
        canComplete: false,
        reason: 'Bir hata oluştu!'
      };
    }
  }

  // Günlük erişim limitini kontrol et
  static async checkDailyAccessLimit(): Promise<{
    withinLimit: boolean;
    accessCount: number;
    maxAccess: number;
  }> {
    try {
      const userProgress = await StorageService.getUserProgress();
      const now = new Date();
      const lastAccessTime = new Date(userProgress.last_activity);

      // Aynı gün içindeyse erişim sayısını kontrol et
      if (this.isSameDay(now, lastAccessTime)) {
        // Basit implementasyon - gerçek uygulamada günlük erişim sayısını takip etmek gerekir
        return {
          withinLimit: true, // Şimdilik her zaman true
          accessCount: 1,
          maxAccess: this.VALIDATION_RULES.max_daily_access
        };
      }

      return {
        withinLimit: true,
        accessCount: 0,
        maxAccess: this.VALIDATION_RULES.max_daily_access
      };
    } catch (error) {
      console.error('Error checking daily access limit:', error);
      return {
        withinLimit: false,
        accessCount: 0,
        maxAccess: this.VALIDATION_RULES.max_daily_access
      };
    }
  }

  // Sonraki erişilebilir günü hesapla
  static async getNextAccessibleDay(): Promise<number> {
    try {
      const userProgress = await StorageService.getUserProgress();
      const completedDays = userProgress.completed_days;
      
      if (completedDays.length === 0) {
        return 1; // İlk gün
      }

      const maxCompletedDay = Math.max(...completedDays);
      return Math.min(maxCompletedDay + 1, 14); // Maksimum 14 gün
    } catch (error) {
      console.error('Error getting next accessible day:', error);
      return 1;
    }
  }

  // İki tarihin aynı gün olup olmadığını kontrol et
  private static isSameDay(date1: Date, date2: Date): boolean {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  }

  // Kalan süreyi hesapla (saat:dakika formatında)
  static calculateRemainingTime(targetTime: Date): string {
    const now = new Date();
    const diff = targetTime.getTime() - now.getTime();
    
    if (diff <= 0) {
      return '00:00';
    }

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  }

  // Gece modu kontrolü (22:00-06:00 arası)
  static isNightMode(): boolean {
    const now = new Date();
    const hour = now.getHours();
    return hour >= 22 || hour < 6;
  }

  // Özel senaryo: Kullanıcı 23:55'te günü bitirip 00:05'te yeni güne geçmek isterse
  static async handleMidnightScenario(dayId: number): Promise<{
    allowed: boolean;
    reason?: string;
    waitTime?: string;
  }> {
    try {
      const userProgress = await StorageService.getUserProgress();
      const now = new Date();
      const lastAccessTime = new Date(userProgress.last_activity);

      // Gece yarısı geçişi kontrolü
      const isNearMidnight = now.getHours() >= 23 || now.getHours() < 1;
      const wasRecentlyCompleted = (now.getTime() - lastAccessTime.getTime()) < (2 * 60 * 60 * 1000); // 2 saat

      if (isNearMidnight && wasRecentlyCompleted) {
        const waitUntil = new Date(lastAccessTime);
        waitUntil.setHours(waitUntil.getHours() + 6); // 6 saat bekle

        return {
          allowed: false,
          reason: 'Gece yarısı kuralı: 6 saat beklemelisin!',
          waitTime: this.calculateRemainingTime(waitUntil)
        };
      }

      return { allowed: true };
    } catch (error) {
      console.error('Error handling midnight scenario:', error);
      return {
        allowed: false,
        reason: 'Bir hata oluştu!'
      };
    }
  }
}

export default TimeValidationService;
