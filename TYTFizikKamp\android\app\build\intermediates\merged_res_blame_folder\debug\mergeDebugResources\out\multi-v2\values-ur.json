{"logs": [{"outputFile": "com.tytfizikkamp.app-mergeDebugResources-45:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d6862beb0c03f816962150b27b444ce5\\transformed\\play-services-basement-17.0.0\\res\\values-ur\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "210", "endOffsets": "457"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4625", "endColumns": "210", "endOffsets": "4831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,269,338,418,484,552,627,704,787,866,934,1011,1093,1167,1250,1336,1412,1485,1557,1646,1717,1793,1862", "endColumns": "67,76,68,68,79,65,67,74,76,82,78,67,76,81,73,82,85,75,72,71,88,70,75,68,72", "endOffsets": "118,195,264,333,413,479,547,622,699,782,861,929,1006,1088,1162,1245,1331,1407,1480,1552,1641,1712,1788,1857,1930"}, "to": {"startLines": "33,49,55,57,58,60,74,75,76,123,124,125,126,131,132,133,134,135,136,137,138,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3036,4548,5157,5296,5365,5504,6527,6595,6670,10467,10550,10629,10697,11100,11182,11256,11339,11425,11501,11574,11646,11836,11907,11983,12052", "endColumns": "67,76,68,68,79,65,67,74,76,82,78,67,76,81,73,82,85,75,72,71,88,70,75,68,72", "endOffsets": "3099,4620,5221,5360,5440,5565,6590,6665,6742,10545,10624,10692,10769,11177,11251,11334,11420,11496,11569,11641,11730,11902,11978,12047,12120"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3e7df312ad5b5ee0d6592d754d6c3af1\\transformed\\material-1.12.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,415,493,591,680,780,899,982,1038,1103,1197,1267,1326,1416,1480,1549,1607,1676,1736,1800,1912,1971,2030,2085,2160,2283,2363,2446,2540,2627,2711,2844,2926,3007,3138,3225,3307,3365,3421,3487,3562,3642,3713,3792,3859,3934,4011,4075,4182,4276,4346,4435,4528,4602,4677,4767,4823,4902,4969,5053,5137,5199,5263,5326,5392,5492,5599,5693,5801,5863,5923,6003,6088,6169", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,77,77,97,88,99,118,82,55,64,93,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,82,93,86,83,132,81,80,130,86,81,57,55,65,74,79,70,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79,84,80,73", "endOffsets": "254,332,410,488,586,675,775,894,977,1033,1098,1192,1262,1321,1411,1475,1544,1602,1671,1731,1795,1907,1966,2025,2080,2155,2278,2358,2441,2535,2622,2706,2839,2921,3002,3133,3220,3302,3360,3416,3482,3557,3637,3708,3787,3854,3929,4006,4070,4177,4271,4341,4430,4523,4597,4672,4762,4818,4897,4964,5048,5132,5194,5258,5321,5387,5487,5594,5688,5796,5858,5918,5998,6083,6164,6238"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,51,52,53,56,59,61,62,63,64,65,66,67,68,69,70,71,72,73,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3104,3182,3260,3338,3436,4246,4346,4465,4836,4892,4957,5226,5445,5570,5660,5724,5793,5851,5920,5980,6044,6156,6215,6274,6329,6404,6747,6827,6910,7004,7091,7175,7308,7390,7471,7602,7689,7771,7829,7885,7951,8026,8106,8177,8256,8323,8398,8475,8539,8646,8740,8810,8899,8992,9066,9141,9231,9287,9366,9433,9517,9601,9663,9727,9790,9856,9956,10063,10157,10265,10327,10387,10860,10945,11026", "endLines": "5,34,35,36,37,38,46,47,48,51,52,53,56,59,61,62,63,64,65,66,67,68,69,70,71,72,73,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,128,129,130", "endColumns": "12,77,77,77,97,88,99,118,82,55,64,93,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,82,93,86,83,132,81,80,130,86,81,57,55,65,74,79,70,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79,84,80,73", "endOffsets": "304,3177,3255,3333,3431,3520,4341,4460,4543,4887,4952,5046,5291,5499,5655,5719,5788,5846,5915,5975,6039,6151,6210,6269,6324,6399,6522,6822,6905,6999,7086,7170,7303,7385,7466,7597,7684,7766,7824,7880,7946,8021,8101,8172,8251,8318,8393,8470,8534,8641,8735,8805,8894,8987,9061,9136,9226,9282,9361,9428,9512,9596,9658,9722,9785,9851,9951,10058,10152,10260,10322,10382,10462,10940,11021,11095"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\625ed137c6a3f5343b71917adedb437c\\transformed\\appcompat-1.7.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,423,529,638,724,828,948,1025,1100,1192,1286,1381,1475,1576,1670,1766,1860,1952,2044,2129,2237,2343,2445,2556,2657,2773,2938,10774", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "418,524,633,719,823,943,1020,1095,1187,1281,1376,1470,1571,1665,1761,1855,1947,2039,2124,2232,2338,2440,2551,2652,2768,2933,3031,10855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ec4e31fa8d0cf74d3d6955f9c39b3c31\\transformed\\core-1.16.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "39,40,41,42,43,44,45,139", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3525,3623,3725,3827,3931,4034,4132,11735", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "3618,3720,3822,3926,4029,4127,4241,11831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e932dc8d24e50c2cb3b8fbb16fc5745d\\transformed\\firebase-messaging-21.1.0\\res\\values-ur\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "207", "endColumns": "101", "endOffsets": "308"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5051", "endColumns": "105", "endOffsets": "5152"}}]}]}