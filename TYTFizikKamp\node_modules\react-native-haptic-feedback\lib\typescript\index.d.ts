import { HapticFeedbackTypes } from "./types";
import type { HapticOptions } from "./types";
declare const RNHapticFeedback: {
    trigger(type?: keyof typeof HapticFeedbackTypes | HapticFeedbackTypes, options?: HapticOptions): void;
};
export * from "./types";
export declare const trigger: (type?: keyof typeof HapticFeedbackTypes | HapticFeedbackTypes, options?: HapticOptions) => void;
export default RNHapticFeedback;
//# sourceMappingURL=index.d.ts.map