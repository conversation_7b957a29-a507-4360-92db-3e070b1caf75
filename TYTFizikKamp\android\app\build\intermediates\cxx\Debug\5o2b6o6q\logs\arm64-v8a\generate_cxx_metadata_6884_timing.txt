# C/C++ build system timings
generate_cxx_metadata
  [gap of 12ms]
  create-invalidation-state 28ms
  generate-prefab-packages
    exec-prefab 727ms
    [gap of 71ms]
  generate-prefab-packages completed in 807ms
  execute-generate-process
    exec-configure 2002ms
    [gap of 124ms]
  execute-generate-process completed in 2128ms
  [gap of 60ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 3074ms

