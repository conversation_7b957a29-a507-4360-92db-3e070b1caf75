{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native-haptic-feedback\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\.cxx\\Debug\\5o2b6o6q\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\.cxx\\Debug\\5o2b6o6q\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "appmodules", "output": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\cxx\\Debug\\5o2b6o6q\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\cxx\\Debug\\5o2b6o6q\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\cxx\\Debug\\5o2b6o6q\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b6a6ccfe82730aba80016e7d9940f54a\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_RNHapticFeedbackSpec::@a83561f277f6afbb326c": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNHapticFeedbackSpec"}, "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNVectorIconsSpec"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rngesturehandler_codegen"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnscreens", "output": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\cxx\\Debug\\5o2b6o6q\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b6a6ccfe82730aba80016e7d9940f54a\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_safeareacontext", "output": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\build\\intermediates\\cxx\\Debug\\5o2b6o6q\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b6a6ccfe82730aba80016e7d9940f54a\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}