[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86_64", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\.cxx\\Debug\\5o2b6o6q\\x86_64\\android_gradle_build.json due to:", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- a file changed", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\.cxx\\Debug\\5o2b6o6q\\x86_64\\build.ninja (LAST_MODIFIED_CHANGED)", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\.cxx\\Debug\\5o2b6o6q\\x86_64\\compile_commands.json (LAST_MODIFIED_CHANGED)", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt (LAST_MODIFIED_CHANGED)", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt (LAST_MODIFIED_CHANGED)", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt (LAST_MODIFIED_CHANGED)", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Microsoft\\\\jdk-17.0.14.7-hotspot\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  x86_64 ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging14957472587612187152\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.14.1\\\\transforms\\\\1d221509c521a46e74f6572064742339\\\\transformed\\\\react-android-0.80.1-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.14.1\\\\transforms\\\\816951bc71efb4c99db481b15113489d\\\\transformed\\\\hermes-android-0.80.1-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.14.1\\\\transforms\\\\b6a6ccfe82730aba80016e7d9940f54a\\\\transformed\\\\fbjni-0.7.0\\\\prefab\"\n", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "keeping json folder 'C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\.cxx\\Debug\\5o2b6o6q\\x86_64' but regenerating project", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86_64\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86_64\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\5o2b6o6q\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\5o2b6o6q\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\android\\\\app\\\\.cxx\\\\Debug\\\\5o2b6o6q\\\\prefab\\\\x86_64\\\\prefab\" ^\n  \"-BC:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\android\\\\app\\\\.cxx\\\\Debug\\\\5o2b6o6q\\\\x86_64\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=C:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=C:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=C:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86_64\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86_64\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\5o2b6o6q\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\5o2b6o6q\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\android\\\\app\\\\.cxx\\\\Debug\\\\5o2b6o6q\\\\prefab\\\\x86_64\\\\prefab\" ^\n  \"-BC:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\android\\\\app\\\\.cxx\\\\Debug\\\\5o2b6o6q\\\\x86_64\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=C:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=C:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=C:\\\\Users\\\\<USER>\\\\Desktop\\\\30days15mobileapp\\\\TYTFizikKamp\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\.cxx\\Debug\\5o2b6o6q\\x86_64\\compile_commands.json.bin normally", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\.cxx\\Debug\\5o2b6o6q\\x86_64\\compile_commands.json to C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\android\\app\\.cxx\\tools\\debug\\x86_64\\compile_commands.json", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\Users\\<USER>\\Desktop\\30days15mobileapp\\TYTFizikKamp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]