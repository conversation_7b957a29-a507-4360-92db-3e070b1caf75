{"logs": [{"outputFile": "com.tytfizikkamp.app-mergeDebugResources-45:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3e7df312ad5b5ee0d6592d754d6c3af1\\transformed\\material-1.12.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,280,364,444,530,627,717,822,958,1043,1103,1168,1267,1335,1394,1483,1551,1618,1681,1756,1824,1878,1998,2056,2118,2172,2247,2389,2479,2557,2651,2734,2819,2964,3048,3131,3277,3373,3450,3508,3559,3625,3699,3777,3848,3934,4008,4087,4160,4232,4348,4452,4525,4624,4724,4798,4873,4980,5032,5121,5188,5279,5373,5435,5499,5562,5632,5751,5856,5965,6065,6127,6182,6267,6354,6432", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,79,85,96,89,104,135,84,59,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,77,93,82,84,144,83,82,145,95,76,57,50,65,73,77,70,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84,86,77,74", "endOffsets": "275,359,439,525,622,712,817,953,1038,1098,1163,1262,1330,1389,1478,1546,1613,1676,1751,1819,1873,1993,2051,2113,2167,2242,2384,2474,2552,2646,2729,2814,2959,3043,3126,3272,3368,3445,3503,3554,3620,3694,3772,3843,3929,4003,4082,4155,4227,4343,4447,4520,4619,4719,4793,4868,4975,5027,5116,5183,5274,5368,5430,5494,5557,5627,5746,5851,5960,6060,6122,6177,6262,6349,6427,6502"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,50,51,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3154,3234,3320,3417,4241,4346,4482,4772,4832,4897,5101,5169,5228,5317,5385,5452,5515,5590,5658,5712,5832,5890,5952,6006,6081,6223,6313,6391,6485,6568,6653,6798,6882,6965,7111,7207,7284,7342,7393,7459,7533,7611,7682,7768,7842,7921,7994,8066,8182,8286,8359,8458,8558,8632,8707,8814,8866,8955,9022,9113,9207,9269,9333,9396,9466,9585,9690,9799,9899,9961,10016,10186,10273,10351", "endLines": "5,33,34,35,36,37,45,46,47,49,50,51,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115,116,117", "endColumns": "12,83,79,85,96,89,104,135,84,59,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,77,93,82,84,144,83,82,145,95,76,57,50,65,73,77,70,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84,86,77,74", "endOffsets": "325,3149,3229,3315,3412,3502,4341,4477,4562,4827,4892,4991,5164,5223,5312,5380,5447,5510,5585,5653,5707,5827,5885,5947,6001,6076,6218,6308,6386,6480,6563,6648,6793,6877,6960,7106,7202,7279,7337,7388,7454,7528,7606,7677,7763,7837,7916,7989,8061,8177,8281,8354,8453,8553,8627,8702,8809,8861,8950,9017,9108,9202,9264,9328,9391,9461,9580,9685,9794,9894,9956,10011,10096,10268,10346,10421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ec4e31fa8d0cf74d3d6955f9c39b3c31\\transformed\\core-1.16.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3507,3604,3706,3807,3904,4011,4119,10426", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3599,3701,3802,3899,4006,4114,4236,10522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d6862beb0c03f816962150b27b444ce5\\transformed\\play-services-basement-17.0.0\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "204", "endOffsets": "451"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "4567", "endColumns": "204", "endOffsets": "4767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\625ed137c6a3f5343b71917adedb437c\\transformed\\appcompat-1.7.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "330,441,549,662,750,856,971,1051,1128,1219,1312,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2368,2469,2579,2697,2805,2968,10101", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "436,544,657,745,851,966,1046,1123,1214,1307,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2363,2464,2574,2692,2800,2963,3065,10181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e932dc8d24e50c2cb3b8fbb16fc5745d\\transformed\\firebase-messaging-21.1.0\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "207", "endColumns": "100", "endOffsets": "307"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "4996", "endColumns": "104", "endOffsets": "5096"}}]}]}