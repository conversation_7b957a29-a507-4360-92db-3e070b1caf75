import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
} from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface TimerComponentProps {
  initialTime: number; // saniye cinsinden
  onTimeUp: () => void;
  warningTime?: number; // uyarı zamanı (saniye)
}

const TimerComponent: React.FC<TimerComponentProps> = ({
  initialTime,
  onTimeUp,
  warningTime = 60, // Son 1 dakika
}) => {
  const [timeLeft, setTimeLeft] = useState(initialTime);
  const [isWarning, setIsWarning] = useState(false);
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const colorAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prevTime => {
        if (prevTime <= 1) {
          clearInterval(timer);
          onTimeUp();
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [onTimeUp]);

  useEffect(() => {
    // Uyarı durumu kontrolü
    if (timeLeft <= warningTime && timeLeft > 0) {
      if (!isWarning) {
        setIsWarning(true);
        startWarningAnimation();
      }
    } else {
      setIsWarning(false);
      stopWarningAnimation();
    }
  }, [timeLeft, warningTime, isWarning]);

  const startWarningAnimation = () => {
    // Nabız animasyonu
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ])
    );

    // Renk animasyonu
    const colorAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(colorAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(colorAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    );

    pulseAnimation.start();
    colorAnimation.start();
  };

  const stopWarningAnimation = () => {
    pulseAnim.stopAnimation();
    colorAnim.stopAnimation();
    pulseAnim.setValue(1);
    colorAnim.setValue(0);
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getTimerColor = () => {
    if (timeLeft <= 30) {
      return colorAnim.interpolate({
        inputRange: [0, 1],
        outputRange: ['#E74C3C', '#C0392B'],
      });
    } else if (timeLeft <= warningTime) {
      return colorAnim.interpolate({
        inputRange: [0, 1],
        outputRange: ['#F39C12', '#E67E22'],
      });
    } else {
      return '#00CEFF';
    }
  };

  const getProgressPercentage = (): number => {
    return (timeLeft / initialTime) * 100;
  };

  const getTimerIcon = () => {
    if (timeLeft <= 30) return 'timer';
    if (timeLeft <= warningTime) return 'schedule';
    return 'access-time';
  };

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.timerContainer,
          {
            transform: [{ scale: pulseAnim }],
          },
        ]}
      >
        {/* Timer Icon */}
        <Animated.View style={[styles.iconContainer, { backgroundColor: getTimerColor() }]}>
          <MaterialIcons name={getTimerIcon()} size={16} color="white" />
        </Animated.View>

        {/* Timer Text */}
        <Animated.Text
          style={[
            styles.timerText,
            { color: getTimerColor() },
          ]}
        >
          {formatTime(timeLeft)}
        </Animated.Text>
      </Animated.View>

      {/* Progress Ring */}
      <View style={styles.progressRing}>
        <View style={styles.progressBackground} />
        <View
          style={[
            styles.progressFill,
            {
              width: `${getProgressPercentage()}%`,
              backgroundColor: isWarning ? '#F39C12' : '#00CEFF',
            },
          ]}
        />
      </View>

      {/* Warning Messages */}
      {isWarning && (
        <View style={styles.warningContainer}>
          {timeLeft <= 30 && (
            <Text style={styles.criticalWarning}>⚠️ Son 30 saniye!</Text>
          )}
          {timeLeft <= warningTime && timeLeft > 30 && (
            <Text style={styles.warning}>⏰ Süre azalıyor!</Text>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 8,
  },
  iconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 6,
  },
  timerText: {
    fontSize: 14,
    fontWeight: 'bold',
    fontFamily: 'monospace',
  },
  progressRing: {
    width: 80,
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressBackground: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  warningContainer: {
    alignItems: 'center',
  },
  warning: {
    fontSize: 10,
    color: '#F39C12',
    fontWeight: '600',
    textAlign: 'center',
  },
  criticalWarning: {
    fontSize: 10,
    color: '#E74C3C',
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default TimerComponent;
