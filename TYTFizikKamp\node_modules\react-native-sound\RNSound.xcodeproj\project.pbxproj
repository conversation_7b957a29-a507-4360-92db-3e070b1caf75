// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		19825A221BD4A89800EE0337 /* RNSound.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 19825A211BD4A89800EE0337 /* RNSound.h */; };
		19825A241BD4A89800EE0337 /* RNSound.m in Sources */ = {isa = PBXBuildFile; fileRef = 19825A231BD4A89800EE0337 /* RNSound.m */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		19825A1C1BD4A89800EE0337 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
				19825A221BD4A89800EE0337 /* RNSound.h in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		19825A1E1BD4A89800EE0337 /* libRNSound.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libRNSound.a; sourceTree = BUILT_PRODUCTS_DIR; };
		19825A211BD4A89800EE0337 /* RNSound.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNSound.h; sourceTree = "<group>"; };
		19825A231BD4A89800EE0337 /* RNSound.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNSound.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		19825A1B1BD4A89800EE0337 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		19825A151BD4A89800EE0337 = {
			isa = PBXGroup;
			children = (
				19825A201BD4A89800EE0337 /* RNSound */,
				19825A1F1BD4A89800EE0337 /* Products */,
			);
			sourceTree = "<group>";
		};
		19825A1F1BD4A89800EE0337 /* Products */ = {
			isa = PBXGroup;
			children = (
				19825A1E1BD4A89800EE0337 /* libRNSound.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		19825A201BD4A89800EE0337 /* RNSound */ = {
			isa = PBXGroup;
			children = (
				19825A211BD4A89800EE0337 /* RNSound.h */,
				19825A231BD4A89800EE0337 /* RNSound.m */,
			);
			path = RNSound;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		19825A1D1BD4A89800EE0337 /* RNSound */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 19825A271BD4A89800EE0337 /* Build configuration list for PBXNativeTarget "RNSound" */;
			buildPhases = (
				19825A1A1BD4A89800EE0337 /* Sources */,
				19825A1B1BD4A89800EE0337 /* Frameworks */,
				19825A1C1BD4A89800EE0337 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RNSound;
			productName = RNSound;
			productReference = 19825A1E1BD4A89800EE0337 /* libRNSound.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		19825A161BD4A89800EE0337 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0700;
				ORGANIZATIONNAME = zmxv;
				TargetAttributes = {
					19825A1D1BD4A89800EE0337 = {
						CreatedOnToolsVersion = 7.0.1;
					};
				};
			};
			buildConfigurationList = 19825A191BD4A89800EE0337 /* Build configuration list for PBXProject "RNSound" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
			);
			mainGroup = 19825A151BD4A89800EE0337;
			productRefGroup = 19825A1F1BD4A89800EE0337 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				19825A1D1BD4A89800EE0337 /* RNSound */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		19825A1A1BD4A89800EE0337 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				19825A241BD4A89800EE0337 /* RNSound.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		19825A251BD4A89800EE0337 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/../react-native/React/**",
					"$(SRCROOT)/node_modules/react-native/React/**",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		19825A261BD4A89800EE0337 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/../react-native/React/**",
					"$(SRCROOT)/node_moduels/react-native/React/**",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		19825A281BD4A89800EE0337 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		19825A291BD4A89800EE0337 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		19825A191BD4A89800EE0337 /* Build configuration list for PBXProject "RNSound" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				19825A251BD4A89800EE0337 /* Debug */,
				19825A261BD4A89800EE0337 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		19825A271BD4A89800EE0337 /* Build configuration list for PBXNativeTarget "RNSound" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				19825A281BD4A89800EE0337 /* Debug */,
				19825A291BD4A89800EE0337 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 19825A161BD4A89800EE0337 /* Project object */;
}
