import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import BadgeCard from '../components/BadgeCard';
import BadgeStatsCard from '../components/BadgeStatsCard';
import BadgeService, { BadgeProgress } from '../services/BadgeService';
import StorageService from '../services/StorageService';
import badgesData from '../data/badges.json';
import { Badge } from '../types';

const { width } = Dimensions.get('window');

interface BadgeScreenProps {
  navigation: any;
}

const BadgeScreen: React.FC<BadgeScreenProps> = ({ navigation }) => {
  const [badges, setBadges] = useState<Badge[]>([]);
  const [badgeProgress, setBadgeProgress] = useState<BadgeProgress[]>([]);
  const [badgeStats, setBadgeStats] = useState({
    total: 0,
    earned: 0,
    percentage: 0,
    totalPoints: 0,
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    loadBadgeData();
  }, []);

  const loadBadgeData = async () => {
    try {
      setLoading(true);
      
      // Rozet verilerini yükle
      setBadges(badgesData as Badge[]);
      
      // Rozet ilerlemelerini yükle
      const progress = await BadgeService.getBadgeProgress();
      setBadgeProgress(progress);
      
      // Rozet istatistiklerini yükle
      const stats = await BadgeService.getBadgeStats();
      setBadgeStats(stats);
      
      // Yeni rozetleri kontrol et
      const userProgress = await StorageService.getUserProgress();
      await BadgeService.checkAndAwardBadges(userProgress);
      
    } catch (error) {
      console.error('Error loading badge data:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadBadgeData();
    setRefreshing(false);
  };

  const getFilteredBadges = (): Badge[] => {
    if (selectedCategory === 'all') return badges;
    if (selectedCategory === 'earned') {
      const earnedIds = badgeProgress.filter(p => p.isEarned).map(p => p.badgeId);
      return badges.filter(badge => earnedIds.includes(badge.id));
    }
    return badges.filter(badge => badge.type === selectedCategory);
  };

  const getBadgeProgressData = (badgeId: string): BadgeProgress | undefined => {
    return badgeProgress.find(p => p.badgeId === badgeId);
  };

  const categories = [
    { id: 'all', name: 'Tümü', icon: 'apps' },
    { id: 'earned', name: 'Kazanılan', icon: 'star' },
    { id: 'completion', name: 'Tamamlama', icon: 'check-circle' },
    { id: 'streak', name: 'Seri', icon: 'local-fire-department' },
    { id: 'score', name: 'Başarı', icon: 'trending-up' },
    { id: 'special', name: 'Özel', icon: 'auto-awesome' },
  ];

  if (loading) {
    return (
      <LinearGradient
        colors={['#6C5CE7', '#A29BFE']}
        style={styles.loadingContainer}
      >
        <Text style={styles.loadingText}>Rozetler Yükleniyor...</Text>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={['#6C5CE7', '#A29BFE', '#F5F6FA']}
      style={styles.container}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>

        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Rozet Koleksiyonu</Text>
          <Text style={styles.headerSubtitle}>Başarılarını keşfet</Text>
        </View>

        <View style={styles.headerRight}>
          <MaterialIcons name="emoji-events" size={24} color="white" />
        </View>
      </View>

      {/* Stats Card */}
      <View style={styles.statsContainer}>
        <BadgeStatsCard stats={badgeStats} />
      </View>

      {/* Category Filter */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoryContainer}
        contentContainerStyle={styles.categoryContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryButton,
              selectedCategory === category.id && styles.categoryButtonActive,
            ]}
            onPress={() => setSelectedCategory(category.id)}
          >
            <MaterialIcons
              name={category.icon}
              size={20}
              color={selectedCategory === category.id ? 'white' : '#6C5CE7'}
            />
            <Text
              style={[
                styles.categoryText,
                selectedCategory === category.id && styles.categoryTextActive,
              ]}
            >
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Badge Grid */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.badgeGrid}>
          {getFilteredBadges().map((badge) => {
            const progress = getBadgeProgressData(badge.id);
            return (
              <BadgeCard
                key={badge.id}
                badge={badge}
                progress={progress}
                onPress={() => {
                  // Badge detay modalı açılabilir
                }}
              />
            );
          })}
        </View>

        {getFilteredBadges().length === 0 && (
          <View style={styles.emptyContainer}>
            <MaterialIcons name="emoji-events" size={60} color="rgba(255,255,255,0.5)" />
            <Text style={styles.emptyText}>
              {selectedCategory === 'earned'
                ? 'Henüz rozet kazanmadın!\nDers çözmeye başla!'
                : 'Bu kategoride rozet bulunamadı.'}
            </Text>
          </View>
        )}

        {/* Motivational Message */}
        <View style={styles.motivationContainer}>
          <LinearGradient
            colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
            style={styles.motivationCard}
          >
            <MaterialIcons name="auto-awesome" size={24} color="#00CEFF" />
            <Text style={styles.motivationText}>
              Her rozet bir başarı hikayesi! Çalışmaya devam et ve koleksiyonunu büyüt! 🚀
            </Text>
          </LinearGradient>
        </View>
      </ScrollView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    color: 'white',
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 2,
  },
  headerRight: {
    width: 40,
    alignItems: 'center',
  },
  statsContainer: {
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  categoryContainer: {
    marginBottom: 15,
  },
  categoryContent: {
    paddingHorizontal: 20,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.9)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 18,
    marginRight: 10,
  },
  categoryButtonActive: {
    backgroundColor: '#6C5CE7',
  },
  categoryText: {
    fontSize: 13,
    color: '#6C5CE7',
    fontWeight: '600',
    marginLeft: 5,
  },
  categoryTextActive: {
    color: 'white',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
    paddingTop: 10,
  },
  badgeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
    lineHeight: 24,
  },
  motivationContainer: {
    paddingHorizontal: 20,
    marginTop: 30,
  },
  motivationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 15,
  },
  motivationText: {
    flex: 1,
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
    lineHeight: 22,
  },
});

export default BadgeScreen;
