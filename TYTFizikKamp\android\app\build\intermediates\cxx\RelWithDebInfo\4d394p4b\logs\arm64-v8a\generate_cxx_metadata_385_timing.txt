# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 105ms
  generate-prefab-packages
    [gap of 21ms]
    exec-prefab 512ms
    [gap of 17ms]
  generate-prefab-packages completed in 550ms
  execute-generate-process
    exec-configure 1915ms
    [gap of 130ms]
  execute-generate-process completed in 2045ms
  remove-unexpected-so-files 14ms
  [gap of 35ms]
generate_cxx_metadata completed in 2766ms

