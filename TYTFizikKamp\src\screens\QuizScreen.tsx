import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';

import QuestionCard from '../components/QuestionCard';
import SolutionModal from '../components/SolutionModal';
import TimerComponent from '../components/TimerComponent';
import StorageService from '../services/StorageService';
import lessonsData from '../data/lessons.json';
import { Question, Lesson, QuizState } from '../types';

const { width, height } = Dimensions.get('window');

// Import type from App.tsx
import { QuizScreenProps } from '../../App';

const QuizScreen: React.FC<QuizScreenProps> = ({ navigation, route }) => {
  const { dayId } = route.params;
  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [quizState, setQuizState] = useState<QuizState>({
    currentQuestionIndex: 0,
    answers: {},
    score: 0,
    timeRemaining: 900, // 15 dakika
    isCompleted: false,
  });
  const [selectedAnswer, setSelectedAnswer] = useState<string>('');
  const [showSolution, setShowSolution] = useState(false);
  const [isAnswerCorrect, setIsAnswerCorrect] = useState(false);
  const [loading, setLoading] = useState(true);

  const progressAnim = useRef(new Animated.Value(0)).current;
  const scoreAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadQuizData();
  }, [dayId]);

  useEffect(() => {
    // İlerleme animasyonu
    const progress = ((quizState.currentQuestionIndex + 1) / (lesson?.sorular.length || 1)) * 100;
    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 500,
      useNativeDriver: false,
    }).start();
  }, [quizState.currentQuestionIndex, lesson]);

  useEffect(() => {
    // Skor animasyonu
    Animated.timing(scoreAnim, {
      toValue: quizState.score,
      duration: 800,
      useNativeDriver: false,
    }).start();
  }, [quizState.score]);

  const loadQuizData = async () => {
    try {
      setLoading(true);
      const lessonData = lessonsData.find(l => l.gun === dayId);
      if (!lessonData) {
        Alert.alert('Hata', 'Quiz bulunamadı!');
        navigation.goBack();
        return;
      }
      setLesson(lessonData as Lesson);
    } catch (error) {
      console.error('Error loading quiz data:', error);
      Alert.alert('Hata', 'Quiz yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleAnswer = async (questionId: number, answer: string, isCorrect: boolean) => {
    setSelectedAnswer(answer);
    setIsAnswerCorrect(isCorrect);
    setShowSolution(true);

    // Cevabı kaydet
    setQuizState(prev => ({
      ...prev,
      answers: { ...prev.answers, [questionId]: answer },
      score: isCorrect ? prev.score + 20 : prev.score,
    }));

    // Storage'a kaydet
    try {
      await StorageService.answerQuestion(dayId, questionId, isCorrect, selectedAnswer || '');
    } catch (error) {
      console.error('Error saving answer:', error);
    }
  };

  const handleNextQuestion = () => {
    setShowSolution(false);
    setSelectedAnswer('');

    if (!lesson) return;

    if (quizState.currentQuestionIndex < lesson.sorular.length - 1) {
      setQuizState(prev => ({
        ...prev,
        currentQuestionIndex: prev.currentQuestionIndex + 1,
      }));
    } else {
      completeQuiz();
    }
  };

  const completeQuiz = () => {
    setQuizState(prev => ({ ...prev, isCompleted: true }));
    
    const correctAnswers = Object.values(quizState.answers).filter((_, index) => {
      const question = lesson?.sorular[index];
      return question && quizState.answers[question.id] === question.cevap;
    }).length;

    Alert.alert(
      'Quiz Tamamlandı! 🎉',
      `Skorun: ${quizState.score}\nDoğru Cevap: ${correctAnswers}/${lesson?.sorular.length}`,
      [
        {
          text: 'Gün Detayına Dön',
          onPress: () => navigation.goBack(),
        },
      ]
    );
  };

  const handleTimeUp = () => {
    Alert.alert(
      'Süre Doldu! ⏰',
      'Quiz süresi tamamlandı. Mevcut skorunla devam edebilirsin.',
      [
        {
          text: 'Tamam',
          onPress: completeQuiz,
        },
      ]
    );
  };

  const getCurrentQuestion = (): Question | null => {
    if (!lesson || quizState.currentQuestionIndex >= lesson.sorular.length) {
      return null;
    }
    return lesson.sorular[quizState.currentQuestionIndex];
  };

  const getProgressPercentage = (): number => {
    if (!lesson) return 0;
    return ((quizState.currentQuestionIndex + 1) / lesson.sorular.length) * 100;
  };

  if (loading || !lesson) {
    return (
      <LinearGradient
        colors={['#6C5CE7', '#A29BFE']}
        style={styles.loadingContainer}
      >
        <Text style={styles.loadingText}>Quiz Yükleniyor...</Text>
      </LinearGradient>
    );
  }

  const currentQuestion = getCurrentQuestion();

  if (!currentQuestion) {
    return (
      <LinearGradient
        colors={['#6C5CE7', '#A29BFE']}
        style={styles.loadingContainer}
      >
        <Text style={styles.loadingText}>Quiz Tamamlandı!</Text>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={['#6C5CE7', '#A29BFE', '#F5F6FA']}
      style={styles.container}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="white" />
        </TouchableOpacity>

        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Gün {dayId} Quiz</Text>
          <Text style={styles.headerSubtitle}>{lesson.konu}</Text>
        </View>

        <View style={styles.scoreContainer}>
          <Animated.Text style={styles.scoreText}>
            {scoreAnim.interpolate({
              inputRange: [0, 1000],
              outputRange: ['0', '1000'],
              extrapolate: 'clamp',
            })}
          </Animated.Text>
        </View>
      </View>

      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressInfo}>
          <Text style={styles.progressText}>
            Soru {quizState.currentQuestionIndex + 1} / {lesson.sorular.length}
          </Text>
          <TimerComponent
            initialTime={quizState.timeRemaining}
            onTimeUp={handleTimeUp}
          />
        </View>
        
        <View style={styles.progressBar}>
          <Animated.View
            style={[
              styles.progressFill,
              {
                width: progressAnim.interpolate({
                  inputRange: [0, 100],
                  outputRange: ['0%', '100%'],
                  extrapolate: 'clamp',
                }),
              },
            ]}
          />
        </View>
      </View>

      {/* Question Card */}
      <View style={styles.questionContainer}>
        <QuestionCard
          question={currentQuestion}
          onAnswer={handleAnswer}
          disabled={showSolution}
        />
      </View>

      {/* Solution Modal */}
      <SolutionModal
        isVisible={showSolution}
        question={currentQuestion}
        selectedAnswer={selectedAnswer}
        isCorrect={isAnswerCorrect}
        onClose={() => setShowSolution(false)}
        onNext={handleNextQuestion}
      />

      {/* Bottom Stats */}
      <View style={styles.bottomStats}>
        <View style={styles.statItem}>
          <Icon name="check-circle" size={20} color="#27AE60" />
          <Text style={styles.statText}>
            {Object.values(quizState.answers).filter((_, index) => {
              const q = lesson.sorular[index];
              return q && quizState.answers[q.id] === q.cevap;
            }).length} Doğru
          </Text>
        </View>

        <View style={styles.statItem}>
          <Icon name="cancel" size={20} color="#E74C3C" />
          <Text style={styles.statText}>
            {Object.values(quizState.answers).filter((_, index) => {
              const q = lesson.sorular[index];
              return q && quizState.answers[q.id] !== q.cevap;
            }).length} Yanlış
          </Text>
        </View>

        <View style={styles.statItem}>
          <Icon name="star" size={20} color="#F39C12" />
          <Text style={styles.statText}>{quizState.score} Puan</Text>
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    color: 'white',
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 2,
  },
  scoreContainer: {
    alignItems: 'center',
  },
  scoreText: {
    fontSize: 20,
    color: '#00CEFF',
    fontWeight: 'bold',
  },
  progressContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  progressText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#00CEFF',
    borderRadius: 4,
  },
  questionContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  bottomStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
});

export default QuizScreen;
