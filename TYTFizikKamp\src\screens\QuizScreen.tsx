import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
  ScrollView,
  Modal,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import QuestionCard from '../components/QuestionCard';
import TimerComponent from '../components/TimerComponent';
import StorageService from '../services/StorageService';
import lessonsData from '../data/lessons.json';
import { Question, Lesson, QuizState } from '../types';

const { width, height } = Dimensions.get('window');

// Import type from App.tsx
import { QuizScreenProps } from '../../App';

interface ExtendedQuizState extends QuizState {
  wrongQuestions?: number[]; // Yanlış cevaplanan soruların ID'leri
  retryMode: boolean; // Tekrar çözme modunda mı
  retryQuestions: Question[]; // Tekrar çözülecek sorular
}

const QuizScreen: React.FC<QuizScreenProps> = ({ navigation, route }) => {
  const { dayId, retryMode = false } = route.params;
  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [quizState, setQuizState] = useState<ExtendedQuizState>({
    currentQuestionIndex: 0,
    answers: {},
    score: 0,
    timeRemaining: 900, // 15 dakika
    isCompleted: false,
    wrongQuestions: [],
    retryMode: retryMode || false,
    retryQuestions: [],
  });
  const [selectedAnswer, setSelectedAnswer] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [showResults, setShowResults] = useState(false);
  const [hasAnswered, setHasAnswered] = useState(false);
  const [showQuestionNavigator, setShowQuestionNavigator] = useState(false);

  const progressAnim = useRef(new Animated.Value(0)).current;
  const scoreAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadQuizData();
  }, [dayId, retryMode]);

  useEffect(() => {
    // İlerleme animasyonu
    const totalQuestions = quizState.retryMode 
      ? quizState.retryQuestions.length 
      : (lesson?.sorular.length || 1);
    const progress = ((quizState.currentQuestionIndex + 1) / totalQuestions) * 100;
    
    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 500,
      useNativeDriver: false,
    }).start();
  }, [quizState.currentQuestionIndex, lesson, quizState.retryMode, quizState.retryQuestions]);

  useEffect(() => {
    // Skor animasyonu
    Animated.timing(scoreAnim, {
      toValue: quizState.score,
      duration: 800,
      useNativeDriver: false,
    }).start();
  }, [quizState.score]);

  // Soru değiştiğinde cevap durumunu kontrol et
  useEffect(() => {
    const questions = quizState.retryMode ? quizState.retryQuestions : lesson?.sorular;
    if (questions && questions[quizState.currentQuestionIndex]) {
      const currentQuestion = questions[quizState.currentQuestionIndex];
      const hasAnswerForCurrentQuestion = quizState.answers.hasOwnProperty(currentQuestion.id);
      setHasAnswered(hasAnswerForCurrentQuestion);
      
      if (hasAnswerForCurrentQuestion) {
        const userAnswer = quizState.answers[currentQuestion.id];
        setSelectedAnswer(userAnswer);
      } else {
        setSelectedAnswer('');
      }
    }
  }, [quizState.currentQuestionIndex, lesson, quizState.answers, quizState.retryMode, quizState.retryQuestions]);

  const loadQuizData = async () => {
    try {
      setLoading(true);
      const lessonData = lessonsData.find(l => l.gun === dayId);
      if (!lessonData) {
        Alert.alert('Hata', 'Quiz bulunamadı!');
        navigation.goBack();
        return;
      }
      setLesson(lessonData as Lesson);

      if (retryMode) {
        // Tekrar modunda sadece yanlış soruları yükle
        const savedProgress = await StorageService.getQuizProgress(dayId);
        if (savedProgress && savedProgress.wrongQuestions && savedProgress.wrongQuestions.length > 0) {
          const retryQuestions = lessonData.sorular.filter(q =>
            savedProgress.wrongQuestions!.includes(q.id)
          );
          
          setQuizState(prev => ({
            ...prev,
            retryMode: true,
            retryQuestions: retryQuestions,
            currentQuestionIndex: 0,
            answers: {},
            score: 0,
            wrongQuestions: [],
          }));
        } else {
          Alert.alert('Bilgi', 'Tekrar çözülecek yanlış soru bulunamadı!');
          navigation.goBack();
        }
      } else {
        // Normal modda kaldığı yerden devam
        const savedProgress = await StorageService.getQuizProgress(dayId);
        if (savedProgress) {
          setQuizState(prev => ({
            ...prev,
            currentQuestionIndex: savedProgress.currentQuestionIndex || 0,
            answers: savedProgress.answers || {},
            score: savedProgress.score || 0,
            timeRemaining: savedProgress.timeRemaining || 900,
            wrongQuestions: savedProgress.wrongQuestions || [],
          }));
        }
      }
    } catch (error) {
      console.error('Error loading quiz data:', error);
      Alert.alert('Hata', 'Quiz yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleAnswer = async (questionId: number, answer: string, isCorrect: boolean) => {
    if (hasAnswered) return;

    setSelectedAnswer(answer);
    setHasAnswered(true);

    // Cevabı kaydet
    const newAnswers = { ...quizState.answers, [questionId]: answer };
    const newScore = isCorrect ? quizState.score + 20 : quizState.score;

    // Yanlış sorular listesini güncelle
    let newWrongQuestions = [...(quizState.wrongQuestions || [])];
    if (!isCorrect && !newWrongQuestions.includes(questionId)) {
      newWrongQuestions.push(questionId);
    } else if (isCorrect && newWrongQuestions.includes(questionId)) {
      // Eğer tekrar modunda doğru cevaplandıysa listeden çıkar
      newWrongQuestions = newWrongQuestions.filter(id => id !== questionId);
    }

    setQuizState(prev => ({
      ...prev,
      answers: newAnswers,
      score: newScore,
      wrongQuestions: newWrongQuestions,
    }));

    // Storage'a kaydet
    try {
      await StorageService.answerQuestion(dayId, questionId, isCorrect, answer);
      await StorageService.saveQuizProgress(dayId, {
        currentQuestionIndex: quizState.currentQuestionIndex,
        answers: newAnswers,
        score: newScore,
        timeRemaining: quizState.timeRemaining,
        wrongQuestions: newWrongQuestions,
      });
    } catch (error) {
      console.error('Error saving answer:', error);
    }

    // Otomatik olarak sonraki soruya geç veya quiz'i bitir
    setTimeout(() => {
      const questions = quizState.retryMode ? quizState.retryQuestions : lesson?.sorular;
      if (questions && quizState.currentQuestionIndex >= questions.length - 1) {
        // Son soru, quiz'i bitir
        completeQuiz();
      } else {
        // Sonraki soruya geç
        handleNextQuestion();
      }
    }, 500);
  };

  const handleSkipQuestion = async () => {
    if (!lesson || hasAnswered) return;

    const currentQuestion = getCurrentQuestion();
    if (!currentQuestion) return;

    const questionId = currentQuestion.id;

    // Boş cevap olarak kaydet ve yanlış sorular listesine ekle
    const newAnswers = { ...quizState.answers, [questionId]: '' };
    let newWrongQuestions = [...(quizState.wrongQuestions || [])];
    if (!newWrongQuestions.includes(questionId)) {
      newWrongQuestions.push(questionId);
    }

    setQuizState(prev => ({
      ...prev,
      answers: newAnswers,
      wrongQuestions: newWrongQuestions,
    }));

    setHasAnswered(true);

    try {
      await StorageService.answerQuestion(dayId, questionId, false, '');
      await StorageService.saveQuizProgress(dayId, {
        currentQuestionIndex: quizState.currentQuestionIndex,
        answers: newAnswers,
        score: quizState.score,
        timeRemaining: quizState.timeRemaining,
        wrongQuestions: newWrongQuestions,
      });
    } catch (error) {
      console.error('Error saving skip:', error);
    }

    // Sonraki soruya geç
    setTimeout(() => handleNextQuestion(), 1000);
  };

  const handleNextQuestion = async () => {
    const questions = quizState.retryMode ? quizState.retryQuestions : lesson?.sorular;
    if (!questions) return;

    setSelectedAnswer('');
    setHasAnswered(false);

    if (quizState.currentQuestionIndex < questions.length - 1) {
      const newState = {
        ...quizState,
        currentQuestionIndex: quizState.currentQuestionIndex + 1,
      };

      setQuizState(newState);

      // Progress'i kaydet
      try {
        await StorageService.saveQuizProgress(dayId, {
          currentQuestionIndex: newState.currentQuestionIndex,
          answers: newState.answers,
          score: newState.score,
          timeRemaining: newState.timeRemaining,
          wrongQuestions: newState.wrongQuestions,
        });
      } catch (error) {
        console.error('Error saving quiz progress:', error);
      }
    }
    // Son soruda artık otomatik olarak quiz bitmiyor
    // Sadece "Quiz'i Bitir" butonuna tıklandığında bitiyor
  };

  const handlePreviousQuestion = () => {
    if (quizState.currentQuestionIndex > 0) {
      setQuizState(prev => ({
        ...prev,
        currentQuestionIndex: prev.currentQuestionIndex - 1,
      }));
    }
  };

  const jumpToQuestion = (index: number) => {
    setQuizState(prev => ({
      ...prev,
      currentQuestionIndex: index,
    }));
    setShowQuestionNavigator(false);
  };

  const completeQuiz = async () => {
    console.log('completeQuiz called');

    try {
      setQuizState(prev => ({ ...prev, isCompleted: true }));

      // Başarı oranını hesapla
      const results = getQuizResults();
      console.log('Quiz results:', results);

      const successRate = (results.correct / results.total) * 100;
      const isPassed = successRate >= 90;

      console.log('Success rate:', successRate, 'isPassed:', isPassed);

      // Quiz sonucunu kaydet
      await StorageService.completeQuiz(dayId, {
        totalQuestions: results.total,
        correctAnswers: results.correct,
        wrongAnswers: results.wrong,
        skippedAnswers: results.skipped,
        successRate: successRate,
        isPassed: isPassed,
        wrongQuestions: quizState.wrongQuestions || [],
        timeSpent: 900 - quizState.timeRemaining
      });

      // Eğer geçtiyse günü tamamla
      if (isPassed && !quizState.retryMode) {
        await StorageService.completeDay(dayId, quizState.score, 900 - quizState.timeRemaining);
      }

      console.log('About to show results');
      setShowResults(true);

    } catch (error) {
      console.error('Error completing quiz:', error);
      // Hata olsa bile sonuç ekranını göster
      setShowResults(true);
    }
  };

  const startRetryMode = () => {
    if (!lesson || !quizState.wrongQuestions || quizState.wrongQuestions.length === 0) return;

    // Yanlış soruları filtrele
    const wrongQuestions = lesson.sorular.filter(q =>
      quizState.wrongQuestions!.includes(q.id)
    );

    // Quiz state'ini sıfırla ve retry mode'a geç
    setQuizState({
      currentQuestionIndex: 0,
      answers: {},
      score: 0,
      timeRemaining: 900,
      isCompleted: false,
      wrongQuestions: [],
      retryMode: true,
      retryQuestions: wrongQuestions,
    });

    // UI state'lerini sıfırla
    setShowResults(false);
    setSelectedAnswer('');
    setHasAnswered(false);
  };

  const startFullRetry = () => {
    if (!lesson) return;

    // Quiz state'ini tamamen sıfırla
    setQuizState({
      currentQuestionIndex: 0,
      answers: {},
      score: 0,
      timeRemaining: 900,
      isCompleted: false,
      wrongQuestions: [],
      retryMode: false,
      retryQuestions: [],
    });

    // UI state'lerini sıfırla
    setShowResults(false);
    setSelectedAnswer('');
    setHasAnswered(false);
  };

  const getQuizResults = () => {
    const questions = quizState.retryMode ? quizState.retryQuestions : lesson?.sorular;
    if (!questions) return { correct: 0, wrong: 0, total: 0, skipped: 0, details: [] };

    let correct = 0;
    let wrong = 0;
    let skipped = 0;
    const details: Array<{questionId: number, userAnswer: string, correctAnswer: string, isCorrect: boolean}> = [];

    questions.forEach(question => {
      const userAnswer = quizState.answers[question.id] || '';
      const isCorrect = userAnswer === question.cevap;
      const isSkipped = userAnswer === '';

      if (isCorrect) {
        correct++;
      } else if (isSkipped) {
        skipped++;
      } else {
        wrong++;
      }

      details.push({
        questionId: question.id,
        userAnswer,
        correctAnswer: question.cevap,
        isCorrect
      });
    });

    return { correct, wrong, total: questions.length, skipped, details };
  };

  const handleTimeUp = () => {
    Alert.alert(
      'Süre Doldu! ⏰',
      'Quiz süresi tamamlandı. Mevcut skorunla devam edebilirsin.',
      [
        {
          text: 'Tamam',
          onPress: completeQuiz,
        },
      ]
    );
  };

  const getCurrentQuestion = (): Question | null => {
    const questions = quizState.retryMode ? quizState.retryQuestions : lesson?.sorular;
    if (!questions || quizState.currentQuestionIndex >= questions.length) {
      return null;
    }
    return questions[quizState.currentQuestionIndex];
  };

  const getAnsweredStats = () => {
    const questions = quizState.retryMode ? quizState.retryQuestions : lesson?.sorular;
    if (!questions) return { correct: 0, wrong: 0, skipped: 0 };

    let correct = 0;
    let wrong = 0;
    let skipped = 0;

    questions.forEach(question => {
      const userAnswer = quizState.answers[question.id];
      if (userAnswer === undefined) return;

      if (userAnswer === '') {
        skipped++;
      } else if (userAnswer === question.cevap) {
        correct++;
      } else {
        wrong++;
      }
    });

    return { correct, wrong, skipped };
  };

  // Question Navigator Component
  const renderQuestionNavigator = () => {
    const questions = quizState.retryMode ? quizState.retryQuestions : lesson?.sorular;
    if (!questions) return null;

    return (
      <Modal
        visible={showQuestionNavigator}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowQuestionNavigator(false)}
      >
        <View style={styles.navigatorOverlay}>
          <View style={styles.navigatorContainer}>
            <View style={styles.navigatorHeader}>
              <Text style={styles.navigatorTitle}>Soru Seçimi</Text>
              <TouchableOpacity
                onPress={() => setShowQuestionNavigator(false)}
                style={styles.navigatorClose}
              >
                <MaterialIcons name="close" size={24} color="#6C5CE7" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.navigatorContent}>
              <View style={styles.navigatorGrid}>
                {questions.map((question, index) => {
                  const userAnswer = quizState.answers[question.id];
                  const isAnswered = userAnswer !== undefined;
                  const isCorrect = userAnswer === question.cevap;
                  const isSkipped = userAnswer === '';
                  const isCurrent = index === quizState.currentQuestionIndex;

                  return (
                    <TouchableOpacity
                      key={question.id}
                      style={[
                        styles.navigatorItem,
                        isCurrent && styles.navigatorItemCurrent,
                        isAnswered && (isCorrect ? styles.navigatorItemCorrect : 
                          (isSkipped ? styles.navigatorItemSkipped : styles.navigatorItemWrong))
                      ]}
                      onPress={() => jumpToQuestion(index)}
                    >
                      <Text style={[
                        styles.navigatorItemText,
                        isCurrent && styles.navigatorItemTextCurrent,
                        isAnswered && styles.navigatorItemTextAnswered
                      ]}>
                        {index + 1}
                      </Text>
                      {isAnswered && (
                        <MaterialIcons 
                          name={isCorrect ? "check" : (isSkipped ? "skip-next" : "close")} 
                          size={16} 
                          color={isCorrect ? "#27AE60" : (isSkipped ? "#F39C12" : "#E74C3C")} 
                        />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            </ScrollView>

            <View style={styles.navigatorLegend}>
              <View style={styles.legendItem}>
                <View style={[styles.legendColor, styles.navigatorItemCorrect]} />
                <Text style={styles.legendText}>Doğru</Text>
              </View>
              <View style={styles.legendItem}>
                <View style={[styles.legendColor, styles.navigatorItemWrong]} />
                <Text style={styles.legendText}>Yanlış</Text>
              </View>
              <View style={styles.legendItem}>
                <View style={[styles.legendColor, styles.navigatorItemSkipped]} />
                <Text style={styles.legendText}>Geçildi</Text>
              </View>
              <View style={styles.legendItem}>
                <View style={[styles.legendColor, styles.navigatorItemCurrent]} />
                <Text style={styles.legendText}>Mevcut</Text>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  if (loading || !lesson) {
    return (
      <LinearGradient
        colors={['#6C5CE7', '#A29BFE']}
        style={styles.loadingContainer}
      >
        <Text style={styles.loadingText}>Quiz Yükleniyor...</Text>
      </LinearGradient>
    );
  }

  const currentQuestion = getCurrentQuestion();
  const questions = quizState.retryMode ? quizState.retryQuestions : lesson?.sorular;

  if (!currentQuestion || !questions) {
    return (
      <LinearGradient
        colors={['#6C5CE7', '#A29BFE']}
        style={styles.loadingContainer}
      >
        <Text style={styles.loadingText}>Quiz Tamamlandı!</Text>
      </LinearGradient>
    );
  }

  const answeredStats = getAnsweredStats();

  return (
    <LinearGradient
      colors={['#6C5CE7', '#A29BFE', '#F5F6FA']}
      style={styles.container}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>

        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>
            {quizState.retryMode ? `Gün ${dayId} - Tekrar` : `Gün ${dayId} Quiz`}
          </Text>
          <Text style={styles.headerSubtitle}>{lesson.konu}</Text>
        </View>

        <TouchableOpacity
          style={styles.navigatorButton}
          onPress={() => setShowQuestionNavigator(true)}
        >
          <MaterialIcons name="apps" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressInfo}>
          <Text style={styles.progressText}>
            Soru {quizState.currentQuestionIndex + 1} / {questions.length}
          </Text>
          {!quizState.retryMode && (
            <TimerComponent
              initialTime={quizState.timeRemaining}
              onTimeUp={handleTimeUp}
            />
          )}
        </View>
        
        <View style={styles.progressBar}>
          <Animated.View
            style={[
              styles.progressFill,
              {
                width: progressAnim.interpolate({
                  inputRange: [0, 100],
                  outputRange: ['0%', '100%'],
                  extrapolate: 'clamp',
                }),
              },
            ]}
          />
        </View>
      </View>

      {/* Navigation Buttons */}
      <View style={styles.navigationContainer}>
        <TouchableOpacity
          style={[styles.navButton, quizState.currentQuestionIndex === 0 && styles.navButtonDisabled]}
          onPress={handlePreviousQuestion}
          disabled={quizState.currentQuestionIndex === 0}
        >
          <MaterialIcons name="arrow-back" size={20} color={quizState.currentQuestionIndex === 0 ? '#ccc' : '#6C5CE7'} />
          <Text style={[styles.navButtonText, quizState.currentQuestionIndex === 0 && styles.navButtonTextDisabled]}>
            Önceki
          </Text>
        </TouchableOpacity>

        <View style={styles.scoreContainer}>
          <Animated.Text style={styles.scoreText}>
            {scoreAnim.interpolate({
              inputRange: [0, 1000],
              outputRange: ['0', '1000'],
              extrapolate: 'clamp',
            })}
          </Animated.Text>
          <Text style={styles.scoreLabel}>Puan</Text>
        </View>

        <TouchableOpacity
          style={[styles.navButton, quizState.currentQuestionIndex === questions.length - 1 && styles.navButtonDisabled]}
          onPress={handleNextQuestion}
          disabled={quizState.currentQuestionIndex === questions.length - 1}
        >
          <Text style={[styles.navButtonText, quizState.currentQuestionIndex === questions.length - 1 && styles.navButtonTextDisabled]}>
            Sonraki
          </Text>
          <MaterialIcons name="arrow-forward" size={20} color={quizState.currentQuestionIndex === questions.length - 1 ? '#ccc' : '#6C5CE7'} />
        </TouchableOpacity>
      </View>

      {/* Question Card */}
      <View style={styles.questionContainer}>
        <QuestionCard
          question={currentQuestion}
          onAnswer={handleAnswer}
          disabled={hasAnswered}
          selectedAnswer={hasAnswered ? selectedAnswer : ''}
        />

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          {!hasAnswered && (
            <TouchableOpacity
              style={styles.skipButton}
              onPress={handleSkipQuestion}
            >
              <MaterialIcons name="skip-next" size={20} color="#6C5CE7" />
              <Text style={styles.skipButtonText}>Soruyu Geç</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>



      {/* Question Navigator Modal */}
      {renderQuestionNavigator()}

      {/* Results Modal */}
      {showResults && (
        <View style={styles.resultsOverlay}>
          <View style={styles.resultsModal}>
            <LinearGradient
              colors={['#6C5CE7', '#A29BFE']}
              style={styles.resultsHeader}
            >
              <MaterialIcons name="emoji-events" size={40} color="white" />
              <Text style={styles.resultsTitle}>
                {quizState.retryMode ? 'Tekrar Tamamlandı!' : 'Quiz Tamamlandı!'} 🎉
              </Text>
            </LinearGradient>

            <View style={styles.resultsContent}>
              {(() => {
                const results = getQuizResults();
                const successRate = (results.correct / results.total) * 100;
                const isPassed = successRate >= 90;
                
                return (
                  <>
                    <View style={styles.resultsStats}>
                      <View style={styles.resultStat}>
                        <MaterialIcons name="check-circle" size={24} color="#27AE60" />
                        <Text style={styles.resultStatText}>{results.correct} Doğru</Text>
                      </View>
                      <View style={styles.resultStat}>
                        <MaterialIcons name="cancel" size={24} color="#E74C3C" />
                        <Text style={styles.resultStatText}>{results.wrong} Yanlış</Text>
                      </View>
                      <View style={styles.resultStat}>
                        <MaterialIcons name="skip-next" size={24} color="#F39C12" />
                        <Text style={styles.resultStatText}>{results.skipped} Geçildi</Text>
                      </View>
                    </View>

                    <Text style={styles.resultsScore}>
                      Skorun: {quizState.score} / {results.total * 20}
                    </Text>

                    <View style={styles.successRateContainer}>
                      <Text style={styles.successRateText}>
                        Başarı Oranı: %{Math.round(successRate)}
                      </Text>
                      {isPassed ? (
                        <View style={styles.passedContainer}>
                          <MaterialIcons name="check-circle" size={20} color="#27AE60" />
                          <Text style={styles.passedText}>
                            {quizState.retryMode 
                              ? 'Tebrikler! Yanlış soruları doğru cevapladın! 🎉' 
                              : 'Geçtin! Sonraki ders açıldı 🎉'}
                          </Text>
                        </View>
                      ) : (
                        <View style={styles.failedContainer}>
                          <MaterialIcons name="cancel" size={20} color="#E74C3C" />
                          <Text style={styles.failedText}>
                            %90 başarı gerekli. {(quizState.wrongQuestions?.length || 0) > 0 && 'Yanlış soruları tekrar çözebilirsin!'}
                          </Text>
                        </View>
                      )}
                    </View>

                    <View style={styles.resultsActions}>
                      {!quizState.retryMode && (
                        <TouchableOpacity
                          style={[styles.resultsButton, styles.retryAllButton]}
                          onPress={() => {
                            startFullRetry();
                          }}
                        >
                          <MaterialIcons name="replay" size={20} color="white" />
                          <Text style={styles.resultsButtonText}>
                            Testi Tekrar Çöz
                          </Text>
                        </TouchableOpacity>
                      )}

                      {!isPassed && (quizState.wrongQuestions?.length || 0) > 0 && !quizState.retryMode && (
                        <TouchableOpacity
                          style={[styles.resultsButton, styles.retryButton]}
                          onPress={() => {
                            startRetryMode();
                          }}
                        >
                          <MaterialIcons name="refresh" size={20} color="white" />
                          <Text style={styles.resultsButtonText}>
                            Yanlışları Çöz ({quizState.wrongQuestions?.length || 0})
                          </Text>
                        </TouchableOpacity>
                      )}

                      <TouchableOpacity
                        style={styles.resultsButton}
                        onPress={() => navigation.goBack()}
                      >
                        <Text style={styles.resultsButtonText}>Gün Detayına Dön</Text>
                      </TouchableOpacity>
                    </View>
                  </>
                );
              })()}
            </View>
          </View>
        </View>
      )}

      {/* Bottom Stats */}
      <View style={styles.bottomStats}>
        <View style={styles.statItem}>
          <MaterialIcons name="check-circle" size={20} color="#27AE60" />
          <Text style={styles.statText}>{answeredStats.correct} Doğru</Text>
        </View>

        <View style={styles.statItem}>
          <MaterialIcons name="cancel" size={20} color="#E74C3C" />
          <Text style={styles.statText}>{answeredStats.wrong} Yanlış</Text>
        </View>

        <View style={styles.statItem}>
          <MaterialIcons name="skip-next" size={20} color="#F39C12" />
          <Text style={styles.statText}>{answeredStats.skipped} Geçildi</Text>
        </View>

        {quizState.retryMode && (
          <View style={styles.statItem}>
            <MaterialIcons name="error-outline" size={20} color="#E74C3C" />
            <Text style={styles.statText}>{quizState.wrongQuestions?.length || 0} Hatalı</Text>
          </View>
        )}
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    color: 'white',
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 2,
  },
  navigatorButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scoreContainer: {
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.1)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  scoreText: {
    fontSize: 18,
    color: '#00CEFF',
    fontWeight: 'bold',
  },
  scoreLabel: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 2,
  },
  progressContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  progressText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#00CEFF',
    borderRadius: 4,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.1)',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#6C5CE7',
  },
  navButtonDisabled: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderColor: '#ccc',
  },
  navButtonText: {
    color: '#6C5CE7',
    fontSize: 14,
    fontWeight: '600',
    marginHorizontal: 5,
  },
  navButtonTextDisabled: {
    color: '#ccc',
  },
  questionContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  actionButtons: {
    marginTop: 15,
  },
  skipButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255,255,255,0.1)',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: '#6C5CE7',
  },
  skipButtonText: {
    color: '#6C5CE7',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  finishButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#27AE60',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
  },
  finishButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  bottomStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  // Question Navigator Styles
  navigatorOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  navigatorContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    margin: 20,
    maxWidth: 400,
    width: '90%',
    maxHeight: '80%',
  },
  navigatorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  navigatorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  navigatorClose: {
    padding: 5,
  },
  navigatorContent: {
    padding: 20,
  },
  navigatorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  navigatorItem: {
    width: '18%',
    aspectRatio: 1,
    backgroundColor: '#f0f0f0',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  navigatorItemCurrent: {
    borderColor: '#6C5CE7',
    backgroundColor: 'rgba(108, 92, 231, 0.1)',
  },
  navigatorItemCorrect: {
    backgroundColor: 'rgba(39, 174, 96, 0.1)',
    borderColor: '#27AE60',
  },
  navigatorItemWrong: {
    backgroundColor: 'rgba(231, 76, 60, 0.1)',
    borderColor: '#E74C3C',
  },
  navigatorItemSkipped: {
    backgroundColor: 'rgba(243, 156, 18, 0.1)',
    borderColor: '#F39C12',
  },
  navigatorItemText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  navigatorItemTextCurrent: {
    color: '#6C5CE7',
  },
  navigatorItemTextAnswered: {
    fontSize: 14,
  },
  navigatorLegend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 4,
    marginRight: 5,
    borderWidth: 2,
  },
  legendText: {
    fontSize: 12,
    color: '#666',
  },
  // Results Modal Styles
  resultsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  resultsModal: {
    backgroundColor: 'white',
    borderRadius: 20,
    margin: 20,
    maxWidth: 350,
    width: '90%',
    overflow: 'hidden',
  },
  resultsHeader: {
    padding: 30,
    alignItems: 'center',
  },
  resultsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 10,
    textAlign: 'center',
  },
  resultsContent: {
    padding: 20,
  },
  resultsStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  resultStat: {
    alignItems: 'center',
  },
  resultStatText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
    marginTop: 5,
  },
  resultsScore: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6C5CE7',
    textAlign: 'center',
    marginBottom: 20,
  },
  resultsActions: {
    marginTop: 20,
  },
  resultsButton: {
    backgroundColor: '#6C5CE7',
    paddingVertical: 15,
    borderRadius: 25,
    alignItems: 'center',
    marginBottom: 10,
  },
  retryButton: {
    backgroundColor: '#E74C3C',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  retryAllButton: {
    backgroundColor: '#3498DB',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  resultsButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  successRateContainer: {
    marginTop: 15,
    padding: 15,
    backgroundColor: 'rgba(108, 92, 231, 0.1)',
    borderRadius: 10,
  },
  successRateText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginBottom: 10,
  },
  passedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(39, 174, 96, 0.1)',
    padding: 10,
    borderRadius: 8,
  },
  passedText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#27AE60',
    marginLeft: 8,
    textAlign: 'center',
  },
  failedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(231, 76, 60, 0.1)',
    padding: 10,
    borderRadius: 8,
  },
  failedText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#E74C3C',
    marginLeft: 8,
    textAlign: 'center',
  },
});

export default QuizScreen;