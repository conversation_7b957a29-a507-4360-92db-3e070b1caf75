import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Animated,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import { SolutionModalProps } from '../types';

const { width, height } = Dimensions.get('window');

const SolutionModal: React.FC<SolutionModalProps> = ({
  isVisible,
  question,
  selectedAnswer,
  isCorrect,
  onClose,
  onNext,
}) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(height)).current;
  const iconScaleAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isVisible) {
      // Modal açılma animasyonu
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }),
      ]).start();

      // İkon animasyonu (gecikme ile)
      setTimeout(() => {
        Animated.spring(iconScaleAnim, {
          toValue: 1,
          tension: 150,
          friction: 6,
          useNativeDriver: true,
        }).start();
      }, 200);
    } else {
      // Modal kapanma animasyonu
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: height,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
      
      iconScaleAnim.setValue(0);
    }
  }, [isVisible]);

  const getResultIcon = () => {
    return isCorrect ? 'check-circle' : 'cancel';
  };

  const getResultColor = () => {
    return isCorrect ? '#27AE60' : '#E74C3C';
  };

  const getResultTitle = () => {
    return isCorrect ? 'Tebrikler! 🎉' : 'Yanlış Cevap 😔';
  };

  const getResultMessage = () => {
    return isCorrect 
      ? 'Doğru cevabı verdin! +20 puan kazandın.'
      : 'Üzülme, bir dahaki sefere daha dikkatli ol.';
  };

  const getGradientColors = () => {
    return isCorrect 
      ? ['#27AE60', '#2ECC71', '#58D68D']
      : ['#E74C3C', '#EC7063', '#F1948A'];
  };

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.modalContainer,
            {
              transform: [
                { scale: scaleAnim },
                { translateY: slideAnim },
              ],
            },
          ]}
        >
          <LinearGradient
            colors={getGradientColors()}
            style={styles.modalContent}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            {/* Sonuç İkonu */}
            <Animated.View
              style={[
                styles.resultIconContainer,
                {
                  transform: [{ scale: iconScaleAnim }],
                },
              ]}
            >
              <MaterialIcons
                name={getResultIcon()}
                size={80}
                color="white"
              />
            </Animated.View>

            {/* Sonuç Başlığı */}
            <Text style={styles.resultTitle}>{getResultTitle()}</Text>
            <Text style={styles.resultMessage}>{getResultMessage()}</Text>

            {/* Cevap Bilgisi */}
            <View style={styles.answerInfo}>
              <View style={styles.answerRow}>
                <Text style={styles.answerLabel}>Senin Cevabın:</Text>
                <View style={[
                  styles.answerBadge,
                  { backgroundColor: isCorrect ? '#27AE60' : '#E74C3C' }
                ]}>
                  <Text style={styles.answerText}>{selectedAnswer}</Text>
                </View>
              </View>

              <View style={styles.answerRow}>
                <Text style={styles.answerLabel}>Doğru Cevap:</Text>
                <View style={[styles.answerBadge, { backgroundColor: '#27AE60' }]}>
                  <Text style={styles.answerText}>{question.cevap}</Text>
                </View>
              </View>
            </View>

            {/* Açıklama */}
            <View style={styles.explanationContainer}>
              <View style={styles.explanationHeader}>
                <MaterialIcons name="lightbulb" size={20} color="white" />
                <Text style={styles.explanationTitle}>Açıklama</Text>
              </View>
              <Text style={styles.explanationText}>{question.aciklama}</Text>
            </View>

            {/* Aksiyon Butonları */}
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={styles.nextButton}
                onPress={onNext}
              >
                <MaterialIcons name="arrow-forward" size={24} color="white" />
                <Text style={styles.nextButtonText}>Sonraki Soru</Text>
              </TouchableOpacity>
            </View>

            {/* Dekoratif Elementler */}
            <View style={styles.decorativeElements}>
              <View style={[styles.circle, styles.circle1]} />
              <View style={[styles.circle, styles.circle2]} />
              <View style={[styles.circle, styles.circle3]} />
            </View>
          </LinearGradient>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContainer: {
    width: width - 40,
    maxHeight: height * 0.8,
    borderRadius: 25,
    overflow: 'hidden',
  },
  modalContent: {
    padding: 30,
    alignItems: 'center',
    position: 'relative',
  },
  resultIconContainer: {
    marginBottom: 20,
  },
  resultTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },
  resultMessage: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.9)',
    textAlign: 'center',
    marginBottom: 25,
    lineHeight: 22,
  },
  answerInfo: {
    width: '100%',
    marginBottom: 25,
  },
  answerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  answerLabel: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.9)',
    fontWeight: '600',
  },
  answerBadge: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 50,
    alignItems: 'center',
  },
  answerText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  explanationContainer: {
    width: '100%',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 15,
    padding: 20,
    marginBottom: 25,
  },
  explanationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  explanationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginLeft: 8,
  },
  explanationText: {
    fontSize: 15,
    color: 'rgba(255,255,255,0.9)',
    lineHeight: 22,
  },
  actionButtons: {
    width: '100%',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  nextButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  decorativeElements: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  circle: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 50,
  },
  circle1: {
    width: 100,
    height: 100,
    top: -30,
    right: -30,
  },
  circle2: {
    width: 60,
    height: 60,
    bottom: -20,
    left: -20,
  },
  circle3: {
    width: 40,
    height: 40,
    top: 100,
    left: 30,
  },
});

export default SolutionModal;
