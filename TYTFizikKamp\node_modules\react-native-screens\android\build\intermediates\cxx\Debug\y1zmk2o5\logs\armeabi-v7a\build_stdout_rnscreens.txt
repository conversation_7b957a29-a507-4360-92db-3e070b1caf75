ninja: Entering directory `C:\Users\<USER>\Desktop\30days15mobileapp\TYTFizikKamp\node_modules\react-native-screens\android\.cxx\Debug\y1zmk2o5\armeabi-v7a'
[1/6] Building CXX object CMakeFiles/rnscreens.dir/2b9cdc903ce17ebb479edbb268daa6f0/react-native-screens/cpp/RNScreensTurboModule.cpp.o
[2/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o
[3/6] Building CXX object CMakeFiles/rnscreens.dir/2b9cdc903ce17ebb479edbb268daa6f0/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o
[4/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o
[5/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o
[6/6] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\y1zmk2o5\obj\armeabi-v7a\librnscreens.so
