import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Linking,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import BackReferenceCard from '../components/BackReferenceCard';
import VideoPlayer from '../components/VideoPlayer';
import StorageService, { DayProgress } from '../services/StorageService';
import TimeValidationService from '../services/TimeValidationService';
import lessonsData from '../data/lessons.json';
import { Lesson } from '../types';

const { width, height } = Dimensions.get('window');

// Import type from App.tsx
import { DayDetailScreenProps } from '../../App';

const DayDetailScreen: React.FC<DayDetailScreenProps> = ({ navigation, route }) => {
  const { dayId } = route.params;
  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [dayProgress, setDayProgress] = useState<DayProgress | null>(null);
  const [loading, setLoading] = useState(true);
  const [canComplete, setCanComplete] = useState(false);

  useEffect(() => {
    loadLessonData();
  }, [dayId]);

  const loadLessonData = async () => {
    try {
      setLoading(true);
      
      // Ders verisini bul
      const lessonData = lessonsData.find(l => l.gun === dayId);
      if (!lessonData) {
        Alert.alert('Hata', 'Ders bulunamadı!');
        navigation.goBack();
        return;
      }

      setLesson(lessonData as Lesson);

      // Gün ilerlemesini yükle
      const userProgress = await StorageService.getUserProgress();
      const progress = userProgress.day_progress[dayId] || { score: 0, questionsAnswered: [], completedAt: null };
      setDayProgress(progress);

      // Tamamlama kontrolü
      const completionCheck = await TimeValidationService.canCompleteNewDay();
      setCanComplete(completionCheck.canComplete);

    } catch (error) {
      console.error('Error loading lesson data:', error);
      Alert.alert('Hata', 'Ders yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleVideoPress = async () => {
    if (!lesson?.video_link) return;

    try {
      const supported = await Linking.canOpenURL(lesson.video_link);
      if (supported) {
        await Linking.openURL(lesson.video_link);
      } else {
        Alert.alert('Hata', 'Video açılamadı!');
      }
    } catch (error) {
      console.error('Error opening video:', error);
      Alert.alert('Hata', 'Video açılırken bir hata oluştu.');
    }
  };

  const handleStartQuiz = () => {
    if (!lesson) return;
    navigation.navigate('Quiz', { dayId: lesson.gun });
  };

  const handleCompleteDay = async () => {
    if (!lesson) return;

    try {
      // Günü tamamla (örnek değerlerle)
      await StorageService.completeDay(lesson.gun, 100, 3600); // 100% skor, 1 saat

      // Başarılı tamamlama
      Alert.alert(
        'Tebrikler! 🎉',
        `${lesson.gun}. günü başarıyla tamamladın!`,
        [
          {
            text: 'Ana Sayfaya Dön',
            onPress: () => navigation.navigate('Main'),
          },
        ]
      );
    } catch (error) {
      console.error('Error completing day:', error);
      Alert.alert('Hata', 'Gün tamamlanırken bir hata oluştu.');
    }
  };

  if (loading || !lesson) {
    return (
      <LinearGradient
        colors={['#6C5CE7', '#A29BFE']}
        style={styles.loadingContainer}
      >
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </LinearGradient>
    );
  }

  return (
    <LinearGradient
      colors={['#6C5CE7', '#A29BFE', '#F5F6FA']}
      style={styles.container}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={styles.dayNumber}>Gün {lesson.gun}</Text>
          <Text style={styles.lessonTitle}>{lesson.konu}</Text>
        </View>

        <View style={styles.headerRight}>
          <MaterialIcons name="school" size={24} color="white" />
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Geriye Dönük Hatırlatma */}
        {lesson.geri_baglanti && (
          <BackReferenceCard
            question={lesson.geri_baglanti.soru}
            answer={lesson.geri_baglanti.cevap}
            hint={lesson.geri_baglanti.ipucu}
          />
        )}

        {/* Video Bölümü */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📹 Video Ders</Text>
          <VideoPlayer
            videoUrl={lesson.video_link}
            onPress={handleVideoPress}
            thumbnail={`https://img.youtube.com/vi/${lesson.video_link.split('/').pop()}/maxresdefault.jpg`}
          />
        </View>

        {/* Özet Bölümü */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📚 Konu Özeti</Text>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryText}>{lesson.ozet}</Text>
          </View>
        </View>

        {/* Gerçek Hayat Örnekleri */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🌟 Gerçek Hayattan Örnekler</Text>
          <View style={styles.exampleCard}>
            <MaterialIcons name="lightbulb" size={24} color="#F39C12" style={styles.exampleIcon} />
            <Text style={styles.exampleText}>{lesson.gercek_hayat}</Text>
          </View>
        </View>

        {/* Motivasyon Mesajı */}
        <View style={styles.section}>
          <View style={styles.motivationCard}>
            <Text style={styles.motivationText}>{lesson.motivasyon}</Text>
          </View>
        </View>

        {/* Aksiyon Butonları */}
        <View style={styles.actionSection}>
          <TouchableOpacity
            style={styles.quizButton}
            onPress={handleStartQuiz}
          >
            <LinearGradient
              colors={['#00CEFF', '#0984E3']}
              style={styles.buttonGradient}
            >
              <MaterialIcons name="quiz" size={24} color="white" />
              <Text style={styles.buttonText}>Soruları Çöz</Text>
            </LinearGradient>
          </TouchableOpacity>

          {canComplete && (
            <TouchableOpacity
              style={styles.completeButton}
              onPress={handleCompleteDay}
            >
              <LinearGradient
                colors={['#27AE60', '#2ECC71']}
                style={styles.buttonGradient}
              >
                <MaterialIcons name="check-circle" size={24} color="white" />
                <Text style={styles.buttonText}>Günü Bitir</Text>
              </LinearGradient>
            </TouchableOpacity>
          )}
        </View>

        {/* İlerleme Bilgisi */}
        {dayProgress && (
          <View style={styles.progressInfo}>
            <Text style={styles.progressTitle}>Bu Günkü İlerlemen</Text>
            <View style={styles.progressStats}>
              <View style={styles.progressStat}>
                <Text style={styles.progressNumber}>{dayProgress.questionsAnswered.length}</Text>
                <Text style={styles.progressLabel}>Çözülen Soru</Text>
              </View>
              <View style={styles.progressStat}>
                <Text style={styles.progressNumber}>{dayProgress.score}</Text>
                <Text style={styles.progressLabel}>Puan</Text>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  dayNumber: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.8)',
    fontWeight: '500',
  },
  lessonTitle: {
    fontSize: 20,
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 4,
  },
  headerRight: {
    width: 40,
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 12,
  },
  summaryCard: {
    backgroundColor: 'rgba(255,255,255,0.95)',
    borderRadius: 15,
    padding: 20,
  },
  summaryText: {
    fontSize: 16,
    color: '#2C3E50',
    lineHeight: 24,
  },
  exampleCard: {
    backgroundColor: 'rgba(255,255,255,0.95)',
    borderRadius: 15,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  exampleIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  exampleText: {
    flex: 1,
    fontSize: 16,
    color: '#2C3E50',
    lineHeight: 24,
  },
  motivationCard: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 15,
    padding: 20,
    alignItems: 'center',
  },
  motivationText: {
    fontSize: 18,
    color: 'white',
    fontWeight: '600',
    textAlign: 'center',
  },
  actionSection: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  quizButton: {
    marginBottom: 12,
    borderRadius: 15,
    overflow: 'hidden',
  },
  completeButton: {
    borderRadius: 15,
    overflow: 'hidden',
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  progressInfo: {
    marginHorizontal: 20,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 15,
    padding: 20,
  },
  progressTitle: {
    fontSize: 16,
    color: 'white',
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 15,
  },
  progressStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  progressStat: {
    alignItems: 'center',
  },
  progressNumber: {
    fontSize: 24,
    color: '#00CEFF',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  progressLabel: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
  },
});

export default DayDetailScreen;
