{"name": "react-native-sound", "version": "0.11.2", "description": "React Native module for playing sound clips on iOS, Android, and Windows", "main": "sound.js", "typings": "index.d.ts", "repository": "zmxv/react-native-sound", "keywords": ["react-native", "sound", "audio", "ios", "android", "windows"], "author": "<PERSON><PERSON> <<EMAIL>> (http://blog.zmxv.com)", "license": "MIT", "peerDependencies": {"react-native": ">=0.8.0"}, "scripts": {"clang-format": "node scripts/clang-format.js", "clang-format:check": "node scripts/clang-format.js --check"}, "devDependencies": {"glob": "^7.1.4", "promisify-child-process": "^3.1.1"}, "files": ["**/*.@(js|ts)", "*.podsp<PERSON>", "android/build.gradle", "android/src/**/*.@(java|xml)", "RNSound.xcodeproj/project.pbxproj", "RNSound.xcodeproj/project.xcworkspace/contents.xcworkspacedata", "RNSound/**/*.@(h|m)", "windows/RNSoundModule/**/*.@(sln|json|xml|csproj)", "windows/RNSoundModule/**/*.cs", "!scripts"]}