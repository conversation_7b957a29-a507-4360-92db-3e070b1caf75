com.tytfizikkamp:styleable/ViewBackgroundHelper = 0x7f120093
com.tytfizikkamp:styleable/Transition = 0x7f120090
com.tytfizikkamp:styleable/Transform = 0x7f12008f
com.tytfizikkamp:styleable/Toolbar = 0x7f12008d
com.tytfizikkamp:styleable/StateListDrawable = 0x7f120081
com.tytfizikkamp:styleable/Snackbar = 0x7f12007d
com.tytfizikkamp:styleable/ShapeableImageView = 0x7f120079
com.tytfizikkamp:styleable/ShapeAppearance = 0x7f120078
com.tytfizikkamp:styleable/SnackbarLayout = 0x7f12007e
com.tytfizikkamp:styleable/SearchBar = 0x7f120076
com.tytfizikkamp:styleable/RecycleListView = 0x7f120072
com.tytfizikkamp:styleable/RangeSlider = 0x7f120071
com.tytfizikkamp:styleable/OnSwipe = 0x7f12006c
com.tytfizikkamp:styleable/OnClick = 0x7f12006b
com.tytfizikkamp:styleable/NavigationView = 0x7f12006a
com.tytfizikkamp:styleable/NavigationRailView = 0x7f120069
com.tytfizikkamp:styleable/NavigationBarView = 0x7f120068
com.tytfizikkamp:styleable/NavigationBarActiveIndicator = 0x7f120067
com.tytfizikkamp:styleable/MotionLayout = 0x7f120064
com.tytfizikkamp:styleable/MotionHelper = 0x7f120063
com.tytfizikkamp:styleable/Motion = 0x7f120062
com.tytfizikkamp:styleable/MenuView = 0x7f120060
com.tytfizikkamp:styleable/MaterialTextAppearance = 0x7f12005a
com.tytfizikkamp:styleable/MaterialShape = 0x7f120058
com.tytfizikkamp:styleable/MaterialCheckBox = 0x7f120054
com.tytfizikkamp:styleable/MaterialCardView = 0x7f120053
com.tytfizikkamp:styleable/MaterialButtonToggleGroup = 0x7f120050
com.tytfizikkamp:styleable/MaterialAutoCompleteTextView = 0x7f12004e
com.tytfizikkamp:styleable/LinearProgressIndicator = 0x7f12004a
com.tytfizikkamp:styleable/KeyTimeCycle = 0x7f120045
com.tytfizikkamp:styleable/KeyFramesAcceleration = 0x7f120042
com.tytfizikkamp:styleable/KeyFrame = 0x7f120041
com.tytfizikkamp:styleable/KeyCycle = 0x7f120040
com.tytfizikkamp:styleable/GradientColorItem = 0x7f12003c
com.tytfizikkamp:styleable/FontFamilyFont = 0x7f120036
com.tytfizikkamp:styleable/ExtendedFloatingActionButton = 0x7f120030
com.tytfizikkamp:styleable/DrawerLayout = 0x7f12002f
com.tytfizikkamp:styleable/CoordinatorLayout = 0x7f12002b
com.tytfizikkamp:styleable/ConstraintLayout_placeholder = 0x7f120029
com.tytfizikkamp:styleable/Constraint = 0x7f120027
com.tytfizikkamp:styleable/ColorStateListItem = 0x7f120025
com.tytfizikkamp:styleable/ClockHandView = 0x7f120022
com.tytfizikkamp:styleable/ClockFaceView = 0x7f120021
com.tytfizikkamp:styleable/CircularProgressIndicator = 0x7f120020
com.tytfizikkamp:styleable/ChipGroup = 0x7f12001f
com.tytfizikkamp:styleable/BottomSheetBehavior_Layout = 0x7f120018
com.tytfizikkamp:styleable/BottomNavigationView = 0x7f120017
com.tytfizikkamp:styleable/BottomAppBar = 0x7f120016
com.tytfizikkamp:styleable/AppCompatTextHelper = 0x7f120010
com.tytfizikkamp:styleable/AppBarLayout_Layout = 0x7f12000c
com.tytfizikkamp:styleable/AnimatedStateListDrawableCompat = 0x7f120007
com.tytfizikkamp:styleable/ActivityChooserView = 0x7f120005
com.tytfizikkamp:styleable/ActionMenuView = 0x7f120003
com.tytfizikkamp:styleable/ActionMenuItemView = 0x7f120002
com.tytfizikkamp:style/Widget.Support.CoordinatorLayout = 0x7f110480
com.tytfizikkamp:style/Widget.MaterialComponents.Tooltip = 0x7f11047f
com.tytfizikkamp:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f11047e
com.tytfizikkamp:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f11047c
com.tytfizikkamp:style/Widget.MaterialComponents.Toolbar = 0x7f11047b
com.tytfizikkamp:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f110478
com.tytfizikkamp:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f110477
com.tytfizikkamp:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f110476
com.tytfizikkamp:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f110473
com.tytfizikkamp:style/Widget.MaterialComponents.TimePicker.Button = 0x7f110472
com.tytfizikkamp:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f11046b
com.tytfizikkamp:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f11046a
com.tytfizikkamp:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f110468
com.tytfizikkamp:style/Widget.MaterialComponents.TabLayout = 0x7f110461
com.tytfizikkamp:style/Widget.MaterialComponents.Snackbar = 0x7f11045e
com.tytfizikkamp:style/Widget.MaterialComponents.ShapeableImageView = 0x7f11045c
com.tytfizikkamp:style/Widget.MaterialComponents.ProgressIndicator = 0x7f11045b
com.tytfizikkamp:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f11045a
com.tytfizikkamp:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f110459
com.tytfizikkamp:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f110458
com.tytfizikkamp:style/Widget.MaterialComponents.PopupMenu = 0x7f110457
com.tytfizikkamp:style/Widget.MaterialComponents.NavigationView = 0x7f110456
com.tytfizikkamp:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f110455
com.tytfizikkamp:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f110453
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f11044f
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f11044e
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f11044d
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f11044c
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f11044a
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f110449
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f110447
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f110445
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f110440
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f11043c
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f110439
com.tytfizikkamp:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f110436
com.tytfizikkamp:style/Widget.MaterialComponents.FloatingActionButton = 0x7f110434
com.tytfizikkamp:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f11042f
com.tytfizikkamp:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f11042e
com.tytfizikkamp:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f11042b
com.tytfizikkamp:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f11042a
com.tytfizikkamp:style/Widget.MaterialComponents.ChipGroup = 0x7f110429
com.tytfizikkamp:style/Widget.MaterialComponents.Chip.Filter = 0x7f110428
com.tytfizikkamp:style/Widget.MaterialComponents.Chip.Entry = 0x7f110427
com.tytfizikkamp:style/Widget.MaterialComponents.Chip.Choice = 0x7f110426
com.tytfizikkamp:style/Widget.MaterialComponents.Chip.Action = 0x7f110425
com.tytfizikkamp:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f110421
com.tytfizikkamp:styleable/GradientColor = 0x7f12003b
com.tytfizikkamp:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f11041c
com.tytfizikkamp:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f110419
com.tytfizikkamp:style/Widget.MaterialComponents.BottomSheet = 0x7f110415
com.tytfizikkamp:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f110410
com.tytfizikkamp:style/Widget.MaterialComponents.BottomAppBar = 0x7f11040f
com.tytfizikkamp:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f11040b
com.tytfizikkamp:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f110407
com.tytfizikkamp:styleable/DrawerArrowToggle = 0x7f12002e
com.tytfizikkamp:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f110404
com.tytfizikkamp:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f110403
com.tytfizikkamp:style/Widget.Material3.Tooltip = 0x7f110401
com.tytfizikkamp:style/Widget.Material3.Toolbar.Surface = 0x7f110400
com.tytfizikkamp:style/Widget.Material3.Toolbar.OnSurface = 0x7f1103ff
com.tytfizikkamp:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f1103fd
com.tytfizikkamp:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f1103f2
com.tytfizikkamp:style/Widget.Material3.TabLayout.OnSurface = 0x7f1103f0
com.tytfizikkamp:style/Widget.Material3.TabLayout = 0x7f1103ef
com.tytfizikkamp:style/Widget.Material3.Snackbar.FullWidth = 0x7f1103ed
com.tytfizikkamp:style/Widget.Material3.Snackbar = 0x7f1103ec
com.tytfizikkamp:style/Widget.Material3.Slider.Legacy = 0x7f1103ea
com.tytfizikkamp:style/Widget.Material3.Slider = 0x7f1103e8
com.tytfizikkamp:style/Widget.Material3.SearchView.Prefix = 0x7f1103e2
com.tytfizikkamp:style/Widget.Material3.PopupMenu.Overflow = 0x7f1103dc
com.tytfizikkamp:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1103da
com.tytfizikkamp:style/Widget.Material3.PopupMenu = 0x7f1103d9
com.tytfizikkamp:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1103d6
com.tytfizikkamp:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1103d3
com.tytfizikkamp:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1103d2
com.tytfizikkamp:style/Widget.Material3.MaterialDivider = 0x7f1103ca
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1103c9
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.Year = 0x7f1103c6
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.Item = 0x7f1103c3
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1103bf
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f1103be
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1103bc
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1103b7
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f110448
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar = 0x7f1103b3
com.tytfizikkamp:style/Widget.Material3.LinearProgressIndicator.Legacy = 0x7f1103b1
com.tytfizikkamp:style/Widget.Material3.LinearProgressIndicator = 0x7f1103b0
com.tytfizikkamp:style/Widget.Material3.Light.ActionBar.Solid = 0x7f1103af
com.tytfizikkamp:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f1103ac
com.tytfizikkamp:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f1103a4
com.tytfizikkamp:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f1103a1
com.tytfizikkamp:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1103a0
com.tytfizikkamp:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f11039f
com.tytfizikkamp:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f11039d
com.tytfizikkamp:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f11039c
com.tytfizikkamp:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f110397
com.tytfizikkamp:style/Widget.Material3.CompoundButton.CheckBox = 0x7f110396
com.tytfizikkamp:style/Widget.Material3.CircularProgressIndicator.Legacy.Medium = 0x7f11038f
com.tytfizikkamp:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f110389
com.tytfizikkamp:style/Widget.Material3.Chip.Input.Icon = 0x7f110386
com.tytfizikkamp:style/Widget.Material3.Chip.Filter.Elevated = 0x7f110383
com.tytfizikkamp:style/Widget.Material3.Chip.Assist.Elevated = 0x7f110381
com.tytfizikkamp:style/Widget.Material3.CardView.Elevated = 0x7f11037c
com.tytfizikkamp:style/Widget.Material3.Button.TonalButton = 0x7f110379
com.tytfizikkamp:style/Widget.Material3.Button.TextButton.Icon = 0x7f110377
com.tytfizikkamp:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f110375
com.tytfizikkamp:style/Widget.Material3.Button.IconButton.Outlined = 0x7f110370
com.tytfizikkamp:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f11036f
com.tytfizikkamp:style/Widget.Material3.Button.IconButton = 0x7f11036d
com.tytfizikkamp:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f11036b
com.tytfizikkamp:style/Widget.Material3.BottomSheet.DragHandle = 0x7f110367
com.tytfizikkamp:style/Widget.Material3.BottomNavigation.Badge = 0x7f110363
com.tytfizikkamp:style/Widget.Material3.BottomAppBar.Legacy = 0x7f110362
com.tytfizikkamp:style/Widget.Material3.BottomAppBar = 0x7f110360
com.tytfizikkamp:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f11035d
com.tytfizikkamp:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f11035c
com.tytfizikkamp:style/Widget.Material3.AppBarLayout = 0x7f110359
com.tytfizikkamp:style/Widget.Design.TextInputLayout = 0x7f110356
com.tytfizikkamp:style/Widget.Design.TextInputEditText = 0x7f110355
com.tytfizikkamp:style/Widget.Design.TabLayout = 0x7f110354
com.tytfizikkamp:style/Widget.Design.Snackbar = 0x7f110353
com.tytfizikkamp:style/Widget.Design.NavigationView = 0x7f110351
com.tytfizikkamp:style/Widget.Design.BottomNavigationView = 0x7f11034d
com.tytfizikkamp:style/Widget.Design.AppBarLayout = 0x7f11034c
com.tytfizikkamp:style/Widget.Autofill.InlineSuggestionTitle = 0x7f110349
com.tytfizikkamp:style/Widget.Autofill.InlineSuggestionEndIconStyle = 0x7f110346
com.tytfizikkamp:style/Widget.Autofill.InlineSuggestionChip = 0x7f110345
com.tytfizikkamp:style/Widget.AppCompat.Toolbar = 0x7f110342
com.tytfizikkamp:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f110341
com.tytfizikkamp:style/Widget.AppCompat.Spinner.DropDown = 0x7f11033d
com.tytfizikkamp:style/Widget.AppCompat.SeekBar.Discrete = 0x7f11033b
com.tytfizikkamp:style/Widget.AppCompat.SeekBar = 0x7f11033a
com.tytfizikkamp:style/Widget.AppCompat.SearchView.ActionBar = 0x7f110339
com.tytfizikkamp:style/Widget.AppCompat.SearchView = 0x7f110338
com.tytfizikkamp:style/Widget.AppCompat.RatingBar.Indicator = 0x7f110336
com.tytfizikkamp:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f110334
com.tytfizikkamp:style/Widget.AppCompat.ProgressBar = 0x7f110333
com.tytfizikkamp:style/Widget.AppCompat.PopupWindow = 0x7f110332
com.tytfizikkamp:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f110331
com.tytfizikkamp:styleable/ScrimInsetsFrameLayout = 0x7f120074
com.tytfizikkamp:style/Widget.AppCompat.PopupMenu = 0x7f110330
com.tytfizikkamp:style/Widget.AppCompat.ListView.Menu = 0x7f11032f
com.tytfizikkamp:style/Widget.AppCompat.ListView = 0x7f11032d
com.tytfizikkamp:style/Widget.AppCompat.ListMenuView = 0x7f11032b
com.tytfizikkamp:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f11032a
com.tytfizikkamp:styleable/ScrollingViewBehavior_Layout = 0x7f120075
com.tytfizikkamp:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f110328
com.tytfizikkamp:style/Widget.AppCompat.Light.PopupMenu = 0x7f110327
com.tytfizikkamp:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f110326
com.tytfizikkamp:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f11031d
com.tytfizikkamp:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f11031c
com.tytfizikkamp:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f11031a
com.tytfizikkamp:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f110319
com.tytfizikkamp:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f110317
com.tytfizikkamp:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f110316
com.tytfizikkamp:style/Widget.AppCompat.ImageButton = 0x7f110314
com.tytfizikkamp:style/Widget.AppCompat.EditText = 0x7f110313
com.tytfizikkamp:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f11030f
com.tytfizikkamp:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f110309
com.tytfizikkamp:style/Widget.AppCompat.AutoCompleteTextView = 0x7f110305
com.tytfizikkamp:style/Widget.AppCompat.ActionButton = 0x7f110300
com.tytfizikkamp:style/Widget.AppCompat.ActionBar.TabView = 0x7f1102ff
com.tytfizikkamp:style/Widget.AppCompat.ActionBar.TabText = 0x7f1102fe
com.tytfizikkamp:style/Widget.AppCompat.ActionBar.TabBar = 0x7f1102fd
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f1102f8
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f1102f4
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f1102f3
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f1102f2
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f1102ee
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f1102ed
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f1102eb
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f1102e4
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f1102e2
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f1102e1
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1102de
com.tytfizikkamp:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f110413
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f1102dd
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1102db
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1102d9
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1102d6
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1102d5
com.tytfizikkamp:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1102d2
com.tytfizikkamp:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1102ce
com.tytfizikkamp:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1102cc
com.tytfizikkamp:style/ThemeOverlay.Material3.Snackbar = 0x7f1102ca
com.tytfizikkamp:style/ThemeOverlay.Material3.Search = 0x7f1102c8
com.tytfizikkamp:style/ThemeOverlay.Material3.NavigationView = 0x7f1102c6
com.tytfizikkamp:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1102c5
com.tytfizikkamp:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1102c3
com.tytfizikkamp:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1102c1
com.tytfizikkamp:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1102c0
com.tytfizikkamp:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1102bd
com.tytfizikkamp:style/ThemeOverlay.Material3.Light = 0x7f1102bc
com.tytfizikkamp:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1102bb
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f11043e
com.tytfizikkamp:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f1102b2
com.tytfizikkamp:style/Widget.Material3.Chip.Input.Elevated = 0x7f110385
com.tytfizikkamp:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1102af
com.tytfizikkamp:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f1102ad
com.tytfizikkamp:style/Widget.AppCompat.ButtonBar = 0x7f11030c
com.tytfizikkamp:style/ThemeOverlay.Material3.Dialog = 0x7f1102ac
com.tytfizikkamp:style/ThemeOverlay.Material3.Dark = 0x7f1102a8
com.tytfizikkamp:style/ThemeOverlay.Material3.Chip.Assist = 0x7f1102a7
com.tytfizikkamp:style/ThemeOverlay.Material3.Button.TextButton = 0x7f1102a3
com.tytfizikkamp:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f1102a2
com.tytfizikkamp:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f1102a1
com.tytfizikkamp:style/ThemeOverlay.Material3.Button.IconButton = 0x7f1102a0
com.tytfizikkamp:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f11029f
com.tytfizikkamp:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f11029c
com.tytfizikkamp:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f110414
com.tytfizikkamp:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f11029b
com.tytfizikkamp:style/ThemeOverlay.Material3 = 0x7f110293
com.tytfizikkamp:style/ThemeOverlay.AppCompat.Light = 0x7f110291
com.tytfizikkamp:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f110295
com.tytfizikkamp:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f110290
com.tytfizikkamp:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f11028e
com.tytfizikkamp:style/ThemeOverlay.AppCompat.DayNight = 0x7f11028d
com.tytfizikkamp:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f11028c
com.tytfizikkamp:style/ThemeOverlay.AppCompat.Dark = 0x7f11028b
com.tytfizikkamp:style/ThemeOverlay.AppCompat.ActionBar = 0x7f11028a
com.tytfizikkamp:styleable/MaterialDivider = 0x7f120056
com.tytfizikkamp:style/Theme.ReactNative.AppCompat.Light.NoActionBar.FullScreen = 0x7f110287
com.tytfizikkamp:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f110285
com.tytfizikkamp:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f110283
com.tytfizikkamp:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f110282
com.tytfizikkamp:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f110281
com.tytfizikkamp:styleable/Fragment = 0x7f120038
com.tytfizikkamp:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f11027f
com.tytfizikkamp:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f11027e
com.tytfizikkamp:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f11027d
com.tytfizikkamp:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f11027c
com.tytfizikkamp:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f11027a
com.tytfizikkamp:style/Theme.MaterialComponents.Light.Dialog = 0x7f110279
com.tytfizikkamp:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f110278
com.tytfizikkamp:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f110277
com.tytfizikkamp:style/Theme.MaterialComponents.Light.Bridge = 0x7f110276
com.tytfizikkamp:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f110275
com.tytfizikkamp:style/Theme.MaterialComponents.Light = 0x7f110274
com.tytfizikkamp:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f110273
com.tytfizikkamp:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f110272
com.tytfizikkamp:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f110271
com.tytfizikkamp:style/Theme.MaterialComponents.Dialog.Alert = 0x7f11026c
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f110266
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f110263
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f110261
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f11025f
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f11025e
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f11025d
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f11025c
com.tytfizikkamp:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f11033e
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight = 0x7f11025b
com.tytfizikkamp:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f1103fa
com.tytfizikkamp:style/Theme.MaterialComponents.Bridge = 0x7f110259
com.tytfizikkamp:style/Theme.MaterialComponents = 0x7f110257
com.tytfizikkamp:style/Theme.Material3.Light.SideSheetDialog = 0x7f110256
com.tytfizikkamp:style/Theme.Material3.Light.DialogWhenLarge = 0x7f110254
com.tytfizikkamp:style/Theme.Material3.DynamicColors.Light.NoActionBar = 0x7f11024e
com.tytfizikkamp:style/Theme.Material3.DynamicColors.Light = 0x7f11024d
com.tytfizikkamp:style/Theme.Material3.DynamicColors.DayNight = 0x7f11024b
com.tytfizikkamp:style/Theme.Material3.DayNight.NoActionBar = 0x7f110247
com.tytfizikkamp:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f110420
com.tytfizikkamp:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f110246
com.tytfizikkamp:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f110245
com.tytfizikkamp:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f110244
com.tytfizikkamp:style/Theme.Material3.DayNight.Dialog = 0x7f110243
com.tytfizikkamp:style/Theme.Material3.DayNight = 0x7f110241
com.tytfizikkamp:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f11023e
com.tytfizikkamp:style/Theme.Material3.Dark.Dialog.Alert = 0x7f11023c
com.tytfizikkamp:style/Theme.Material3.Dark.Dialog = 0x7f11023b
com.tytfizikkamp:style/Theme.FullScreenDialogAnimatedSlide = 0x7f110238
com.tytfizikkamp:style/Theme.FullScreenDialogAnimatedFade = 0x7f110237
com.tytfizikkamp:style/Theme.FullScreenDialog = 0x7f110236
com.tytfizikkamp:style/Theme.Design.Light = 0x7f110232
com.tytfizikkamp:style/Theme.Catalyst.LogBox = 0x7f11022e
com.tytfizikkamp:style/Theme.Catalyst = 0x7f11022d
com.tytfizikkamp:style/Theme.AppCompat.Light.NoActionBar = 0x7f11022a
com.tytfizikkamp:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f110229
com.tytfizikkamp:style/Theme.AppCompat.Light.DarkActionBar = 0x7f110225
com.tytfizikkamp:style/Theme.AppCompat.Light = 0x7f110224
com.tytfizikkamp:style/Theme.AppCompat.DialogWhenLarge = 0x7f110222
com.tytfizikkamp:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f11021e
com.tytfizikkamp:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f11021d
com.tytfizikkamp:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f11021c
com.tytfizikkamp:style/Theme.AppCompat.DayNight = 0x7f110218
com.tytfizikkamp:style/Theme = 0x7f110215
com.tytfizikkamp:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f110214
com.tytfizikkamp:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f110213
com.tytfizikkamp:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f110210
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f11020f
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f11020e
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Overline = 0x7f11020d
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Headline6 = 0x7f11020c
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Headline2 = 0x7f110208
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Caption = 0x7f110205
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Button = 0x7f110204
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Body2 = 0x7f110203
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Badge = 0x7f110201
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Tooltip = 0x7f110211
com.tytfizikkamp:style/TextAppearance.Material3.TitleLarge = 0x7f1101fe
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f110442
com.tytfizikkamp:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f1103a3
com.tytfizikkamp:style/TextAppearance.Material3.SearchView = 0x7f1101fc
com.tytfizikkamp:style/TextAppearance.Material3.LabelMedium = 0x7f1101f8
com.tytfizikkamp:style/TextAppearance.Material3.DisplayMedium = 0x7f1101f2
com.tytfizikkamp:style/TextAppearance.Material3.DisplayLarge = 0x7f1101f1
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f1102dc
com.tytfizikkamp:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f1101ec
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f1101ea
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f1101e6
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f1101e3
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f1101e0
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f1101de
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f1101dd
com.tytfizikkamp:style/TextAppearance.Design.Tab = 0x7f1101dc
com.tytfizikkamp:style/TextAppearance.Design.Prefix = 0x7f1101d9
com.tytfizikkamp:style/TextAppearance.Design.Hint = 0x7f1101d7
com.tytfizikkamp:style/TextAppearance.Design.Counter.Overflow = 0x7f1101d4
com.tytfizikkamp:style/TextAppearance.Compat.Notification.Time = 0x7f1101d0
com.tytfizikkamp:style/TextAppearance.Compat.Notification.Info = 0x7f1101ce
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1101cc
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1101cb
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1101ca
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1101c9
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1101c8
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1101c7
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1101c6
com.tytfizikkamp:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f11026e
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1101c0
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1101be
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1101bd
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1101bb
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1101ba
com.tytfizikkamp:style/TextAppearance.AppCompat.Tooltip = 0x7f1101b9
com.tytfizikkamp:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1101b6
com.tytfizikkamp:style/TextAppearance.AppCompat.Subhead = 0x7f1101b5
com.tytfizikkamp:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1101af
com.tytfizikkamp:style/TextAppearance.AppCompat.Medium = 0x7f1101ae
com.tytfizikkamp:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1101ab
com.tytfizikkamp:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1101a9
com.tytfizikkamp:style/TextAppearance.AppCompat.Large = 0x7f1101a8
com.tytfizikkamp:style/TextAppearance.AppCompat.Inverse = 0x7f1101a7
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1103c2
com.tytfizikkamp:style/TextAppearance.AppCompat.Display4 = 0x7f1101a5
com.tytfizikkamp:style/Widget.Material3.BottomNavigationView = 0x7f110364
com.tytfizikkamp:style/TextAppearance.AppCompat.Body2 = 0x7f11019f
com.tytfizikkamp:style/TextAppearance.AppCompat = 0x7f11019d
com.tytfizikkamp:style/SpinnerDatePickerDialog = 0x7f11019b
com.tytfizikkamp:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f11019a
com.tytfizikkamp:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f110197
com.tytfizikkamp:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f110194
com.tytfizikkamp:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f11018f
com.tytfizikkamp:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f11018d
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1102da
com.tytfizikkamp:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f11018b
com.tytfizikkamp:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f11018a
com.tytfizikkamp:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f110189
com.tytfizikkamp:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f110184
com.tytfizikkamp:style/Widget.MaterialComponents.Button = 0x7f110417
com.tytfizikkamp:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f110312
com.tytfizikkamp:style/ShapeAppearance.Material3.Tooltip = 0x7f110181
com.tytfizikkamp:style/ShapeAppearance.Material3.SmallComponent = 0x7f110180
com.tytfizikkamp:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f110253
com.tytfizikkamp:style/ShapeAppearance.Material3.MediumComponent = 0x7f11017e
com.tytfizikkamp:style/ShapeAppearance.Material3.LargeComponent = 0x7f11017d
com.tytfizikkamp:style/ShapeAppearance.Material3.Corner.Small = 0x7f11017c
com.tytfizikkamp:style/ShapeAppearance.Material3.Corner.None = 0x7f11017b
com.tytfizikkamp:style/ShapeAppearance.Material3.Corner.Full = 0x7f110178
com.tytfizikkamp:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f110177
com.tytfizikkamp:styleable/TabItem = 0x7f120087
com.tytfizikkamp:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f110176
com.tytfizikkamp:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f1103ae
com.tytfizikkamp:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f110173
com.tytfizikkamp:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f110171
com.tytfizikkamp:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f110170
com.tytfizikkamp:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f11016f
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f11016d
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f11016c
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f11016b
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f11016a
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f110169
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f110166
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f110164
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f110161
com.tytfizikkamp:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f110258
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f11015e
com.tytfizikkamp:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f11015b
com.tytfizikkamp:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f110159
com.tytfizikkamp:style/Widget.Material3.ChipGroup = 0x7f11038a
com.tytfizikkamp:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f110158
com.tytfizikkamp:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f110157
com.tytfizikkamp:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f110156
com.tytfizikkamp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f110154
com.tytfizikkamp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f110152
com.tytfizikkamp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f110150
com.tytfizikkamp:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f11014e
com.tytfizikkamp:style/Widget.Material3.MaterialTimePicker = 0x7f1103cc
com.tytfizikkamp:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f110146
com.tytfizikkamp:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f110185
com.tytfizikkamp:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f110145
com.tytfizikkamp:style/Platform.MaterialComponents.Dialog = 0x7f110141
com.tytfizikkamp:style/Platform.AppCompat = 0x7f11013e
com.tytfizikkamp:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f11013c
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f1102f9
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f1101e4
com.tytfizikkamp:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f11013b
com.tytfizikkamp:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f1103e7
com.tytfizikkamp:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f110137
com.tytfizikkamp:style/MaterialAlertDialog.MaterialComponents = 0x7f110133
com.tytfizikkamp:style/ThemeOverlay.Material3.ActionBar = 0x7f110294
com.tytfizikkamp:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f11012d
com.tytfizikkamp:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f11012c
com.tytfizikkamp:style/MaterialAlertDialog.Material3.Body.Text = 0x7f11012b
com.tytfizikkamp:style/MaterialAlertDialog.Material3.Animation = 0x7f11012a
com.tytfizikkamp:style/MaterialAlertDialog.Material3 = 0x7f110129
com.tytfizikkamp:style/CardView.Light = 0x7f110126
com.tytfizikkamp:style/CardView.Dark = 0x7f110125
com.tytfizikkamp:style/CalendarDatePickerDialog = 0x7f110122
com.tytfizikkamp:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f11011f
com.tytfizikkamp:style/Base.Widget.MaterialComponents.Slider = 0x7f11011d
com.tytfizikkamp:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f110119
com.tytfizikkamp:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f110118
com.tytfizikkamp:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f110115
com.tytfizikkamp:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f110114
com.tytfizikkamp:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f110113
com.tytfizikkamp:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f110112
com.tytfizikkamp:style/Base.Widget.Material3.TabLayout = 0x7f110111
com.tytfizikkamp:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f11010f
com.tytfizikkamp:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f11010d
com.tytfizikkamp:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f11010c
com.tytfizikkamp:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f11010a
com.tytfizikkamp:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f110108
com.tytfizikkamp:style/Base.Widget.Material3.CollapsingToolbar = 0x7f110105
com.tytfizikkamp:style/Base.Widget.Material3.Chip = 0x7f110104
com.tytfizikkamp:style/Base.Widget.Material3.BottomNavigationView = 0x7f110102
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1101c5
com.tytfizikkamp:style/Base.Widget.AppCompat.Toolbar = 0x7f1100fd
com.tytfizikkamp:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1100fc
com.tytfizikkamp:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1100fa
com.tytfizikkamp:style/Base.Widget.AppCompat.SeekBar = 0x7f1100f7
com.tytfizikkamp:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1100f6
com.tytfizikkamp:style/Base.Widget.AppCompat.SearchView = 0x7f1100f5
com.tytfizikkamp:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1100f4
com.tytfizikkamp:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1100f3
com.tytfizikkamp:style/Base.Widget.AppCompat.ProgressBar = 0x7f1100f0
com.tytfizikkamp:style/Base.Widget.AppCompat.PopupWindow = 0x7f1100ef
com.tytfizikkamp:style/Base.Widget.AppCompat.PopupMenu = 0x7f1100ed
com.tytfizikkamp:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1100ec
com.tytfizikkamp:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1102b3
com.tytfizikkamp:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1100e9
com.tytfizikkamp:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1100e7
com.tytfizikkamp:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1100e6
com.tytfizikkamp:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1100e3
com.tytfizikkamp:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1100e2
com.tytfizikkamp:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1100e0
com.tytfizikkamp:style/Base.Widget.AppCompat.ImageButton = 0x7f1100df
com.tytfizikkamp:style/Base.Widget.AppCompat.EditText = 0x7f1100de
com.tytfizikkamp:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1100dc
com.tytfizikkamp:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1100db
com.tytfizikkamp:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1100d9
com.tytfizikkamp:style/Base.Widget.AppCompat.ButtonBar = 0x7f1100d6
com.tytfizikkamp:style/Base.Widget.AppCompat.Button.Small = 0x7f1100d5
com.tytfizikkamp:style/Base.Widget.AppCompat.Button = 0x7f1100d0
com.tytfizikkamp:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1100cc
com.tytfizikkamp:styleable/Chip = 0x7f12001e
com.tytfizikkamp:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1100cb
com.tytfizikkamp:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1100c8
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1102d7
com.tytfizikkamp:style/Base.Widget.AppCompat.ActionBar = 0x7f1100c5
com.tytfizikkamp:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1100c4
com.tytfizikkamp:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1100c2
com.tytfizikkamp:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1100c7
com.tytfizikkamp:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1100c1
com.tytfizikkamp:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1100c0
com.tytfizikkamp:style/Base.V7.Theme.AppCompat.Light = 0x7f1100bf
com.tytfizikkamp:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1100be
com.tytfizikkamp:style/Base.V7.Theme.AppCompat = 0x7f1100bd
com.tytfizikkamp:style/Widget.Material3.ActionMode = 0x7f110358
com.tytfizikkamp:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1100ba
com.tytfizikkamp:style/Base.V23.Theme.AppCompat.Light = 0x7f1100b3
com.tytfizikkamp:style/Base.V22.Theme.AppCompat = 0x7f1100b0
com.tytfizikkamp:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f1103b2
com.tytfizikkamp:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1100af
com.tytfizikkamp:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1100ac
com.tytfizikkamp:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1100ab
com.tytfizikkamp:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1100a9
com.tytfizikkamp:style/Base.V21.Theme.AppCompat.Light = 0x7f1100a6
com.tytfizikkamp:style/Base.V21.Theme.AppCompat = 0x7f1100a4
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f11015d
com.tytfizikkamp:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1100a3
com.tytfizikkamp:styleable/AppCompatTheme = 0x7f120012
com.tytfizikkamp:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1100a1
com.tytfizikkamp:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f1100a0
com.tytfizikkamp:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f11009d
com.tytfizikkamp:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f11009c
com.tytfizikkamp:style/Widget.AppCompat.Light.ActionBar = 0x7f110315
com.tytfizikkamp:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f110097
com.tytfizikkamp:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f110095
com.tytfizikkamp:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f11041d
com.tytfizikkamp:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f110093
com.tytfizikkamp:style/Base.V14.Theme.Material3.Light = 0x7f110090
com.tytfizikkamp:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f11008d
com.tytfizikkamp:style/Base.V14.Theme.Material3.Dark = 0x7f11008c
com.tytfizikkamp:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f11008b
com.tytfizikkamp:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f11008a
com.tytfizikkamp:style/Widget.AppCompat.Light.SearchView = 0x7f110329
com.tytfizikkamp:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f110088
com.tytfizikkamp:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f110086
com.tytfizikkamp:style/Base.ThemeOverlay.Material3.Dialog = 0x7f110084
com.tytfizikkamp:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f110083
com.tytfizikkamp:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f110082
com.tytfizikkamp:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f110080
com.tytfizikkamp:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f11017f
com.tytfizikkamp:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f11007c
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f11007a
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f110076
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f110075
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Light = 0x7f110071
com.tytfizikkamp:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f110070
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f11006c
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Dialog = 0x7f11006b
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Bridge = 0x7f110069
com.tytfizikkamp:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f110067
com.tytfizikkamp:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f110066
com.tytfizikkamp:style/Base.Theme.Material3.Light.Dialog = 0x7f110064
com.tytfizikkamp:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f110065
com.tytfizikkamp:style/Base.Theme.Material3.Light = 0x7f110062
com.tytfizikkamp:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f110061
com.tytfizikkamp:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f110060
com.tytfizikkamp:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f11005d
com.tytfizikkamp:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f11041e
com.tytfizikkamp:style/Base.Theme.Material3.Dark = 0x7f11005c
com.tytfizikkamp:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f11005b
com.tytfizikkamp:styleable/View = 0x7f120092
com.tytfizikkamp:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f110059
com.tytfizikkamp:style/Base.Theme.AppCompat.Light.Dialog = 0x7f110057
com.tytfizikkamp:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f110056
com.tytfizikkamp:style/Base.Theme.AppCompat.Light = 0x7f110055
com.tytfizikkamp:style/Base.Theme.AppCompat = 0x7f11004e
com.tytfizikkamp:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f11004b
com.tytfizikkamp:style/Base.TextAppearance.Material3.Search = 0x7f110046
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f110045
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f110043
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f110042
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f11003f
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f11003e
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f110039
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f110033
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Title = 0x7f110032
com.tytfizikkamp:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f1103a9
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f110031
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Subhead = 0x7f110030
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f11002f
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Small = 0x7f11002e
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f11002c
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f110027
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f110026
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f110025
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Large = 0x7f110024
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Inverse = 0x7f110023
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Display3 = 0x7f110020
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Caption = 0x7f11001d
com.tytfizikkamp:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f110054
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Body2 = 0x7f11001b
com.tytfizikkamp:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f110018
com.tytfizikkamp:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f110017
com.tytfizikkamp:style/Base.DialogWindowTitle.AppCompat = 0x7f110014
com.tytfizikkamp:style/Base.CardView = 0x7f110013
com.tytfizikkamp:styleable/FontFamily = 0x7f120035
com.tytfizikkamp:style/Base.Animation.AppCompat.DropDownUp = 0x7f110011
com.tytfizikkamp:style/Base.Animation.AppCompat.Dialog = 0x7f110010
com.tytfizikkamp:style/Base.AlertDialog.AppCompat.Light = 0x7f11000f
com.tytfizikkamp:style/Base.AlertDialog.AppCompat = 0x7f11000e
com.tytfizikkamp:style/AppTheme = 0x7f11000d
com.tytfizikkamp:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f11000c
com.tytfizikkamp:style/Animation.Material3.BottomSheetDialog = 0x7f110008
com.tytfizikkamp:style/Animation.Catalyst.RedBox = 0x7f110006
com.tytfizikkamp:style/Animation.Catalyst.LogBox = 0x7f110005
com.tytfizikkamp:style/Animation.AppCompat.Tooltip = 0x7f110004
com.tytfizikkamp:style/Animation.AppCompat.Dialog = 0x7f110002
com.tytfizikkamp:style/AlertDialog.AppCompat.Light = 0x7f110001
com.tytfizikkamp:style/AlertDialog.AppCompat = 0x7f110000
com.tytfizikkamp:string/toolbar_description = 0x7f1000df
com.tytfizikkamp:string/state_unselected_description = 0x7f1000da
com.tytfizikkamp:string/state_on_description = 0x7f1000d9
com.tytfizikkamp:string/state_expanded_description = 0x7f1000d6
com.tytfizikkamp:string/state_collapsed_description = 0x7f1000d5
com.tytfizikkamp:string/searchview_navigation_content_description = 0x7f1000d0
com.tytfizikkamp:string/searchview_clear_text_content_description = 0x7f1000cf
com.tytfizikkamp:string/react_native_dev_server_ip = 0x7f1000ca
com.tytfizikkamp:string/radiogroup_description = 0x7f1000c9
com.tytfizikkamp:style/Widget.Compat.NotificationActionContainer = 0x7f11034a
com.tytfizikkamp:string/progressbar_description = 0x7f1000c8
com.tytfizikkamp:string/path_password_eye = 0x7f1000c4
com.tytfizikkamp:string/mtrl_switch_track_decoration_path = 0x7f1000bf
com.tytfizikkamp:string/mtrl_switch_thumb_path_name = 0x7f1000bc
com.tytfizikkamp:string/mtrl_switch_thumb_path_morphing = 0x7f1000bb
com.tytfizikkamp:string/mtrl_switch_thumb_path_checked = 0x7f1000ba
com.tytfizikkamp:string/mtrl_picker_toggle_to_year_selection = 0x7f1000b8
com.tytfizikkamp:style/Base.Widget.Material3.Snackbar = 0x7f110110
com.tytfizikkamp:string/mtrl_picker_toggle_to_text_input_mode = 0x7f1000b7
com.tytfizikkamp:string/mtrl_picker_toggle_to_day_selection = 0x7f1000b6
com.tytfizikkamp:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f1000b5
com.tytfizikkamp:style/TextAppearance.Material3.ActionBar.Title = 0x7f1101ed
com.tytfizikkamp:string/mtrl_picker_text_input_month_abbr = 0x7f1000b2
com.tytfizikkamp:string/mtrl_picker_text_input_day_abbr = 0x7f1000b1
com.tytfizikkamp:string/mtrl_picker_text_input_date_hint = 0x7f1000ae
com.tytfizikkamp:string/mtrl_picker_start_date_description = 0x7f1000ad
com.tytfizikkamp:style/Widget.Material3.CompoundButton.Switch = 0x7f110399
com.tytfizikkamp:string/mtrl_picker_save = 0x7f1000ac
com.tytfizikkamp:string/mtrl_picker_range_header_unselected = 0x7f1000ab
com.tytfizikkamp:string/mtrl_picker_range_header_selected = 0x7f1000a9
com.tytfizikkamp:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f110466
com.tytfizikkamp:string/mtrl_picker_range_header_only_start_selected = 0x7f1000a8
com.tytfizikkamp:string/mtrl_picker_navigate_to_year_description = 0x7f1000a5
com.tytfizikkamp:string/mtrl_picker_navigate_to_current_year_description = 0x7f1000a4
com.tytfizikkamp:string/mtrl_picker_invalid_range = 0x7f1000a3
com.tytfizikkamp:style/Widget.Material3.Snackbar.TextView = 0x7f1103ee
com.tytfizikkamp:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f110376
com.tytfizikkamp:string/mtrl_picker_invalid_format_use = 0x7f1000a2
com.tytfizikkamp:styleable/ConstraintLayout_Layout = 0x7f120028
com.tytfizikkamp:string/mtrl_picker_invalid_format_example = 0x7f1000a1
com.tytfizikkamp:string/mtrl_picker_invalid_format = 0x7f1000a0
com.tytfizikkamp:string/mtrl_picker_end_date_description = 0x7f10009f
com.tytfizikkamp:string/mtrl_picker_day_of_week_column_header = 0x7f10009e
com.tytfizikkamp:string/mtrl_picker_date_header_unselected = 0x7f10009d
com.tytfizikkamp:style/Widget.AppCompat.ListView.DropDown = 0x7f11032e
com.tytfizikkamp:string/mtrl_picker_date_header_selected = 0x7f10009b
com.tytfizikkamp:string/mtrl_picker_cancel = 0x7f100099
com.tytfizikkamp:string/mtrl_picker_announce_current_selection_none = 0x7f100098
com.tytfizikkamp:string/mtrl_picker_a11y_prev_month = 0x7f100095
com.tytfizikkamp:string/mtrl_checkbox_state_description_unchecked = 0x7f100090
com.tytfizikkamp:string/mtrl_checkbox_state_description_checked = 0x7f10008e
com.tytfizikkamp:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f100088
com.tytfizikkamp:string/mtrl_checkbox_button_icon_path_group_name = 0x7f100087
com.tytfizikkamp:string/mtrl_checkbox_button_icon_path_checked = 0x7f100086
com.tytfizikkamp:string/menubar_description = 0x7f100083
com.tytfizikkamp:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1101b8
com.tytfizikkamp:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f110049
com.tytfizikkamp:string/menu_description = 0x7f100082
com.tytfizikkamp:string/material_timepicker_select_time = 0x7f100080
com.tytfizikkamp:string/material_timepicker_minute = 0x7f10007e
com.tytfizikkamp:string/material_timepicker_clock_mode_description = 0x7f10007c
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1101bf
com.tytfizikkamp:string/material_timepicker_am = 0x7f10007b
com.tytfizikkamp:string/material_slider_value = 0x7f10007a
com.tytfizikkamp:string/material_motion_easing_accelerated = 0x7f100073
com.tytfizikkamp:string/material_minute_suffix = 0x7f100072
com.tytfizikkamp:string/material_hour_suffix = 0x7f100070
com.tytfizikkamp:string/material_hour_selection = 0x7f10006f
com.tytfizikkamp:style/TextAppearance.AppCompat.Headline = 0x7f1101a6
com.tytfizikkamp:string/material_hour_24h_suffix = 0x7f10006e
com.tytfizikkamp:style/Base.Widget.Design.TabLayout = 0x7f1100ff
com.tytfizikkamp:string/m3_sys_motion_easing_standard_decelerate = 0x7f10006b
com.tytfizikkamp:string/m3_sys_motion_easing_linear = 0x7f100068
com.tytfizikkamp:string/m3_sys_motion_easing_legacy_accelerate = 0x7f100066
com.tytfizikkamp:string/m3_sys_motion_easing_legacy = 0x7f100065
com.tytfizikkamp:string/m3_sys_motion_easing_emphasized_path_data = 0x7f100064
com.tytfizikkamp:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f100063
com.tytfizikkamp:string/m3_sys_motion_easing_emphasized = 0x7f100061
com.tytfizikkamp:string/m3_ref_typeface_plain_regular = 0x7f100060
com.tytfizikkamp:styleable/BaseProgressIndicator = 0x7f120015
com.tytfizikkamp:string/m3_ref_typeface_brand_medium = 0x7f10005d
com.tytfizikkamp:style/Base.V7.Widget.AppCompat.EditText = 0x7f1100c3
com.tytfizikkamp:string/m3_exceed_max_badge_text_suffix = 0x7f10005c
com.tytfizikkamp:string/imagebutton_description = 0x7f100059
com.tytfizikkamp:string/icon_content_description = 0x7f100057
com.tytfizikkamp:string/header_description = 0x7f100055
com.tytfizikkamp:string/fab_transformation_sheet_behavior = 0x7f100054
com.tytfizikkamp:string/fab_transformation_scrim_behavior = 0x7f100053
com.tytfizikkamp:string/error_a11y_label = 0x7f100050
com.tytfizikkamp:string/clear_text_end_icon_content_description = 0x7f10004e
com.tytfizikkamp:string/character_counter_pattern = 0x7f10004d
com.tytfizikkamp:string/character_counter_content_description = 0x7f10004b
com.tytfizikkamp:string/catalyst_settings_title = 0x7f10004a
com.tytfizikkamp:string/catalyst_settings = 0x7f100049
com.tytfizikkamp:string/catalyst_sample_profiler_toggle = 0x7f100048
com.tytfizikkamp:string/catalyst_report_button = 0x7f100047
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f1102f5
com.tytfizikkamp:string/m3_ref_typeface_brand_regular = 0x7f10005e
com.tytfizikkamp:string/catalyst_reload_error = 0x7f100046
com.tytfizikkamp:string/catalyst_reload_button = 0x7f100045
com.tytfizikkamp:string/catalyst_perf_monitor_stop = 0x7f100043
com.tytfizikkamp:style/Widget.Material3.FloatingActionButton.Surface = 0x7f1103ad
com.tytfizikkamp:string/catalyst_inspector_toggle = 0x7f10003f
com.tytfizikkamp:string/catalyst_hot_reloading_auto_enable = 0x7f10003d
com.tytfizikkamp:string/catalyst_hot_reloading_auto_disable = 0x7f10003c
com.tytfizikkamp:string/mtrl_checkbox_button_path_name = 0x7f10008c
com.tytfizikkamp:string/catalyst_dev_menu_sub_header = 0x7f100038
com.tytfizikkamp:string/catalyst_debug_open = 0x7f100035
com.tytfizikkamp:string/catalyst_debug_error = 0x7f100034
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f110269
com.tytfizikkamp:string/catalyst_change_bundle_location_instructions = 0x7f100031
com.tytfizikkamp:string/catalyst_change_bundle_location_input_label = 0x7f100030
com.tytfizikkamp:string/catalyst_change_bundle_location_apply = 0x7f10002d
com.tytfizikkamp:string/catalyst_change_bundle_location = 0x7f10002c
com.tytfizikkamp:string/call_notification_screening_text = 0x7f10002b
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f11043d
com.tytfizikkamp:string/call_notification_hang_up_action = 0x7f100028
com.tytfizikkamp:string/call_notification_answer_video_action = 0x7f100026
com.tytfizikkamp:style/Widget.AppCompat.Spinner.Underlined = 0x7f11033f
com.tytfizikkamp:string/call_notification_answer_action = 0x7f100025
com.tytfizikkamp:style/DialogAnimationSlide = 0x7f110128
com.tytfizikkamp:string/bottom_sheet_behavior = 0x7f10001f
com.tytfizikkamp:string/appbar_scrolling_view_behavior = 0x7f10001e
com.tytfizikkamp:string/alert_description = 0x7f10001b
com.tytfizikkamp:string/abc_searchview_description_submit = 0x7f100016
com.tytfizikkamp:string/abc_searchview_description_query = 0x7f100014
com.tytfizikkamp:string/abc_searchview_description_clear = 0x7f100013
com.tytfizikkamp:string/abc_menu_sym_shortcut_label = 0x7f100010
com.tytfizikkamp:string/abc_menu_shift_shortcut_label = 0x7f10000e
com.tytfizikkamp:string/catalyst_copy_button = 0x7f100032
com.tytfizikkamp:string/abc_menu_meta_shortcut_label = 0x7f10000d
com.tytfizikkamp:string/abc_menu_function_shortcut_label = 0x7f10000c
com.tytfizikkamp:string/abc_menu_enter_shortcut_label = 0x7f10000b
com.tytfizikkamp:string/abc_menu_ctrl_shortcut_label = 0x7f100009
com.tytfizikkamp:string/abc_capital_on = 0x7f100007
com.tytfizikkamp:string/abc_activitychooserview_choose_application = 0x7f100005
com.tytfizikkamp:styleable/TabLayout = 0x7f120088
com.tytfizikkamp:string/abc_activity_chooser_view_see_all = 0x7f100004
com.tytfizikkamp:string/abc_action_mode_done = 0x7f100003
com.tytfizikkamp:string/abc_action_menu_overflow_description = 0x7f100002
com.tytfizikkamp:plurals/mtrl_badge_content_description = 0x7f0e0000
com.tytfizikkamp:mipmap/ic_launcher = 0x7f0d0000
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Headline4 = 0x7f11020a
com.tytfizikkamp:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0c0174
com.tytfizikkamp:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0c0172
com.tytfizikkamp:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0c0171
com.tytfizikkamp:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0c0170
com.tytfizikkamp:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0c016b
com.tytfizikkamp:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0c016a
com.tytfizikkamp:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0c0168
com.tytfizikkamp:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0c0167
com.tytfizikkamp:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0c0166
com.tytfizikkamp:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0c0165
com.tytfizikkamp:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0c0163
com.tytfizikkamp:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0c0162
com.tytfizikkamp:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0c0161
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f110262
com.tytfizikkamp:string/call_notification_ongoing_text = 0x7f10002a
com.tytfizikkamp:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0c015e
com.tytfizikkamp:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0c015b
com.tytfizikkamp:styleable/ViewPager2 = 0x7f120094
com.tytfizikkamp:string/path_password_eye_mask_visible = 0x7f1000c6
com.tytfizikkamp:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0c015a
com.tytfizikkamp:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0c0158
com.tytfizikkamp:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0c0155
com.tytfizikkamp:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0c0154
com.tytfizikkamp:macro/m3_comp_time_picker_headline_color = 0x7f0c0150
com.tytfizikkamp:string/catalyst_change_bundle_location_input_hint = 0x7f10002f
com.tytfizikkamp:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0c0146
com.tytfizikkamp:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0c0143
com.tytfizikkamp:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0c0141
com.tytfizikkamp:style/TextAppearance.AppCompat.Caption = 0x7f1101a1
com.tytfizikkamp:macro/m3_comp_switch_unselected_track_color = 0x7f0c0140
com.tytfizikkamp:string/password_toggle_content_description = 0x7f1000c3
com.tytfizikkamp:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0c013d
com.tytfizikkamp:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0c013c
com.tytfizikkamp:macro/m3_comp_switch_unselected_icon_color = 0x7f0c013a
com.tytfizikkamp:style/Theme.Material3.DynamicColors.Dark.NoActionBar = 0x7f11024a
com.tytfizikkamp:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0c0139
com.tytfizikkamp:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0c0138
com.tytfizikkamp:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0c0137
com.tytfizikkamp:style/TextAppearance.AppCompat.Display2 = 0x7f1101a3
com.tytfizikkamp:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0c0136
com.tytfizikkamp:macro/m3_comp_switch_unselected_handle_color = 0x7f0c0134
com.tytfizikkamp:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0c0133
com.tytfizikkamp:string/catalyst_debug_open_disabled = 0x7f100036
com.tytfizikkamp:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0c0132
com.tytfizikkamp:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0c0131
com.tytfizikkamp:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0c012f
com.tytfizikkamp:macro/m3_comp_switch_selected_track_color = 0x7f0c012e
com.tytfizikkamp:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0c012d
com.tytfizikkamp:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1100e5
com.tytfizikkamp:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0c012c
com.tytfizikkamp:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0c012b
com.tytfizikkamp:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0c012a
com.tytfizikkamp:macro/m3_comp_switch_selected_icon_color = 0x7f0c0129
com.tytfizikkamp:macro/m3_comp_switch_selected_hover_track_color = 0x7f0c0128
com.tytfizikkamp:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0c0127
com.tytfizikkamp:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0c0126
com.tytfizikkamp:macro/m3_comp_switch_selected_handle_color = 0x7f0c0124
com.tytfizikkamp:macro/m3_comp_switch_selected_focus_track_color = 0x7f0c0123
com.tytfizikkamp:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0c0121
com.tytfizikkamp:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0c0120
com.tytfizikkamp:string/mtrl_badge_numberless_content_description = 0x7f100085
com.tytfizikkamp:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0c011e
com.tytfizikkamp:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0c011b
com.tytfizikkamp:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0c011a
com.tytfizikkamp:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0c0119
com.tytfizikkamp:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0c0118
com.tytfizikkamp:macro/m3_comp_suggestion_chip_container_shape = 0x7f0c0117
com.tytfizikkamp:macro/m3_comp_snackbar_supporting_text_type = 0x7f0c0116
com.tytfizikkamp:macro/m3_comp_snackbar_supporting_text_color = 0x7f0c0115
com.tytfizikkamp:styleable/ActionMode = 0x7f120004
com.tytfizikkamp:macro/m3_comp_snackbar_container_shape = 0x7f0c0114
com.tytfizikkamp:macro/m3_comp_snackbar_container_color = 0x7f0c0113
com.tytfizikkamp:macro/m3_comp_slider_label_container_color = 0x7f0c0111
com.tytfizikkamp:macro/m3_comp_slider_inactive_track_color = 0x7f0c0110
com.tytfizikkamp:macro/m3_comp_slider_handle_color = 0x7f0c010f
com.tytfizikkamp:macro/m3_comp_slider_disabled_handle_color = 0x7f0c010d
com.tytfizikkamp:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f110395
com.tytfizikkamp:macro/m3_comp_sheet_side_docked_standard_container_color = 0x7f0c010a
com.tytfizikkamp:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0c0109
com.tytfizikkamp:macro/m3_comp_sheet_side_docked_modal_container_color = 0x7f0c0108
com.tytfizikkamp:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c0103
com.tytfizikkamp:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0c0101
com.tytfizikkamp:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0c0100
com.tytfizikkamp:style/Widget.Material3.SearchBar.Outlined = 0x7f1103e0
com.tytfizikkamp:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0c00ff
com.tytfizikkamp:style/Base.Widget.AppCompat.TextView = 0x7f1100fb
com.tytfizikkamp:string/catalyst_debug_connecting = 0x7f100033
com.tytfizikkamp:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0c00fd
com.tytfizikkamp:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0c00fc
com.tytfizikkamp:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0c00fb
com.tytfizikkamp:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0c00fa
com.tytfizikkamp:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0c00f9
com.tytfizikkamp:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0c00f8
com.tytfizikkamp:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0c00f7
com.tytfizikkamp:macro/m3_comp_search_view_header_input_text_color = 0x7f0c00f4
com.tytfizikkamp:macro/m3_comp_search_view_divider_color = 0x7f0c00f2
com.tytfizikkamp:macro/m3_comp_search_view_container_color = 0x7f0c00f1
com.tytfizikkamp:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0c00f0
com.tytfizikkamp:macro/m3_comp_search_bar_supporting_text_type = 0x7f0c00ef
com.tytfizikkamp:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0c00ec
com.tytfizikkamp:macro/m3_comp_search_bar_leading_icon_color = 0x7f0c00eb
com.tytfizikkamp:macro/m3_comp_search_bar_input_text_type = 0x7f0c00ea
com.tytfizikkamp:string/exposed_dropdown_menu_content_description = 0x7f100052
com.tytfizikkamp:macro/m3_comp_search_bar_input_text_color = 0x7f0c00e9
com.tytfizikkamp:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0c00e7
com.tytfizikkamp:macro/m3_comp_search_bar_container_color = 0x7f0c00e6
com.tytfizikkamp:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0c00e5
com.tytfizikkamp:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f110299
com.tytfizikkamp:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0c00e4
com.tytfizikkamp:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f11046c
com.tytfizikkamp:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0c00e3
com.tytfizikkamp:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0c00e1
com.tytfizikkamp:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0c00df
com.tytfizikkamp:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0c00de
com.tytfizikkamp:macro/m3_sys_color_light_surface_tint = 0x7f0c0176
com.tytfizikkamp:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0c00dd
com.tytfizikkamp:string/m3_sys_motion_easing_standard_accelerate = 0x7f10006a
com.tytfizikkamp:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0c00d9
com.tytfizikkamp:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0c00d7
com.tytfizikkamp:string/combobox_description = 0x7f10004f
com.tytfizikkamp:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0c00d6
com.tytfizikkamp:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f110352
com.tytfizikkamp:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0c00d3
com.tytfizikkamp:styleable/CheckedTextView = 0x7f12001d
com.tytfizikkamp:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c00d0
com.tytfizikkamp:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0c00cb
com.tytfizikkamp:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0c00c9
com.tytfizikkamp:styleable/FragmentContainerView = 0x7f120039
com.tytfizikkamp:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0c00c7
com.tytfizikkamp:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f11039e
com.tytfizikkamp:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0c00c6
com.tytfizikkamp:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0c00c5
com.tytfizikkamp:macro/m3_comp_outlined_text_field_outline_color = 0x7f0c00c3
com.tytfizikkamp:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0c00bf
com.tytfizikkamp:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f110422
com.tytfizikkamp:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0c00bb
com.tytfizikkamp:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0c00ba
com.tytfizikkamp:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0c00b9
com.tytfizikkamp:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0c00f6
com.tytfizikkamp:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0c00b4
com.tytfizikkamp:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1103f5
com.tytfizikkamp:macro/m3_comp_outlined_text_field_container_shape = 0x7f0c00b1
com.tytfizikkamp:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f110392
com.tytfizikkamp:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0c00af
com.tytfizikkamp:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1102cf
com.tytfizikkamp:macro/m3_comp_outlined_card_outline_color = 0x7f0c00ae
com.tytfizikkamp:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1102b5
com.tytfizikkamp:macro/m3_comp_outlined_card_container_shape = 0x7f0c00a9
com.tytfizikkamp:macro/m3_comp_outlined_button_outline_color = 0x7f0c00a6
com.tytfizikkamp:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0c00a5
com.tytfizikkamp:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0c00a4
com.tytfizikkamp:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0c00a3
com.tytfizikkamp:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0c00a2
com.tytfizikkamp:macro/m3_comp_outlined_autocomplete_menu_container_color = 0x7f0c00a0
com.tytfizikkamp:macro/m3_comp_navigation_rail_label_text_type = 0x7f0c009f
com.tytfizikkamp:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0c009e
com.tytfizikkamp:style/TextAppearance.AppCompat.Small = 0x7f1101b3
com.tytfizikkamp:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0c009c
com.tytfizikkamp:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0c009b
com.tytfizikkamp:macro/m3_comp_navigation_rail_container_color = 0x7f0c0099
com.tytfizikkamp:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0c0096
com.tytfizikkamp:style/Widget.Material3.CircularProgressIndicator.Legacy = 0x7f11038d
com.tytfizikkamp:style/Theme.Design.BottomSheetDialog = 0x7f110231
com.tytfizikkamp:string/androidx_startup = 0x7f10001c
com.tytfizikkamp:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0c0093
com.tytfizikkamp:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0c0090
com.tytfizikkamp:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0c008f
com.tytfizikkamp:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0c008c
com.tytfizikkamp:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0c008b
com.tytfizikkamp:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0c0089
com.tytfizikkamp:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0c0088
com.tytfizikkamp:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0c0086
com.tytfizikkamp:macro/m3_comp_navigation_drawer_headline_type = 0x7f0c0085
com.tytfizikkamp:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0c0083
com.tytfizikkamp:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0c0081
com.tytfizikkamp:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0c0080
com.tytfizikkamp:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0c007f
com.tytfizikkamp:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0c007d
com.tytfizikkamp:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0c007c
com.tytfizikkamp:style/Widget.AppCompat.Button.Borderless = 0x7f110307
com.tytfizikkamp:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0c007b
com.tytfizikkamp:string/bottomsheet_action_collapse = 0x7f100020
com.tytfizikkamp:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0c0079
com.tytfizikkamp:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0c0076
com.tytfizikkamp:style/Widget.Material3.CardView.Filled = 0x7f11037d
com.tytfizikkamp:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0c0075
com.tytfizikkamp:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0c0073
com.tytfizikkamp:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f110301
com.tytfizikkamp:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0c0072
com.tytfizikkamp:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0c0071
com.tytfizikkamp:style/Widget.Material3.SearchView = 0x7f1103e1
com.tytfizikkamp:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0c006e
com.tytfizikkamp:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0c006d
com.tytfizikkamp:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0c006c
com.tytfizikkamp:macro/m3_comp_navigation_bar_container_color = 0x7f0c006b
com.tytfizikkamp:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f110192
com.tytfizikkamp:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0c006a
com.tytfizikkamp:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0c0067
com.tytfizikkamp:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0c0066
com.tytfizikkamp:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0c0065
com.tytfizikkamp:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0c0064
com.tytfizikkamp:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0c005f
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.Day = 0x7f1103b4
com.tytfizikkamp:macro/m3_comp_menu_list_item_selected_container_color = 0x7f0c005e
com.tytfizikkamp:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0c005a
com.tytfizikkamp:macro/m3_comp_icon_button_selected_icon_color = 0x7f0c0059
com.tytfizikkamp:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0c0056
com.tytfizikkamp:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0c0055
com.tytfizikkamp:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0c0160
com.tytfizikkamp:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0c0054
com.tytfizikkamp:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0c0053
com.tytfizikkamp:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f110186
com.tytfizikkamp:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0c004f
com.tytfizikkamp:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0c004e
com.tytfizikkamp:string/hide_bottom_view_on_scroll_behavior = 0x7f100056
com.tytfizikkamp:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0c004d
com.tytfizikkamp:macro/m3_comp_filled_text_field_container_shape = 0x7f0c004c
com.tytfizikkamp:style/NoAnimationDialog = 0x7f11013d
com.tytfizikkamp:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0c0049
com.tytfizikkamp:string/mtrl_picker_today_description = 0x7f1000b4
com.tytfizikkamp:macro/m3_comp_filled_card_container_shape = 0x7f0c0047
com.tytfizikkamp:macro/m3_comp_filled_card_container_color = 0x7f0c0046
com.tytfizikkamp:macro/m3_comp_filled_button_label_text_color = 0x7f0c0044
com.tytfizikkamp:string/mtrl_chip_close_icon_content_description = 0x7f100091
com.tytfizikkamp:macro/m3_comp_filled_autocomplete_menu_container_color = 0x7f0c0041
com.tytfizikkamp:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f110198
com.tytfizikkamp:macro/m3_comp_fab_tertiary_icon_color = 0x7f0c0040
com.tytfizikkamp:macro/m3_comp_fab_tertiary_container_color = 0x7f0c003f
com.tytfizikkamp:macro/m3_comp_fab_surface_icon_color = 0x7f0c003e
com.tytfizikkamp:macro/m3_comp_fab_secondary_icon_color = 0x7f0c003c
com.tytfizikkamp:macro/m3_comp_fab_secondary_container_color = 0x7f0c003b
com.tytfizikkamp:macro/m3_comp_fab_primary_small_container_shape = 0x7f0c003a
com.tytfizikkamp:macro/m3_comp_fab_primary_large_container_shape = 0x7f0c0039
com.tytfizikkamp:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f110323
com.tytfizikkamp:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0c0030
com.tytfizikkamp:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0c002e
com.tytfizikkamp:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0c002d
com.tytfizikkamp:macro/m3_comp_extended_fab_primary_container_color = 0x7f0c002c
com.tytfizikkamp:macro/m3_comp_elevated_card_container_shape = 0x7f0c002b
com.tytfizikkamp:macro/m3_comp_dialog_supporting_text_color = 0x7f0c0026
com.tytfizikkamp:macro/m3_comp_dialog_headline_color = 0x7f0c0024
com.tytfizikkamp:macro/m3_comp_dialog_container_shape = 0x7f0c0023
com.tytfizikkamp:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0c0021
com.tytfizikkamp:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0c001d
com.tytfizikkamp:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0c001c
com.tytfizikkamp:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f110117
com.tytfizikkamp:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0c001a
com.tytfizikkamp:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0c0019
com.tytfizikkamp:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0c0018
com.tytfizikkamp:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0c0016
com.tytfizikkamp:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0c0015
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1102d4
com.tytfizikkamp:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0c0013
com.tytfizikkamp:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0c0012
com.tytfizikkamp:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0c0011
com.tytfizikkamp:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0c000f
com.tytfizikkamp:macro/m3_comp_date_picker_modal_container_shape = 0x7f0c000e
com.tytfizikkamp:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0c000c
com.tytfizikkamp:macro/m3_comp_checkbox_selected_icon_color = 0x7f0c000b
com.tytfizikkamp:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0c000a
com.tytfizikkamp:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0c0009
com.tytfizikkamp:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0c0008
com.tytfizikkamp:style/Platform.MaterialComponents.Light = 0x7f110142
com.tytfizikkamp:macro/m3_comp_checkbox_selected_container_color = 0x7f0c0006
com.tytfizikkamp:style/Base.V22.Theme.AppCompat.Light = 0x7f1100b1
com.tytfizikkamp:macro/m3_comp_bottom_app_bar_container_color = 0x7f0c0005
com.tytfizikkamp:macro/m3_comp_badge_large_label_text_color = 0x7f0c0003
com.tytfizikkamp:macro/m3_comp_badge_color = 0x7f0c0002
com.tytfizikkamp:macro/m3_comp_assist_chip_label_text_type = 0x7f0c0001
com.tytfizikkamp:macro/m3_comp_assist_chip_container_shape = 0x7f0c0000
com.tytfizikkamp:layout/select_dialog_singlechoice_material = 0x7f0b0071
com.tytfizikkamp:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1103cf
com.tytfizikkamp:layout/select_dialog_multichoice_material = 0x7f0b0070
com.tytfizikkamp:layout/redbox_view = 0x7f0b006e
com.tytfizikkamp:layout/redbox_item_title = 0x7f0b006d
com.tytfizikkamp:style/Widget.Material3.CircularProgressIndicator.Legacy.ExtraSmall = 0x7f11038e
com.tytfizikkamp:layout/redbox_item_frame = 0x7f0b006c
com.tytfizikkamp:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f11009e
com.tytfizikkamp:layout/notification_template_part_time = 0x7f0b006a
com.tytfizikkamp:layout/notification_template_icon_group = 0x7f0b0068
com.tytfizikkamp:layout/notification_template_custom_big = 0x7f0b0067
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f110040
com.tytfizikkamp:layout/notification_action = 0x7f0b0065
com.tytfizikkamp:layout/mtrl_search_bar = 0x7f0b0063
com.tytfizikkamp:layout/mtrl_picker_text_input_date_range = 0x7f0b0062
com.tytfizikkamp:layout/mtrl_picker_header_title_text = 0x7f0b005f
com.tytfizikkamp:layout/mtrl_picker_header_selection_text = 0x7f0b005e
com.tytfizikkamp:layout/mtrl_picker_header_fullscreen = 0x7f0b005d
com.tytfizikkamp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f110151
com.tytfizikkamp:layout/mtrl_picker_dialog = 0x7f0b005a
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f1102f0
com.tytfizikkamp:layout/mtrl_picker_actions = 0x7f0b0059
com.tytfizikkamp:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f1103de
com.tytfizikkamp:layout/mtrl_calendar_vertical = 0x7f0b0054
com.tytfizikkamp:layout/mtrl_calendar_months = 0x7f0b0053
com.tytfizikkamp:macro/m3_comp_progress_indicator_active_indicator_color = 0x7f0c00d4
com.tytfizikkamp:layout/mtrl_calendar_horizontal = 0x7f0b004f
com.tytfizikkamp:layout/mtrl_calendar_day_of_week = 0x7f0b004d
com.tytfizikkamp:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0b004a
com.tytfizikkamp:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f110463
com.tytfizikkamp:layout/material_timepicker_dialog = 0x7f0b0043
com.tytfizikkamp:layout/material_time_input = 0x7f0b0041
com.tytfizikkamp:layout/material_time_chip = 0x7f0b0040
com.tytfizikkamp:style/Platform.Widget.AppCompat.Spinner = 0x7f11014b
com.tytfizikkamp:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f11012e
com.tytfizikkamp:layout/material_textinput_timepicker = 0x7f0b003f
com.tytfizikkamp:layout/material_radial_view_group = 0x7f0b003e
com.tytfizikkamp:layout/material_clock_display_divider = 0x7f0b0039
com.tytfizikkamp:layout/m3_side_sheet_dialog = 0x7f0b0036
com.tytfizikkamp:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f110228
com.tytfizikkamp:layout/m3_auto_complete_simple_item = 0x7f0b0035
com.tytfizikkamp:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1101ad
com.tytfizikkamp:layout/m3_alert_dialog = 0x7f0b0032
com.tytfizikkamp:styleable/MaterialTextView = 0x7f12005b
com.tytfizikkamp:style/Widget.Material3.Slider.Label = 0x7f1103e9
com.tytfizikkamp:layout/ime_secondary_split_test_activity = 0x7f0b0031
com.tytfizikkamp:layout/ime_base_split_test_activity = 0x7f0b0030
com.tytfizikkamp:layout/dev_loading_view = 0x7f0b002e
com.tytfizikkamp:layout/design_text_input_end_icon = 0x7f0b002c
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f110446
com.tytfizikkamp:layout/design_navigation_menu = 0x7f0b002a
com.tytfizikkamp:string/catalyst_change_bundle_location_cancel = 0x7f10002e
com.tytfizikkamp:layout/design_navigation_item_header = 0x7f0b0027
com.tytfizikkamp:style/Widget.Material3.TabLayout.Secondary = 0x7f1103f1
com.tytfizikkamp:layout/design_navigation_item = 0x7f0b0026
com.tytfizikkamp:layout/material_clockface_textview = 0x7f0b003c
com.tytfizikkamp:layout/design_layout_tab_icon = 0x7f0b0023
com.tytfizikkamp:macro/m3_comp_slider_active_track_color = 0x7f0c010b
com.tytfizikkamp:layout/design_layout_snackbar_include = 0x7f0b0022
com.tytfizikkamp:layout/design_layout_snackbar = 0x7f0b0021
com.tytfizikkamp:layout/design_bottom_navigation_item = 0x7f0b001f
com.tytfizikkamp:layout/abc_tooltip = 0x7f0b001b
com.tytfizikkamp:layout/abc_select_dialog_material = 0x7f0b001a
com.tytfizikkamp:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
com.tytfizikkamp:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0c0122
com.tytfizikkamp:layout/abc_screen_toolbar = 0x7f0b0017
com.tytfizikkamp:layout/abc_screen_simple = 0x7f0b0015
com.tytfizikkamp:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1102b6
com.tytfizikkamp:layout/abc_popup_menu_item_layout = 0x7f0b0013
com.tytfizikkamp:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
com.tytfizikkamp:layout/abc_list_menu_item_radio = 0x7f0b0011
com.tytfizikkamp:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f110212
com.tytfizikkamp:layout/abc_list_menu_item_layout = 0x7f0b0010
com.tytfizikkamp:layout/abc_expanded_menu_layout = 0x7f0b000d
com.tytfizikkamp:layout/abc_dialog_title_material = 0x7f0b000c
com.tytfizikkamp:layout/abc_cascading_menu_item_layout = 0x7f0b000b
com.tytfizikkamp:layout/abc_activity_chooser_view = 0x7f0b0006
com.tytfizikkamp:layout/abc_action_bar_title_item = 0x7f0b0000
com.tytfizikkamp:interpolator/mtrl_linear = 0x7f0a0010
com.tytfizikkamp:style/Widget.MaterialComponents.CardView = 0x7f110423
com.tytfizikkamp:interpolator/mtrl_fast_out_slow_in = 0x7f0a000f
com.tytfizikkamp:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f11011c
com.tytfizikkamp:interpolator/m3_sys_motion_easing_standard = 0x7f0a000b
com.tytfizikkamp:interpolator/m3_sys_motion_easing_linear = 0x7f0a000a
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f110163
com.tytfizikkamp:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0a0009
com.tytfizikkamp:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0a0008
com.tytfizikkamp:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
com.tytfizikkamp:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
com.tytfizikkamp:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
com.tytfizikkamp:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
com.tytfizikkamp:style/Widget.MaterialComponents.TextView = 0x7f110470
com.tytfizikkamp:integer/status_bar_notification_info_maxnum = 0x7f090044
com.tytfizikkamp:integer/mtrl_view_visible = 0x7f090041
com.tytfizikkamp:integer/mtrl_view_gone = 0x7f09003f
com.tytfizikkamp:integer/mtrl_switch_track_viewport_width = 0x7f09003d
com.tytfizikkamp:integer/mtrl_switch_track_viewport_height = 0x7f09003c
com.tytfizikkamp:integer/mtrl_switch_thumb_viewport_size = 0x7f09003b
com.tytfizikkamp:integer/mtrl_switch_thumb_pressed_duration = 0x7f090039
com.tytfizikkamp:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0c010e
com.tytfizikkamp:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f090038
com.tytfizikkamp:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
com.tytfizikkamp:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f090037
com.tytfizikkamp:integer/mtrl_card_anim_duration_ms = 0x7f090034
com.tytfizikkamp:style/MaterialAlertDialog.Material3.Title.Text = 0x7f110131
com.tytfizikkamp:integer/mtrl_calendar_year_selector_span = 0x7f090032
com.tytfizikkamp:integer/mtrl_calendar_header_orientation = 0x7f090030
com.tytfizikkamp:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0c0147
com.tytfizikkamp:integer/mtrl_btn_anim_duration_ms = 0x7f09002f
com.tytfizikkamp:integer/mtrl_btn_anim_delay_ms = 0x7f09002e
com.tytfizikkamp:integer/mtrl_badge_max_character_count = 0x7f09002d
com.tytfizikkamp:integer/material_motion_path = 0x7f09002c
com.tytfizikkamp:integer/material_motion_duration_short_2 = 0x7f09002b
com.tytfizikkamp:integer/material_motion_duration_short_1 = 0x7f09002a
com.tytfizikkamp:integer/material_motion_duration_medium_2 = 0x7f090029
com.tytfizikkamp:integer/material_motion_duration_long_2 = 0x7f090027
com.tytfizikkamp:layout/abc_list_menu_item_icon = 0x7f0b000f
com.tytfizikkamp:integer/material_motion_duration_long_1 = 0x7f090026
com.tytfizikkamp:integer/m3_sys_shape_corner_large_corner_family = 0x7f090023
com.tytfizikkamp:styleable/MaterialAlertDialog = 0x7f12004c
com.tytfizikkamp:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f090021
com.tytfizikkamp:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f090020
com.tytfizikkamp:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0c004a
com.tytfizikkamp:integer/m3_sys_motion_duration_short3 = 0x7f09001d
com.tytfizikkamp:integer/m3_sys_motion_duration_medium4 = 0x7f09001a
com.tytfizikkamp:layout/abc_action_menu_item_layout = 0x7f0b0002
com.tytfizikkamp:integer/m3_sys_motion_duration_medium3 = 0x7f090019
com.tytfizikkamp:integer/m3_sys_motion_duration_medium2 = 0x7f090018
com.tytfizikkamp:integer/m3_sys_motion_duration_long4 = 0x7f090016
com.tytfizikkamp:integer/m3_sys_motion_duration_long3 = 0x7f090015
com.tytfizikkamp:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1102c9
com.tytfizikkamp:integer/m3_sys_motion_duration_extra_long4 = 0x7f090012
com.tytfizikkamp:string/abc_searchview_description_voice = 0x7f100017
com.tytfizikkamp:integer/m3_sys_motion_duration_extra_long3 = 0x7f090011
com.tytfizikkamp:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1101aa
com.tytfizikkamp:integer/m3_sys_motion_duration_extra_long2 = 0x7f090010
com.tytfizikkamp:style/Theme.Material3.Light.Dialog.Alert = 0x7f110252
com.tytfizikkamp:integer/m3_sys_motion_duration_extra_long1 = 0x7f09000f
com.tytfizikkamp:string/tablist_description = 0x7f1000dd
com.tytfizikkamp:integer/m3_chip_anim_duration = 0x7f09000e
com.tytfizikkamp:integer/m3_card_anim_duration_ms = 0x7f09000d
com.tytfizikkamp:integer/m3_btn_anim_duration_ms = 0x7f09000b
com.tytfizikkamp:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0c0164
com.tytfizikkamp:integer/m3_btn_anim_delay_ms = 0x7f09000a
com.tytfizikkamp:integer/m3_badge_max_number = 0x7f090009
com.tytfizikkamp:integer/design_snackbar_text_max_lines = 0x7f090006
com.tytfizikkamp:styleable/AppBarLayout = 0x7f12000a
com.tytfizikkamp:integer/config_tooltipAnimTime = 0x7f090005
com.tytfizikkamp:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f11008f
com.tytfizikkamp:integer/cancel_button_image_alpha = 0x7f090004
com.tytfizikkamp:integer/app_bar_elevation_anim_duration = 0x7f090002
com.tytfizikkamp:integer/abc_config_activityShortDur = 0x7f090001
com.tytfizikkamp:integer/abc_config_activityDefaultDur = 0x7f090000
com.tytfizikkamp:id/wrap_content = 0x7f0801f7
com.tytfizikkamp:id/withinBounds = 0x7f0801f5
com.tytfizikkamp:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f1103f4
com.tytfizikkamp:id/with_icon = 0x7f0801f4
com.tytfizikkamp:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1102b0
com.tytfizikkamp:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f11013a
com.tytfizikkamp:id/visible_removing_fragment_view_tag = 0x7f0801f2
com.tytfizikkamp:id/visible = 0x7f0801f1
com.tytfizikkamp:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0801ee
com.tytfizikkamp:id/view_tree_disjoint_parent = 0x7f0801ec
com.tytfizikkamp:styleable/Slider = 0x7f12007c
com.tytfizikkamp:id/view_tag_native_id = 0x7f0801eb
com.tytfizikkamp:style/Base.Widget.MaterialComponents.TextView = 0x7f110121
com.tytfizikkamp:id/view_offset_helper = 0x7f0801e9
com.tytfizikkamp:id/use_hardware_layer = 0x7f0801e7
com.tytfizikkamp:style/Widget.MaterialComponents.Button.TextButton = 0x7f11041b
com.tytfizikkamp:style/TextAppearance.Material3.SearchBar = 0x7f1101fb
com.tytfizikkamp:id/up = 0x7f0801e5
com.tytfizikkamp:id/unlabeled = 0x7f0801e4
com.tytfizikkamp:id/unchecked = 0x7f0801e2
com.tytfizikkamp:layout/paused_in_debugger_view = 0x7f0b006b
com.tytfizikkamp:id/transition_layout_save = 0x7f0801dc
com.tytfizikkamp:id/transition_current_scene = 0x7f0801da
com.tytfizikkamp:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f110196
com.tytfizikkamp:id/transitionToStart = 0x7f0801d8
com.tytfizikkamp:id/transitionToEnd = 0x7f0801d7
com.tytfizikkamp:id/transform_origin = 0x7f0801d6
com.tytfizikkamp:id/touch_outside = 0x7f0801d4
com.tytfizikkamp:style/ThemeOverlay.Material3.Button = 0x7f11029e
com.tytfizikkamp:id/topPanel = 0x7f0801d3
com.tytfizikkamp:id/toggle = 0x7f0801d1
com.tytfizikkamp:id/textinput_suffix_text = 0x7f0801cc
com.tytfizikkamp:style/Theme.AppCompat.NoActionBar = 0x7f11022b
com.tytfizikkamp:id/textinput_prefix_text = 0x7f0801cb
com.tytfizikkamp:id/textinput_placeholder = 0x7f0801ca
com.tytfizikkamp:id/textinput_helper_text = 0x7f0801c9
com.tytfizikkamp:id/textinput_error = 0x7f0801c8
com.tytfizikkamp:id/textinput_counter = 0x7f0801c7
com.tytfizikkamp:id/text_input_start_icon = 0x7f0801c6
com.tytfizikkamp:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f11038c
com.tytfizikkamp:id/text_input_error_icon = 0x7f0801c5
com.tytfizikkamp:id/textTop = 0x7f0801c3
com.tytfizikkamp:id/textSpacerNoTitle = 0x7f0801c1
com.tytfizikkamp:id/text = 0x7f0801bd
com.tytfizikkamp:id/tag_unhandled_key_event_manager = 0x7f0801ba
com.tytfizikkamp:id/tag_transition_group = 0x7f0801b9
com.tytfizikkamp:id/tag_system_bar_state_monitor = 0x7f0801b8
com.tytfizikkamp:id/tag_state_description = 0x7f0801b7
com.tytfizikkamp:id/tag_on_receive_content_listener = 0x7f0801b4
com.tytfizikkamp:id/tag_compat_insets_dispatch = 0x7f0801b2
com.tytfizikkamp:id/tag_accessibility_pane_title = 0x7f0801b1
com.tytfizikkamp:id/tag_accessibility_heading = 0x7f0801b0
com.tytfizikkamp:id/tag_accessibility_actions = 0x7f0801ae
com.tytfizikkamp:id/tabMode = 0x7f0801ad
com.tytfizikkamp:id/submit_area = 0x7f0801ac
com.tytfizikkamp:id/stretch = 0x7f0801aa
com.tytfizikkamp:string/mtrl_checkbox_button_icon_path_name = 0x7f100089
com.tytfizikkamp:id/stop = 0x7f0801a9
com.tytfizikkamp:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1103a2
com.tytfizikkamp:style/Widget.Material3.Chip.Input = 0x7f110384
com.tytfizikkamp:id/staticPostLayout = 0x7f0801a8
com.tytfizikkamp:style/Theme.AppCompat.CompactMenu = 0x7f110217
com.tytfizikkamp:id/staticLayout = 0x7f0801a7
com.tytfizikkamp:id/startVertical = 0x7f0801a6
com.tytfizikkamp:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1102ae
com.tytfizikkamp:id/startHorizontal = 0x7f0801a4
com.tytfizikkamp:id/start = 0x7f0801a3
com.tytfizikkamp:id/src_over = 0x7f0801a1
com.tytfizikkamp:id/src_in = 0x7f0801a0
com.tytfizikkamp:id/src_atop = 0x7f08019f
com.tytfizikkamp:id/square = 0x7f08019e
com.tytfizikkamp:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1100fe
com.tytfizikkamp:id/split_action_bar = 0x7f08019b
com.tytfizikkamp:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1103cb
com.tytfizikkamp:id/spline = 0x7f08019a
com.tytfizikkamp:id/special_effects_controller_view_tag = 0x7f080199
com.tytfizikkamp:id/snapMargins = 0x7f080197
com.tytfizikkamp:id/slide = 0x7f080193
com.tytfizikkamp:id/skipCollapsed = 0x7f080192
com.tytfizikkamp:style/TextAppearance.Material3.DisplaySmall = 0x7f1101f3
com.tytfizikkamp:id/showTitle = 0x7f080190
com.tytfizikkamp:id/showHome = 0x7f08018f
com.tytfizikkamp:id/selected = 0x7f08018b
com.tytfizikkamp:id/search_voice_btn = 0x7f080189
com.tytfizikkamp:styleable/CollapsingToolbarLayout = 0x7f120023
com.tytfizikkamp:id/search_mag_icon = 0x7f080186
com.tytfizikkamp:id/search_go_btn = 0x7f080185
com.tytfizikkamp:id/search_edit_frame = 0x7f080184
com.tytfizikkamp:id/search_button = 0x7f080182
com.tytfizikkamp:id/search_bar = 0x7f080181
com.tytfizikkamp:id/search_badge = 0x7f080180
com.tytfizikkamp:id/scrollable = 0x7f08017f
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Display4 = 0x7f110021
com.tytfizikkamp:id/scrollView = 0x7f08017e
com.tytfizikkamp:id/scrollIndicatorDown = 0x7f08017c
com.tytfizikkamp:id/scroll = 0x7f08017b
com.tytfizikkamp:id/scale = 0x7f080179
com.tytfizikkamp:id/sawtooth = 0x7f080178
com.tytfizikkamp:id/rounded = 0x7f080174
com.tytfizikkamp:id/role = 0x7f080173
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1101c4
com.tytfizikkamp:id/rn_redbox_stack = 0x7f080172
com.tytfizikkamp:styleable/PopupWindowBackgroundState = 0x7f12006e
com.tytfizikkamp:id/rn_redbox_report_button = 0x7f080170
com.tytfizikkamp:id/rn_redbox_loading_indicator = 0x7f08016e
com.tytfizikkamp:id/rn_redbox_line_separator = 0x7f08016d
com.tytfizikkamp:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0c00bc
com.tytfizikkamp:id/rn_redbox_dismiss_button = 0x7f08016c
com.tytfizikkamp:id/rn_frame_method = 0x7f08016b
com.tytfizikkamp:id/rn_frame_file = 0x7f08016a
com.tytfizikkamp:string/link_description = 0x7f10005b
com.tytfizikkamp:id/right_side = 0x7f080169
com.tytfizikkamp:id/right = 0x7f080166
com.tytfizikkamp:id/reverseSawtooth = 0x7f080165
com.tytfizikkamp:id/report_drawn = 0x7f080164
com.tytfizikkamp:id/rectangles = 0x7f080163
com.tytfizikkamp:id/ratio = 0x7f080161
com.tytfizikkamp:id/progress_horizontal = 0x7f08015f
com.tytfizikkamp:id/pressed = 0x7f08015d
com.tytfizikkamp:id/postLayout = 0x7f08015c
com.tytfizikkamp:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f11047a
com.tytfizikkamp:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0c00be
com.tytfizikkamp:id/position = 0x7f08015b
com.tytfizikkamp:string/bottomsheet_drag_handle_content_description = 0x7f100024
com.tytfizikkamp:id/pointer_events = 0x7f08015a
com.tytfizikkamp:id/percent = 0x7f080158
com.tytfizikkamp:id/peekHeight = 0x7f080157
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f110073
com.tytfizikkamp:id/pathRelative = 0x7f080156
com.tytfizikkamp:id/password_toggle = 0x7f080154
com.tytfizikkamp:id/transform = 0x7f0801d5
com.tytfizikkamp:id/parent_matrix = 0x7f080153
com.tytfizikkamp:id/parentRelative = 0x7f080152
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f110034
com.tytfizikkamp:id/parentPanel = 0x7f080151
com.tytfizikkamp:id/parent = 0x7f080150
com.tytfizikkamp:id/parallax = 0x7f08014f
com.tytfizikkamp:id/packed = 0x7f08014e
com.tytfizikkamp:id/outward = 0x7f08014d
com.tytfizikkamp:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f110134
com.tytfizikkamp:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0c0095
com.tytfizikkamp:id/outline = 0x7f08014c
com.tytfizikkamp:id/open_search_view_toolbar_container = 0x7f08014a
com.tytfizikkamp:id/open_search_view_root = 0x7f080145
com.tytfizikkamp:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f11021b
com.tytfizikkamp:id/open_search_view_header_container = 0x7f080144
com.tytfizikkamp:id/open_search_view_edit_text = 0x7f080143
com.tytfizikkamp:id/open_search_view_divider = 0x7f080141
com.tytfizikkamp:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1102c2
com.tytfizikkamp:id/open_search_view_clear_button = 0x7f08013f
com.tytfizikkamp:id/open_search_view_background = 0x7f08013e
com.tytfizikkamp:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0c015f
com.tytfizikkamp:id/on = 0x7f08013c
com.tytfizikkamp:macro/m3_comp_elevated_card_container_color = 0x7f0c002a
com.tytfizikkamp:id/off = 0x7f08013b
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1101c1
com.tytfizikkamp:id/notification_background = 0x7f080138
com.tytfizikkamp:id/none = 0x7f080136
com.tytfizikkamp:id/noScroll = 0x7f080135
com.tytfizikkamp:id/never = 0x7f080134
com.tytfizikkamp:id/navigation_header_container = 0x7f080133
com.tytfizikkamp:id/navigation_bar_item_labels_group = 0x7f080130
com.tytfizikkamp:id/mtrl_picker_text_input_date = 0x7f080127
com.tytfizikkamp:id/mtrl_picker_header_toggle = 0x7f080126
com.tytfizikkamp:id/mtrl_picker_header_title_and_selection = 0x7f080125
com.tytfizikkamp:id/mtrl_picker_header = 0x7f080123
com.tytfizikkamp:id/mtrl_picker_fullscreen = 0x7f080122
com.tytfizikkamp:id/mtrl_motion_snapshot_view = 0x7f080121
com.tytfizikkamp:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0c0068
com.tytfizikkamp:macro/m3_comp_dialog_supporting_text_type = 0x7f0c0027
com.tytfizikkamp:id/mtrl_internal_children_alpha_tag = 0x7f080120
com.tytfizikkamp:id/mtrl_child_content_container = 0x7f08011f
com.tytfizikkamp:id/mtrl_calendar_main_pane = 0x7f080119
com.tytfizikkamp:id/mtrl_calendar_frame = 0x7f080118
com.tytfizikkamp:id/mtrl_anchor_parent = 0x7f080115
com.tytfizikkamp:id/month_title = 0x7f080113
com.tytfizikkamp:id/month_navigation_previous = 0x7f080112
com.tytfizikkamp:id/month_navigation_next = 0x7f080111
com.tytfizikkamp:id/month_navigation_bar = 0x7f08010f
com.tytfizikkamp:id/mix_blend_mode = 0x7f08010d
com.tytfizikkamp:style/Base.Theme.AppCompat.Dialog = 0x7f110050
com.tytfizikkamp:id/middle = 0x7f08010b
com.tytfizikkamp:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f11029d
com.tytfizikkamp:id/message = 0x7f08010a
com.tytfizikkamp:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0c008e
com.tytfizikkamp:id/matrix = 0x7f080109
com.tytfizikkamp:id/material_timepicker_view = 0x7f080107
com.tytfizikkamp:id/material_timepicker_ok_button = 0x7f080106
com.tytfizikkamp:style/Widget.Material3.CardView.Outlined = 0x7f11037e
com.tytfizikkamp:id/material_timepicker_container = 0x7f080104
com.tytfizikkamp:id/material_label = 0x7f0800ff
com.tytfizikkamp:id/material_hour_tv = 0x7f0800fe
com.tytfizikkamp:id/material_hour_text_input = 0x7f0800fd
com.tytfizikkamp:style/Widget.Material3.Button.TonalButton.Icon = 0x7f11037a
com.tytfizikkamp:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0c0078
com.tytfizikkamp:id/material_clock_period_toggle = 0x7f0800fc
com.tytfizikkamp:id/material_clock_hand = 0x7f0800f8
com.tytfizikkamp:styleable/CompoundButton = 0x7f120026
com.tytfizikkamp:id/material_clock_face = 0x7f0800f7
com.tytfizikkamp:id/material_clock_display = 0x7f0800f5
com.tytfizikkamp:id/list_item = 0x7f0800f0
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.Light = 0x7f1102e5
com.tytfizikkamp:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1100f8
com.tytfizikkamp:id/linear = 0x7f0800ee
com.tytfizikkamp:id/line3 = 0x7f0800ed
com.tytfizikkamp:id/leftToRight = 0x7f0800ea
com.tytfizikkamp:id/left = 0x7f0800e9
com.tytfizikkamp:id/layout = 0x7f0800e8
com.tytfizikkamp:id/labelled_by = 0x7f0800e7
com.tytfizikkamp:id/labeled = 0x7f0800e6
com.tytfizikkamp:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f110195
com.tytfizikkamp:id/jumpToStart = 0x7f0800e5
com.tytfizikkamp:id/jumpToEnd = 0x7f0800e4
com.tytfizikkamp:id/item_touch_helper_previous_elevation = 0x7f0800e3
com.tytfizikkamp:id/italic = 0x7f0800e2
com.tytfizikkamp:string/state_busy_description = 0x7f1000d4
com.tytfizikkamp:id/invisible = 0x7f0800e0
com.tytfizikkamp:id/indeterminate = 0x7f0800dd
com.tytfizikkamp:id/image = 0x7f0800dc
com.tytfizikkamp:id/icon_group = 0x7f0800d8
com.tytfizikkamp:id/honorRequest = 0x7f0800d6
com.tytfizikkamp:layout/material_clockface_view = 0x7f0b003d
com.tytfizikkamp:id/hideable = 0x7f0800d3
com.tytfizikkamp:layout/mtrl_layout_snackbar = 0x7f0b0056
com.tytfizikkamp:id/hide_ime_id = 0x7f0800d2
com.tytfizikkamp:id/header_title = 0x7f0800d1
com.tytfizikkamp:id/groups = 0x7f0800d0
com.tytfizikkamp:id/group_divider = 0x7f0800cf
com.tytfizikkamp:id/graph_wrap = 0x7f0800ce
com.tytfizikkamp:id/ghost_view = 0x7f0800ca
com.tytfizikkamp:id/fps_text = 0x7f0800c7
com.tytfizikkamp:id/flip = 0x7f0800c3
com.tytfizikkamp:layout/material_timepicker_textinput_display = 0x7f0b0044
com.tytfizikkamp:id/fixed = 0x7f0800c2
com.tytfizikkamp:id/tag_screen_reader_focusable = 0x7f0801b6
com.tytfizikkamp:id/fitXY = 0x7f0800c1
com.tytfizikkamp:id/fitToContents = 0x7f0800c0
com.tytfizikkamp:layout/notification_template_part_chronometer = 0x7f0b0069
com.tytfizikkamp:id/fitStart = 0x7f0800bf
com.tytfizikkamp:id/fitCenter = 0x7f0800bd
com.tytfizikkamp:id/fitBottomStart = 0x7f0800bc
com.tytfizikkamp:id/filled = 0x7f0800ba
com.tytfizikkamp:style/Theme.Material3.Light.BottomSheetDialog = 0x7f110250
com.tytfizikkamp:style/Animation.Material3.SideSheetDialog.Right = 0x7f11000b
com.tytfizikkamp:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0c0130
com.tytfizikkamp:id/fill_vertical = 0x7f0800b9
com.tytfizikkamp:id/fill_horizontal = 0x7f0800b8
com.tytfizikkamp:id/expanded_menu = 0x7f0800b5
com.tytfizikkamp:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0c00cc
com.tytfizikkamp:id/exitUntilCollapsed = 0x7f0800b3
com.tytfizikkamp:id/escape = 0x7f0800b2
com.tytfizikkamp:id/enterAlways = 0x7f0800b0
com.tytfizikkamp:id/embed = 0x7f0800ad
com.tytfizikkamp:id/elastic = 0x7f0800ac
com.tytfizikkamp:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f110296
com.tytfizikkamp:string/bottomsheet_action_expand = 0x7f100021
com.tytfizikkamp:id/edit_text_id = 0x7f0800ab
com.tytfizikkamp:id/edit_query = 0x7f0800aa
com.tytfizikkamp:id/edge = 0x7f0800a9
com.tytfizikkamp:id/easeInOut = 0x7f0800a7
com.tytfizikkamp:id/dropdown_menu = 0x7f0800a5
com.tytfizikkamp:id/dragEnd = 0x7f0800a0
com.tytfizikkamp:id/dragDown = 0x7f08009f
com.tytfizikkamp:id/disjoint = 0x7f08009e
com.tytfizikkamp:id/disableScroll = 0x7f08009d
com.tytfizikkamp:id/disablePostScroll = 0x7f08009c
com.tytfizikkamp:id/dimensions = 0x7f080099
com.tytfizikkamp:id/design_menu_item_action_area_stub = 0x7f080095
com.tytfizikkamp:id/decor_content_parent = 0x7f080090
com.tytfizikkamp:style/Widget.Material3.CircularProgressIndicator.Legacy.Small = 0x7f110390
com.tytfizikkamp:id/date_picker_actions = 0x7f08008d
com.tytfizikkamp:string/side_sheet_behavior = 0x7f1000d2
com.tytfizikkamp:id/custom = 0x7f08008a
com.tytfizikkamp:id/cradle = 0x7f080089
com.tytfizikkamp:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0c011d
com.tytfizikkamp:id/counterclockwise = 0x7f080088
com.tytfizikkamp:id/cos = 0x7f080087
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f110160
com.tytfizikkamp:id/contiguous = 0x7f080085
com.tytfizikkamp:id/contentPanel = 0x7f080084
com.tytfizikkamp:style/Base.V28.Theme.AppCompat = 0x7f1100bb
com.tytfizikkamp:id/content = 0x7f080083
com.tytfizikkamp:id/container = 0x7f080082
com.tytfizikkamp:id/compress = 0x7f080080
com.tytfizikkamp:id/collapseActionView = 0x7f08007f
com.tytfizikkamp:styleable/SideSheetBehavior_Layout = 0x7f12007a
com.tytfizikkamp:id/clip_vertical = 0x7f08007d
com.tytfizikkamp:id/circle_center = 0x7f08007a
com.tytfizikkamp:id/chronometer = 0x7f080079
com.tytfizikkamp:id/checkbox = 0x7f080077
com.tytfizikkamp:id/chain = 0x7f080075
com.tytfizikkamp:id/center_vertical = 0x7f080074
com.tytfizikkamp:id/center_horizontal = 0x7f080073
com.tytfizikkamp:id/centerCrop = 0x7f080071
com.tytfizikkamp:id/catalyst_redbox_title = 0x7f08006f
com.tytfizikkamp:id/buttonPanel = 0x7f08006c
com.tytfizikkamp:id/button = 0x7f08006b
com.tytfizikkamp:id/bounce = 0x7f08006a
com.tytfizikkamp:id/beginOnFirstDraw = 0x7f080066
com.tytfizikkamp:id/baseline = 0x7f080065
com.tytfizikkamp:style/Widget.Material3.Chip.Suggestion = 0x7f110388
com.tytfizikkamp:id/barrier = 0x7f080064
com.tytfizikkamp:style/Theme.Material3.DynamicColors.Dark = 0x7f110249
com.tytfizikkamp:id/autofill_inline_suggestion_title = 0x7f080063
com.tytfizikkamp:string/material_timepicker_pm = 0x7f10007f
com.tytfizikkamp:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0c001f
com.tytfizikkamp:id/autofill_inline_suggestion_subtitle = 0x7f080062
com.tytfizikkamp:string/mtrl_picker_confirm = 0x7f10009a
com.tytfizikkamp:id/autofill_inline_suggestion_start_icon = 0x7f080061
com.tytfizikkamp:id/autofill_inline_suggestion_end_icon = 0x7f080060
com.tytfizikkamp:id/autoCompleteToStart = 0x7f08005f
com.tytfizikkamp:id/autoCompleteToEnd = 0x7f08005e
com.tytfizikkamp:id/auto = 0x7f08005c
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1103ba
com.tytfizikkamp:id/async = 0x7f08005b
com.tytfizikkamp:id/asConfigured = 0x7f08005a
com.tytfizikkamp:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f110464
com.tytfizikkamp:id/arc = 0x7f080059
com.tytfizikkamp:id/animateToStart = 0x7f080058
com.tytfizikkamp:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0c0061
com.tytfizikkamp:id/all = 0x7f080055
com.tytfizikkamp:id/aligned = 0x7f080054
com.tytfizikkamp:styleable/Spinner = 0x7f12007f
com.tytfizikkamp:id/alertTitle = 0x7f080052
com.tytfizikkamp:macro/m3_comp_outlined_text_field_caret_color = 0x7f0c00b0
com.tytfizikkamp:id/add = 0x7f080051
com.tytfizikkamp:id/action_text = 0x7f08004e
com.tytfizikkamp:id/action_mode_close_button = 0x7f08004d
com.tytfizikkamp:id/action_mode_bar_stub = 0x7f08004c
com.tytfizikkamp:id/action_mode_bar = 0x7f08004b
com.tytfizikkamp:id/action_menu_presenter = 0x7f08004a
com.tytfizikkamp:id/snackbar_action = 0x7f080194
com.tytfizikkamp:id/action_menu_divider = 0x7f080049
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f110165
com.tytfizikkamp:id/action_image = 0x7f080048
com.tytfizikkamp:id/action_container = 0x7f080045
com.tytfizikkamp:id/action_bar_title = 0x7f080044
com.tytfizikkamp:attr/actionBarSize = 0x7f030003
com.tytfizikkamp:id/action_bar_spinner = 0x7f080042
com.tytfizikkamp:id/action_bar_root = 0x7f080041
com.tytfizikkamp:id/action_bar = 0x7f08003e
com.tytfizikkamp:drawable/abc_scrubber_track_mtrl_alpha = 0x7f070061
com.tytfizikkamp:id/accessibility_state = 0x7f08003b
com.tytfizikkamp:id/accessibility_order_flow_to = 0x7f080038
com.tytfizikkamp:id/accessibility_order_dirty = 0x7f080037
com.tytfizikkamp:dimen/mtrl_shape_corner_size_large_component = 0x7f0602e3
com.tytfizikkamp:id/accessibility_order = 0x7f080036
com.tytfizikkamp:attr/cornerFamilyTopRight = 0x7f030140
com.tytfizikkamp:id/accessibility_custom_action_7 = 0x7f080030
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f1102f1
com.tytfizikkamp:drawable/abc_star_half_black_48dp = 0x7f070068
com.tytfizikkamp:id/accessibility_custom_action_6 = 0x7f08002f
com.tytfizikkamp:id/accessibility_custom_action_30 = 0x7f08002b
com.tytfizikkamp:macro/m3_comp_navigation_bar_label_text_type = 0x7f0c0077
com.tytfizikkamp:id/accessibility_custom_action_29 = 0x7f080029
com.tytfizikkamp:id/accessibility_custom_action_25 = 0x7f080025
com.tytfizikkamp:id/accessibility_custom_action_24 = 0x7f080024
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f1101e2
com.tytfizikkamp:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0c00ca
com.tytfizikkamp:id/accessibility_custom_action_17 = 0x7f08001c
com.tytfizikkamp:id/accessibility_custom_action_16 = 0x7f08001b
com.tytfizikkamp:id/accessibility_custom_action_12 = 0x7f080017
com.tytfizikkamp:layout/mtrl_layout_snackbar_include = 0x7f0b0057
com.tytfizikkamp:color/m3_ref_palette_primary95 = 0x7f050133
com.tytfizikkamp:id/accessibility_custom_action_11 = 0x7f080016
com.tytfizikkamp:attr/subtitleTextColor = 0x7f0303e4
com.tytfizikkamp:id/accessibility_custom_action_1 = 0x7f080014
com.tytfizikkamp:id/accessibility_custom_action_0 = 0x7f080013
com.tytfizikkamp:id/accessibility_collection_item = 0x7f080012
com.tytfizikkamp:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f110343
com.tytfizikkamp:id/accessibility_action_clickable_span = 0x7f08000f
com.tytfizikkamp:id/TOP_START = 0x7f08000d
com.tytfizikkamp:string/abc_action_bar_up_description = 0x7f100001
com.tytfizikkamp:id/SHOW_ALL = 0x7f080008
com.tytfizikkamp:id/SHIFT = 0x7f080007
com.tytfizikkamp:attr/maxAcceleration = 0x7f0302e7
com.tytfizikkamp:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f050090
com.tytfizikkamp:id/BOTTOM_END = 0x7f080001
com.tytfizikkamp:id/ALT = 0x7f080000
com.tytfizikkamp:dimen/mtrl_btn_focused_z = 0x7f06025f
com.tytfizikkamp:drawable/tooltip_frame_dark = 0x7f0700ef
com.tytfizikkamp:drawable/notify_panel_notification_icon_bg = 0x7f0700e7
com.tytfizikkamp:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f11030e
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f110264
com.tytfizikkamp:drawable/notification_template_icon_bg = 0x7f0700e4
com.tytfizikkamp:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f1102b4
com.tytfizikkamp:drawable/notification_oversize_large_icon_bg = 0x7f0700e3
com.tytfizikkamp:drawable/node_modules_reactnavigation_elements_lib_module_assets_searchicon = 0x7f0700da
com.tytfizikkamp:drawable/node_modules_reactnavigation_elements_lib_module_assets_closeicon = 0x7f0700d9
com.tytfizikkamp:style/Widget.Material3.Badge = 0x7f11035e
com.tytfizikkamp:id/screen = 0x7f08017a
com.tytfizikkamp:drawable/mtrl_switch_thumb_pressed_checked = 0x7f0700cd
com.tytfizikkamp:drawable/node_modules_reactnavigation_elements_lib_module_assets_backiconmask = 0x7f0700d7
com.tytfizikkamp:color/m3_dark_primary_text_disable_only = 0x7f05007a
com.tytfizikkamp:drawable/mtrl_switch_track_decoration = 0x7f0700d3
com.tytfizikkamp:anim/rns_ios_from_right_foreground_open = 0x7f010041
com.tytfizikkamp:drawable/mtrl_switch_track = 0x7f0700d2
com.tytfizikkamp:drawable/mtrl_switch_thumb_checked = 0x7f0700c9
com.tytfizikkamp:attr/toolbarId = 0x7f030470
com.tytfizikkamp:dimen/mtrl_btn_pressed_z = 0x7f06026a
com.tytfizikkamp:drawable/mtrl_switch_thumb = 0x7f0700c8
com.tytfizikkamp:attr/collapseContentDescription = 0x7f0300d9
com.tytfizikkamp:drawable/mtrl_ic_error = 0x7f0700c3
com.tytfizikkamp:drawable/mtrl_ic_check_mark = 0x7f0700c0
com.tytfizikkamp:drawable/mtrl_ic_arrow_drop_up = 0x7f0700be
com.tytfizikkamp:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f0700b9
com.tytfizikkamp:attr/clockFaceBackgroundColor = 0x7f0300cd
com.tytfizikkamp:dimen/m3_badge_with_text_horizontal_offset = 0x7f0600b8
com.tytfizikkamp:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f0700b5
com.tytfizikkamp:string/mtrl_exceed_max_badge_number_content_description = 0x7f100092
com.tytfizikkamp:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07005e
com.tytfizikkamp:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f0700b4
com.tytfizikkamp:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f0700b2
com.tytfizikkamp:drawable/mtrl_checkbox_button = 0x7f0700b1
com.tytfizikkamp:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1103ce
com.tytfizikkamp:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f0700af
com.tytfizikkamp:attr/checkMarkCompat = 0x7f0300a7
com.tytfizikkamp:attr/controlBackground = 0x7f030139
com.tytfizikkamp:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f0700ae
com.tytfizikkamp:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f0700ad
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1102e7
com.tytfizikkamp:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f0700ac
com.tytfizikkamp:styleable/SimpleDraweeView = 0x7f12007b
com.tytfizikkamp:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f110297
com.tytfizikkamp:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f060203
com.tytfizikkamp:drawable/material_ic_clear_black_24dp = 0x7f0700a8
com.tytfizikkamp:drawable/material_cursor_drawable = 0x7f0700a6
com.tytfizikkamp:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f1103fc
com.tytfizikkamp:id/select_dialog_listview = 0x7f08018a
com.tytfizikkamp:drawable/m3_tabs_transparent_background = 0x7f0700a5
com.tytfizikkamp:xml/rn_dev_preferences = 0x7f130000
com.tytfizikkamp:drawable/notification_tile_bg = 0x7f0700e6
com.tytfizikkamp:dimen/m3_comp_suggestion_chip_container_height = 0x7f06018b
com.tytfizikkamp:drawable/m3_tabs_background = 0x7f0700a2
com.tytfizikkamp:drawable/m3_radiobutton_ripple = 0x7f0700a0
com.tytfizikkamp:attr/layout_anchorGravity = 0x7f03025d
com.tytfizikkamp:drawable/ic_search_black_24 = 0x7f070099
com.tytfizikkamp:drawable/ic_mtrl_chip_checked_circle = 0x7f070096
com.tytfizikkamp:color/secondary_text_default_material_light = 0x7f0502fc
com.tytfizikkamp:drawable/ic_mtrl_chip_checked_black = 0x7f070095
com.tytfizikkamp:color/m3_card_ripple_color = 0x7f05006d
com.tytfizikkamp:drawable/ic_m3_chip_close = 0x7f070093
com.tytfizikkamp:color/m3_sys_color_on_secondary_fixed_variant = 0x7f0501f2
com.tytfizikkamp:dimen/m3_comp_slider_inactive_track_height = 0x7f060188
com.tytfizikkamp:drawable/ic_m3_chip_checked_circle = 0x7f070092
com.tytfizikkamp:drawable/ic_keyboard_black_24dp = 0x7f070090
com.tytfizikkamp:style/Widget.Material3.SideSheet.Modal = 0x7f1103e6
com.tytfizikkamp:drawable/ic_clear_black_24 = 0x7f07008e
com.tytfizikkamp:drawable/ic_call_answer_video_low = 0x7f07008b
com.tytfizikkamp:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f110469
com.tytfizikkamp:drawable/ic_call_answer_video = 0x7f07008a
com.tytfizikkamp:id/autoComplete = 0x7f08005d
com.tytfizikkamp:drawable/ic_call_answer = 0x7f070088
com.tytfizikkamp:id/META = 0x7f080005
com.tytfizikkamp:id/blocking = 0x7f080068
com.tytfizikkamp:drawable/ic_arrow_back_black_24 = 0x7f070087
com.tytfizikkamp:drawable/design_ic_visibility_off = 0x7f070084
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1103c5
com.tytfizikkamp:style/Base.Theme.AppCompat.CompactMenu = 0x7f11004f
com.tytfizikkamp:drawable/design_ic_visibility = 0x7f070083
com.tytfizikkamp:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0c011f
com.tytfizikkamp:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f070081
com.tytfizikkamp:attr/maxHeight = 0x7f0302eb
com.tytfizikkamp:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f07007b
com.tytfizikkamp:string/mtrl_exceed_max_badge_number_suffix = 0x7f100093
com.tytfizikkamp:drawable/avd_hide_password = 0x7f070078
com.tytfizikkamp:drawable/abc_vector_test = 0x7f070076
com.tytfizikkamp:drawable/abc_text_select_handle_right_mtrl = 0x7f070070
com.tytfizikkamp:attr/sideSheetModalStyle = 0x7f0303b1
com.tytfizikkamp:drawable/$avd_show_password__2 = 0x7f070005
com.tytfizikkamp:drawable/abc_text_cursor_material = 0x7f07006d
com.tytfizikkamp:layout/mtrl_alert_select_dialog_multichoice = 0x7f0b0049
com.tytfizikkamp:drawable/abc_switch_track_mtrl_alpha = 0x7f07006a
com.tytfizikkamp:attr/contentInsetRight = 0x7f03012d
com.tytfizikkamp:color/cardview_light_background = 0x7f05002c
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant24 = 0x7f0500be
com.tytfizikkamp:drawable/abc_star_black_48dp = 0x7f070067
com.tytfizikkamp:color/mtrl_chip_text_color = 0x7f0502c7
com.tytfizikkamp:drawable/abc_seekbar_track_material = 0x7f070064
com.tytfizikkamp:string/catalyst_hot_reloading = 0x7f10003b
com.tytfizikkamp:drawable/abc_seekbar_tick_mark_material = 0x7f070063
com.tytfizikkamp:color/material_dynamic_tertiary50 = 0x7f05025d
com.tytfizikkamp:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f07005f
com.tytfizikkamp:drawable/abc_ratingbar_small_material = 0x7f07005c
com.tytfizikkamp:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0c0125
com.tytfizikkamp:drawable/abc_ratingbar_indicator_material = 0x7f07005a
com.tytfizikkamp:drawable/mtrl_ic_arrow_drop_down = 0x7f0700bd
com.tytfizikkamp:color/m3_ref_palette_tertiary95 = 0x7f05014d
com.tytfizikkamp:drawable/abc_popup_background_mtrl_mult = 0x7f070059
com.tytfizikkamp:color/mtrl_switch_track_decoration_tint = 0x7f0502e2
com.tytfizikkamp:drawable/abc_list_selector_holo_dark = 0x7f070056
com.tytfizikkamp:color/material_grey_800 = 0x7f050268
com.tytfizikkamp:drawable/abc_list_selector_background_transition_holo_light = 0x7f070053
com.tytfizikkamp:drawable/abc_list_pressed_holo_light = 0x7f070051
com.tytfizikkamp:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f110172
com.tytfizikkamp:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f07000e
com.tytfizikkamp:drawable/abc_list_focused_holo = 0x7f07004e
com.tytfizikkamp:id/forever = 0x7f0800c6
com.tytfizikkamp:drawable/abc_item_background_holo_light = 0x7f07004b
com.tytfizikkamp:layout/abc_action_mode_bar = 0x7f0b0004
com.tytfizikkamp:anim/rns_slide_out_to_right = 0x7f01004b
com.tytfizikkamp:drawable/abc_ic_voice_search_api_material = 0x7f070049
com.tytfizikkamp:style/Widget.Material3.Button.IconButton.Filled = 0x7f11036e
com.tytfizikkamp:dimen/mtrl_toolbar_default_height = 0x7f060306
com.tytfizikkamp:drawable/abc_ic_search_api_material = 0x7f070048
com.tytfizikkamp:attr/contentInsetStartWithNavigation = 0x7f03012f
com.tytfizikkamp:attr/itemFillColor = 0x7f030232
com.tytfizikkamp:color/m3_ref_palette_neutral17 = 0x7f050107
com.tytfizikkamp:drawable/abc_btn_radio_material_anim = 0x7f070032
com.tytfizikkamp:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f070047
com.tytfizikkamp:style/Theme.Material3.DynamicColors.DayNight.NoActionBar = 0x7f11024c
com.tytfizikkamp:string/call_notification_decline_action = 0x7f100027
com.tytfizikkamp:color/m3_ref_palette_primary60 = 0x7f05012f
com.tytfizikkamp:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f070045
com.tytfizikkamp:drawable/abc_ic_clear_material = 0x7f07003f
com.tytfizikkamp:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f07003e
com.tytfizikkamp:drawable/abc_dialog_material_background = 0x7f07003b
com.tytfizikkamp:drawable/abc_cab_background_internal_bg = 0x7f070037
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f1102e9
com.tytfizikkamp:dimen/m3_carousel_small_item_default_corner_size = 0x7f0600f1
com.tytfizikkamp:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f070035
com.tytfizikkamp:macro/m3_comp_time_picker_headline_type = 0x7f0c0151
com.tytfizikkamp:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f070033
com.tytfizikkamp:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070058
com.tytfizikkamp:drawable/abc_btn_radio_material = 0x7f070031
com.tytfizikkamp:drawable/abc_btn_default_mtrl_shape = 0x7f070030
com.tytfizikkamp:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f07002e
com.tytfizikkamp:styleable/Tooltip = 0x7f12008e
com.tytfizikkamp:id/accessibility_actions = 0x7f080010
com.tytfizikkamp:attr/backgroundInsetTop = 0x7f030051
com.tytfizikkamp:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f07002d
com.tytfizikkamp:attr/defaultState = 0x7f030163
com.tytfizikkamp:drawable/abc_btn_check_material_anim = 0x7f07002c
com.tytfizikkamp:style/Widget.AppCompat.Button.Colored = 0x7f11030a
com.tytfizikkamp:drawable/abc_btn_check_material = 0x7f07002b
com.tytfizikkamp:drawable/abc_btn_borderless_material = 0x7f07002a
com.tytfizikkamp:drawable/node_modules_reactnavigation_elements_lib_module_assets_backicon = 0x7f0700d6
com.tytfizikkamp:dimen/m3_comp_slider_active_handle_height = 0x7f060182
com.tytfizikkamp:drawable/abc_action_bar_item_background_material = 0x7f070029
com.tytfizikkamp:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f060258
com.tytfizikkamp:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070028
com.tytfizikkamp:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f070024
com.tytfizikkamp:id/textSpacerNoButtons = 0x7f0801c0
com.tytfizikkamp:dimen/m3_extended_fab_icon_padding = 0x7f0601b2
com.tytfizikkamp:attr/dayInvalidStyle = 0x7f03015b
com.tytfizikkamp:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f070023
com.tytfizikkamp:style/Widget.Compat.NotificationActionText = 0x7f11034b
com.tytfizikkamp:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f070022
com.tytfizikkamp:id/navigation_bar_item_large_label_view = 0x7f080131
com.tytfizikkamp:drawable/rns_rounder_top_corners_shape = 0x7f0700ed
com.tytfizikkamp:color/m3_timepicker_display_background_color = 0x7f05020d
com.tytfizikkamp:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f07001f
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.Button = 0x7f1101c3
com.tytfizikkamp:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f07001d
com.tytfizikkamp:style/Base.V26.Theme.AppCompat.Light = 0x7f1100b9
com.tytfizikkamp:attr/trackColor = 0x7f03047d
com.tytfizikkamp:color/accent_material_light = 0x7f05001a
com.tytfizikkamp:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f07001a
com.tytfizikkamp:id/inward = 0x7f0800e1
com.tytfizikkamp:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f070015
com.tytfizikkamp:layout/design_layout_tab_text = 0x7f0b0024
com.tytfizikkamp:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f070013
com.tytfizikkamp:layout/mtrl_alert_dialog_title = 0x7f0b0047
com.tytfizikkamp:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f070012
com.tytfizikkamp:styleable/SearchView = 0x7f120077
com.tytfizikkamp:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f110132
com.tytfizikkamp:id/snackbar_text = 0x7f080195
com.tytfizikkamp:drawable/abc_list_pressed_holo_dark = 0x7f070050
com.tytfizikkamp:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f070011
com.tytfizikkamp:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f07000f
com.tytfizikkamp:style/Base.Widget.AppCompat.ListView = 0x7f1100ea
com.tytfizikkamp:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f07000d
com.tytfizikkamp:integer/mtrl_card_anim_delay_ms = 0x7f090033
com.tytfizikkamp:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f07000c
com.tytfizikkamp:color/material_personalized_color_on_tertiary_container = 0x7f05028c
com.tytfizikkamp:drawable/$m3_avd_hide_password__2 = 0x7f070008
com.tytfizikkamp:drawable/$m3_avd_hide_password__1 = 0x7f070007
com.tytfizikkamp:drawable/$avd_show_password__0 = 0x7f070003
com.tytfizikkamp:color/design_default_color_background = 0x7f050040
com.tytfizikkamp:drawable/$avd_hide_password__0 = 0x7f070000
com.tytfizikkamp:styleable/ListPopupWindow = 0x7f12004b
com.tytfizikkamp:style/ThemeOverlay.Material3.TabLayout = 0x7f1102cb
com.tytfizikkamp:dimen/tooltip_precise_anchor_threshold = 0x7f060320
com.tytfizikkamp:styleable/CardView = 0x7f12001b
com.tytfizikkamp:dimen/tooltip_precise_anchor_extra_offset = 0x7f06031f
com.tytfizikkamp:dimen/notification_subtext_size = 0x7f060319
com.tytfizikkamp:dimen/notification_large_icon_width = 0x7f060312
com.tytfizikkamp:style/Widget.Material3.Button.UnelevatedButton = 0x7f11037b
com.tytfizikkamp:macro/m3_comp_date_picker_modal_container_color = 0x7f0c000d
com.tytfizikkamp:dimen/notification_large_icon_height = 0x7f060311
com.tytfizikkamp:string/mtrl_checkbox_state_description_indeterminate = 0x7f10008f
com.tytfizikkamp:dimen/notification_big_circle_margin = 0x7f06030f
com.tytfizikkamp:attr/failureImageScaleType = 0x7f0301c4
com.tytfizikkamp:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0601ce
com.tytfizikkamp:dimen/notification_action_text_size = 0x7f06030e
com.tytfizikkamp:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0c0135
com.tytfizikkamp:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f06030c
com.tytfizikkamp:dimen/m3_large_fab_max_image_size = 0x7f0601ba
com.tytfizikkamp:dimen/mtrl_tooltip_padding = 0x7f06030b
com.tytfizikkamp:macro/m3_comp_fab_primary_icon_color = 0x7f0c0038
com.tytfizikkamp:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f0602ff
com.tytfizikkamp:dimen/mtrl_textinput_box_corner_radius_small = 0x7f0602fe
com.tytfizikkamp:style/TextAppearance.Material3.LabelLarge = 0x7f1101f7
com.tytfizikkamp:dimen/mtrl_switch_thumb_size = 0x7f0602fa
com.tytfizikkamp:dimen/mtrl_switch_thumb_icon_size = 0x7f0602f9
com.tytfizikkamp:dimen/mtrl_switch_text_padding = 0x7f0602f7
com.tytfizikkamp:dimen/mtrl_snackbar_padding_horizontal = 0x7f0602f6
com.tytfizikkamp:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f0602f5
com.tytfizikkamp:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f110089
com.tytfizikkamp:dimen/mtrl_snackbar_margin = 0x7f0602f4
com.tytfizikkamp:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f0602f3
com.tytfizikkamp:macro/m3_comp_fab_surface_container_color = 0x7f0c003d
com.tytfizikkamp:id/fade = 0x7f0800b6
com.tytfizikkamp:attr/materialCalendarTheme = 0x7f0302ce
com.tytfizikkamp:attr/motionDurationMedium1 = 0x7f030309
com.tytfizikkamp:dimen/mtrl_snackbar_background_corner_radius = 0x7f0602f2
com.tytfizikkamp:drawable/avd_show_password = 0x7f070079
com.tytfizikkamp:drawable/mtrl_dialog_background = 0x7f0700bb
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f1102e6
com.tytfizikkamp:layout/material_clock_display = 0x7f0b0038
com.tytfizikkamp:attr/actionBarWidgetTheme = 0x7f03000a
com.tytfizikkamp:attr/drawableTint = 0x7f03017d
com.tytfizikkamp:dimen/mtrl_slider_widget_height = 0x7f0602f0
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f1102f6
com.tytfizikkamp:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f110174
com.tytfizikkamp:color/m3_ref_palette_dynamic_secondary99 = 0x7f0500e8
com.tytfizikkamp:dimen/mtrl_slider_track_side_padding = 0x7f0602ef
com.tytfizikkamp:dimen/mtrl_slider_thumb_elevation = 0x7f0602ea
com.tytfizikkamp:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f110452
com.tytfizikkamp:dimen/mtrl_slider_label_radius = 0x7f0602e8
com.tytfizikkamp:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f060162
com.tytfizikkamp:dimen/mtrl_slider_label_padding = 0x7f0602e7
com.tytfizikkamp:attr/subheaderInsetEnd = 0x7f0303dd
com.tytfizikkamp:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0602e1
com.tytfizikkamp:id/gone = 0x7f0800cc
com.tytfizikkamp:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0602e0
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f11003a
com.tytfizikkamp:attr/buttonBarPositiveButtonStyle = 0x7f03008f
com.tytfizikkamp:dimen/mtrl_progress_circular_size_small = 0x7f0602dd
com.tytfizikkamp:string/mtrl_picker_text_input_date_range_start_hint = 0x7f1000b0
com.tytfizikkamp:attr/materialSwitchStyle = 0x7f0302e2
com.tytfizikkamp:dimen/mtrl_progress_circular_inset_medium = 0x7f0602d7
com.tytfizikkamp:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0c00ce
com.tytfizikkamp:dimen/mtrl_progress_circular_inset = 0x7f0602d5
com.tytfizikkamp:dimen/mtrl_navigation_rail_text_size = 0x7f0602d4
com.tytfizikkamp:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0602d3
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.Dark = 0x7f1102df
com.tytfizikkamp:dimen/mtrl_navigation_rail_icon_size = 0x7f0602d1
com.tytfizikkamp:dimen/mtrl_navigation_rail_default_width = 0x7f0602ce
com.tytfizikkamp:attr/titleMarginStart = 0x7f030467
com.tytfizikkamp:dimen/mtrl_navigation_rail_compact_width = 0x7f0602cd
com.tytfizikkamp:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0602cb
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f11003d
com.tytfizikkamp:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f07005d
com.tytfizikkamp:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0602ca
com.tytfizikkamp:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0602c7
com.tytfizikkamp:color/m3_chip_text_color = 0x7f050075
com.tytfizikkamp:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0602c5
com.tytfizikkamp:dimen/mtrl_min_touch_target_size = 0x7f0602c3
com.tytfizikkamp:style/Widget.Material3.Chip.Filter = 0x7f110382
com.tytfizikkamp:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0602c2
com.tytfizikkamp:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f060286
com.tytfizikkamp:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0602c1
com.tytfizikkamp:attr/windowNoTitle = 0x7f0304ac
com.tytfizikkamp:attr/listPreferredItemPaddingLeft = 0x7f0302ad
com.tytfizikkamp:dimen/mtrl_low_ripple_focused_alpha = 0x7f0602c0
com.tytfizikkamp:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f0601fe
com.tytfizikkamp:dimen/mtrl_low_ripple_default_alpha = 0x7f0602bf
com.tytfizikkamp:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0602be
com.tytfizikkamp:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f11009f
com.tytfizikkamp:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0602b9
com.tytfizikkamp:dimen/mtrl_fab_elevation = 0x7f0602b7
com.tytfizikkamp:attr/floatingActionButtonLargeStyle = 0x7f0301cd
com.tytfizikkamp:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0602b6
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f110029
com.tytfizikkamp:dimen/mtrl_extended_fab_top_padding = 0x7f0602b3
com.tytfizikkamp:dimen/mtrl_extended_fab_min_width = 0x7f0602b0
com.tytfizikkamp:dimen/m3_badge_horizontal_offset = 0x7f0600b4
com.tytfizikkamp:dimen/mtrl_extended_fab_icon_size = 0x7f0602ad
com.tytfizikkamp:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f1103f8
com.tytfizikkamp:id/material_textinput_timepicker = 0x7f080102
com.tytfizikkamp:dimen/mtrl_extended_fab_end_padding = 0x7f0602ab
com.tytfizikkamp:dimen/mtrl_extended_fab_elevation = 0x7f0602aa
com.tytfizikkamp:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0602a4
com.tytfizikkamp:dimen/mtrl_chip_text_size = 0x7f0602a3
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant22 = 0x7f0500bd
com.tytfizikkamp:dimen/mtrl_chip_pressed_translation_z = 0x7f0602a2
com.tytfizikkamp:attr/colorOnError = 0x7f0300f1
com.tytfizikkamp:color/mtrl_btn_transparent_bg_color = 0x7f0502bf
com.tytfizikkamp:drawable/notification_action_background = 0x7f0700db
com.tytfizikkamp:style/Theme.AutofillInlineSuggestion = 0x7f11022c
com.tytfizikkamp:attr/motionTarget = 0x7f030322
com.tytfizikkamp:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f06018c
com.tytfizikkamp:dimen/mtrl_card_spacing = 0x7f0602a1
com.tytfizikkamp:dimen/mtrl_card_corner_radius = 0x7f06029e
com.tytfizikkamp:id/accessibility_custom_action_14 = 0x7f080019
com.tytfizikkamp:attr/minHeight = 0x7f0302f5
com.tytfizikkamp:dimen/mtrl_card_checked_icon_margin = 0x7f06029c
com.tytfizikkamp:style/Widget.Material3.SearchView.Toolbar = 0x7f1103e3
com.tytfizikkamp:dimen/mtrl_calendar_year_height = 0x7f060298
com.tytfizikkamp:string/mtrl_picker_text_input_year_abbr = 0x7f1000b3
com.tytfizikkamp:string/catalyst_heap_capture = 0x7f10003a
com.tytfizikkamp:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f09003e
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_primary_container = 0x7f050191
com.tytfizikkamp:attr/marginLeftSystemWindowInsets = 0x7f0302b5
com.tytfizikkamp:attr/motion_triggerOnCollision = 0x7f030324
com.tytfizikkamp:attr/startIconCheckable = 0x7f0303c6
com.tytfizikkamp:dimen/mtrl_calendar_navigation_height = 0x7f06028d
com.tytfizikkamp:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f06028c
com.tytfizikkamp:id/dragStart = 0x7f0800a3
com.tytfizikkamp:id/cancel_button = 0x7f08006e
com.tytfizikkamp:dimen/mtrl_calendar_month_horizontal_padding = 0x7f06028a
com.tytfizikkamp:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f060289
com.tytfizikkamp:styleable/MaterialAlertDialogTheme = 0x7f12004d
com.tytfizikkamp:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1100d2
com.tytfizikkamp:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0a000d
com.tytfizikkamp:id/fragment_container_view_tag = 0x7f0800c8
com.tytfizikkamp:dimen/mtrl_calendar_landscape_header_width = 0x7f060288
com.tytfizikkamp:macro/m3_comp_fab_primary_container_color = 0x7f0c0036
com.tytfizikkamp:dimen/design_bottom_sheet_modal_elevation = 0x7f06006d
com.tytfizikkamp:dimen/mtrl_calendar_header_selection_line_height = 0x7f060284
com.tytfizikkamp:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f1103f3
com.tytfizikkamp:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f11026f
com.tytfizikkamp:dimen/m3_comp_switch_track_height = 0x7f060198
com.tytfizikkamp:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0602ac
com.tytfizikkamp:dimen/mtrl_calendar_header_height_fullscreen = 0x7f060283
com.tytfizikkamp:dimen/mtrl_calendar_header_height = 0x7f060282
com.tytfizikkamp:dimen/mtrl_calendar_day_vertical_padding = 0x7f06027b
com.tytfizikkamp:dimen/mtrl_calendar_day_horizontal_padding = 0x7f060279
com.tytfizikkamp:dimen/mtrl_calendar_content_padding = 0x7f060276
com.tytfizikkamp:dimen/mtrl_calendar_action_padding = 0x7f060274
com.tytfizikkamp:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0c00bd
com.tytfizikkamp:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f060272
com.tytfizikkamp:dimen/mtrl_btn_text_size = 0x7f060270
com.tytfizikkamp:id/text_input_end_icon = 0x7f0801c4
com.tytfizikkamp:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f06026b
com.tytfizikkamp:drawable/indeterminate_static = 0x7f07009a
com.tytfizikkamp:color/cardview_shadow_start_color = 0x7f05002e
com.tytfizikkamp:dimen/mtrl_btn_padding_left = 0x7f060267
com.tytfizikkamp:dimen/mtrl_btn_padding_bottom = 0x7f060266
com.tytfizikkamp:dimen/mtrl_btn_max_width = 0x7f060265
com.tytfizikkamp:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0c0014
com.tytfizikkamp:dimen/mtrl_btn_letter_spacing = 0x7f060264
com.tytfizikkamp:styleable/ForegroundLinearLayout = 0x7f120037
com.tytfizikkamp:attr/dragScale = 0x7f030174
com.tytfizikkamp:dimen/mtrl_btn_inset = 0x7f060263
com.tytfizikkamp:drawable/rn_edit_text_material = 0x7f0700ec
com.tytfizikkamp:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0c014b
com.tytfizikkamp:dimen/mtrl_btn_icon_padding = 0x7f060262
com.tytfizikkamp:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0c00aa
com.tytfizikkamp:attr/contentDescription = 0x7f030129
com.tytfizikkamp:dimen/mtrl_btn_icon_btn_padding_left = 0x7f060261
com.tytfizikkamp:dimen/mtrl_btn_hovered_z = 0x7f060260
com.tytfizikkamp:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1103d1
com.tytfizikkamp:style/ThemeOverlay.AppCompat = 0x7f110289
com.tytfizikkamp:color/mtrl_textinput_default_box_stroke_color = 0x7f0502ea
com.tytfizikkamp:dimen/mtrl_btn_disabled_z = 0x7f06025d
com.tytfizikkamp:dimen/mtrl_btn_dialog_btn_min_width = 0x7f06025b
com.tytfizikkamp:dimen/m3_carousel_small_item_size_min = 0x7f0600f3
com.tytfizikkamp:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f060257
com.tytfizikkamp:color/material_dynamic_neutral_variant95 = 0x7f05023b
com.tytfizikkamp:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f06024f
com.tytfizikkamp:styleable/State = 0x7f120080
com.tytfizikkamp:style/TextAppearance.Design.Error = 0x7f1101d5
com.tytfizikkamp:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f06024d
com.tytfizikkamp:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f110053
com.tytfizikkamp:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0c007a
com.tytfizikkamp:id/accessibility_custom_action_19 = 0x7f08001e
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Headline3 = 0x7f110209
com.tytfizikkamp:drawable/m3_avd_hide_password = 0x7f07009b
com.tytfizikkamp:dimen/mtrl_alert_dialog_background_inset_start = 0x7f060249
com.tytfizikkamp:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f060247
com.tytfizikkamp:dimen/m3_chip_elevated_elevation = 0x7f0600f8
com.tytfizikkamp:drawable/abc_edit_text_material = 0x7f07003c
com.tytfizikkamp:style/Widget.AppCompat.ActionBar.Solid = 0x7f1102fc
com.tytfizikkamp:dimen/material_time_picker_minimum_screen_height = 0x7f060245
com.tytfizikkamp:style/Base.ThemeOverlay.AppCompat.Light = 0x7f110081
com.tytfizikkamp:color/m3_dynamic_primary_text_disable_only = 0x7f050086
com.tytfizikkamp:dimen/material_textinput_min_width = 0x7f060244
com.tytfizikkamp:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f06023f
com.tytfizikkamp:layout/design_navigation_menu_item = 0x7f0b002b
com.tytfizikkamp:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f06023a
com.tytfizikkamp:layout/abc_search_view = 0x7f0b0019
com.tytfizikkamp:dimen/material_emphasis_medium = 0x7f060237
com.tytfizikkamp:id/accessibility_order_parent = 0x7f080039
com.tytfizikkamp:dimen/material_divider_thickness = 0x7f060233
com.tytfizikkamp:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1100b5
com.tytfizikkamp:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0c008d
com.tytfizikkamp:layout/select_dialog_item_material = 0x7f0b006f
com.tytfizikkamp:dimen/material_cursor_width = 0x7f060232
com.tytfizikkamp:attr/colorOnPrimaryFixedVariant = 0x7f0300f6
com.tytfizikkamp:color/material_dynamic_neutral70 = 0x7f05022b
com.tytfizikkamp:dimen/notification_action_icon_size = 0x7f06030d
com.tytfizikkamp:dimen/material_clock_size = 0x7f060230
com.tytfizikkamp:dimen/material_clock_period_toggle_vertical_gap = 0x7f06022e
com.tytfizikkamp:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f0700d0
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1103c8
com.tytfizikkamp:attr/materialAlertDialogTheme = 0x7f0302ba
com.tytfizikkamp:dimen/material_clock_period_toggle_horizontal_gap = 0x7f06022d
com.tytfizikkamp:id/home = 0x7f0800d4
com.tytfizikkamp:color/m3_dark_default_color_primary_text = 0x7f050076
com.tytfizikkamp:dimen/material_clock_face_margin_bottom = 0x7f060226
com.tytfizikkamp:dimen/mtrl_progress_track_thickness = 0x7f0602e2
com.tytfizikkamp:string/scrollbar_description = 0x7f1000cc
com.tytfizikkamp:dimen/material_bottom_sheet_max_width = 0x7f060222
com.tytfizikkamp:dimen/tooltip_y_offset_touch = 0x7f060323
com.tytfizikkamp:string/mtrl_picker_a11y_next_month = 0x7f100094
com.tytfizikkamp:dimen/m3_toolbar_text_size_title = 0x7f060221
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f1102e0
com.tytfizikkamp:string/mtrl_picker_range_header_only_end_selected = 0x7f1000a7
com.tytfizikkamp:dimen/m3_timepicker_window_elevation = 0x7f060220
com.tytfizikkamp:attr/cardForegroundColor = 0x7f03009f
com.tytfizikkamp:color/m3_ref_palette_neutral40 = 0x7f05010d
com.tytfizikkamp:dimen/m3_timepicker_display_stroke_width = 0x7f06021f
com.tytfizikkamp:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f06021e
com.tytfizikkamp:anim/m3_side_sheet_exit_to_left = 0x7f01002d
com.tytfizikkamp:anim/abc_slide_out_bottom = 0x7f010008
com.tytfizikkamp:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f060140
com.tytfizikkamp:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f06021d
com.tytfizikkamp:id/pin = 0x7f080159
com.tytfizikkamp:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f06021b
com.tytfizikkamp:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f06021a
com.tytfizikkamp:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f060218
com.tytfizikkamp:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f060214
com.tytfizikkamp:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f06020c
com.tytfizikkamp:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f06020a
com.tytfizikkamp:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f110431
com.tytfizikkamp:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070040
com.tytfizikkamp:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f060207
com.tytfizikkamp:id/accessibility_custom_action_27 = 0x7f080027
com.tytfizikkamp:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f060204
com.tytfizikkamp:attr/contentPaddingRight = 0x7f030134
com.tytfizikkamp:color/m3_popupmenu_overlay_color = 0x7f05009a
com.tytfizikkamp:id/accessibility_role = 0x7f08003a
com.tytfizikkamp:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f0601ff
com.tytfizikkamp:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f0601fd
com.tytfizikkamp:styleable/Carousel = 0x7f12001c
com.tytfizikkamp:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f0601fb
com.tytfizikkamp:dimen/design_bottom_navigation_elevation = 0x7f060063
com.tytfizikkamp:dimen/m3_sys_elevation_level5 = 0x7f0601f6
com.tytfizikkamp:style/Widget.Material3.Slider.Legacy.Label = 0x7f1103eb
com.tytfizikkamp:dimen/m3_sys_elevation_level3 = 0x7f0601f4
com.tytfizikkamp:styleable/MaterialToolbar = 0x7f12005d
com.tytfizikkamp:style/Widget.Material3.NavigationRailView = 0x7f1103d5
com.tytfizikkamp:attr/fabAnimationMode = 0x7f0301bc
com.tytfizikkamp:dimen/m3_sys_elevation_level0 = 0x7f0601f1
com.tytfizikkamp:attr/startIconTint = 0x7f0303cb
com.tytfizikkamp:dimen/m3_snackbar_action_text_color_alpha = 0x7f0601ef
com.tytfizikkamp:id/accessibility_custom_action_21 = 0x7f080021
com.tytfizikkamp:color/material_dynamic_secondary60 = 0x7f050251
com.tytfizikkamp:color/m3_ref_palette_secondary50 = 0x7f05013b
com.tytfizikkamp:dimen/m3_small_fab_size = 0x7f0601ee
com.tytfizikkamp:attr/useMaterialThemeColors = 0x7f030495
com.tytfizikkamp:dimen/m3_side_sheet_width = 0x7f0601e9
com.tytfizikkamp:dimen/m3_side_sheet_modal_elevation = 0x7f0601e7
com.tytfizikkamp:dimen/m3_searchbar_text_size = 0x7f0601e2
com.tytfizikkamp:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f11004a
com.tytfizikkamp:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
com.tytfizikkamp:dimen/m3_searchbar_margin_horizontal = 0x7f0601dd
com.tytfizikkamp:attr/state_with_icon = 0x7f0303d5
com.tytfizikkamp:color/m3_sys_color_dark_primary_container = 0x7f05016f
com.tytfizikkamp:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0601da
com.tytfizikkamp:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f11005a
com.tytfizikkamp:dimen/m3_ripple_hovered_alpha = 0x7f0601d8
com.tytfizikkamp:dimen/m3_ripple_focused_alpha = 0x7f0601d7
com.tytfizikkamp:attr/colorAccent = 0x7f0300e5
com.tytfizikkamp:dimen/m3_ripple_default_alpha = 0x7f0601d6
com.tytfizikkamp:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f06020e
com.tytfizikkamp:color/material_personalized_color_surface_variant = 0x7f0502a1
com.tytfizikkamp:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0601d4
com.tytfizikkamp:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0601d2
com.tytfizikkamp:color/m3_sys_color_light_tertiary = 0x7f0501ed
com.tytfizikkamp:drawable/abc_item_background_holo_dark = 0x7f07004a
com.tytfizikkamp:attr/simpleItemSelectedRippleColor = 0x7f0303b4
com.tytfizikkamp:dimen/appcompat_dialog_background_inset = 0x7f060051
com.tytfizikkamp:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0601d1
com.tytfizikkamp:dimen/m3_navigation_rail_item_min_height = 0x7f0601d0
com.tytfizikkamp:attr/closeIconStartPadding = 0x7f0300d5
com.tytfizikkamp:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0601cf
com.tytfizikkamp:attr/materialCalendarYearNavigationButton = 0x7f0302cf
com.tytfizikkamp:attr/errorContentDescription = 0x7f03019e
com.tytfizikkamp:dimen/m3_navigation_rail_elevation = 0x7f0601cb
com.tytfizikkamp:style/Platform.V21.AppCompat.Light = 0x7f110148
com.tytfizikkamp:dimen/m3_navigation_rail_default_width = 0x7f0601ca
com.tytfizikkamp:color/switch_thumb_normal_material_light = 0x7f050304
com.tytfizikkamp:dimen/m3_navigation_item_vertical_padding = 0x7f0601c7
com.tytfizikkamp:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f060254
com.tytfizikkamp:color/m3_checkbox_button_tint = 0x7f050070
com.tytfizikkamp:dimen/m3_navigation_item_shape_inset_top = 0x7f0601c6
com.tytfizikkamp:dimen/m3_navigation_item_shape_inset_start = 0x7f0601c5
com.tytfizikkamp:dimen/m3_navigation_item_shape_inset_end = 0x7f0601c4
com.tytfizikkamp:attr/wavePeriod = 0x7f0304a0
com.tytfizikkamp:drawable/abc_cab_background_top_material = 0x7f070038
com.tytfizikkamp:dimen/m3_navigation_item_icon_padding = 0x7f0601c2
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f050198
com.tytfizikkamp:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
com.tytfizikkamp:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0601bf
com.tytfizikkamp:dimen/m3_menu_elevation = 0x7f0601bd
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f11043a
com.tytfizikkamp:drawable/tooltip_frame_light = 0x7f0700f0
com.tytfizikkamp:dimen/m3_chip_disabled_translation_z = 0x7f0600f6
com.tytfizikkamp:dimen/m3_fab_translation_z_pressed = 0x7f0601b9
com.tytfizikkamp:styleable/Variant = 0x7f120091
com.tytfizikkamp:dimen/mtrl_btn_padding_top = 0x7f060269
com.tytfizikkamp:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f070010
com.tytfizikkamp:color/design_error = 0x7f05004d
com.tytfizikkamp:dimen/m3_fab_translation_z_hovered_focused = 0x7f0601b8
com.tytfizikkamp:dimen/m3_fab_border_width = 0x7f0601b6
com.tytfizikkamp:attr/drawableLeftCompat = 0x7f030179
com.tytfizikkamp:dimen/m3_extended_fab_min_height = 0x7f0601b3
com.tytfizikkamp:string/mtrl_picker_text_input_date_range_end_hint = 0x7f1000af
com.tytfizikkamp:dimen/m3_extended_fab_bottom_padding = 0x7f0601b0
com.tytfizikkamp:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0c0042
com.tytfizikkamp:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0602d6
com.tytfizikkamp:string/material_slider_range_start = 0x7f100079
com.tytfizikkamp:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0601ad
com.tytfizikkamp:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0601ac
com.tytfizikkamp:attr/closeIconVisible = 0x7f0300d7
com.tytfizikkamp:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0601ab
com.tytfizikkamp:drawable/abc_textfield_search_material = 0x7f070075
com.tytfizikkamp:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0601a8
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f11006f
com.tytfizikkamp:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0601a7
com.tytfizikkamp:layout/mtrl_calendar_month_labeled = 0x7f0b0051
com.tytfizikkamp:attr/textAllCaps = 0x7f030410
com.tytfizikkamp:attr/track = 0x7f03047c
com.tytfizikkamp:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f0601a6
com.tytfizikkamp:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f0601a5
com.tytfizikkamp:style/Widget.MaterialComponents.BottomNavigationView = 0x7f110412
com.tytfizikkamp:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f0601a4
com.tytfizikkamp:attr/behavior_halfExpandedRatio = 0x7f030071
com.tytfizikkamp:dimen/abc_text_size_menu_header_material = 0x7f06004a
com.tytfizikkamp:drawable/mtrl_ic_checkbox_checked = 0x7f0700c1
com.tytfizikkamp:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f0601a2
com.tytfizikkamp:attr/materialCardViewOutlinedStyle = 0x7f0302d2
com.tytfizikkamp:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f0601a0
com.tytfizikkamp:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0601a9
com.tytfizikkamp:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f06019f
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1103c1
com.tytfizikkamp:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f06019e
com.tytfizikkamp:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f06019d
com.tytfizikkamp:dimen/mtrl_extended_fab_bottom_padding = 0x7f0602a7
com.tytfizikkamp:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f06019a
com.tytfizikkamp:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f060193
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant92 = 0x7f0500c9
com.tytfizikkamp:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f0601a3
com.tytfizikkamp:id/bottom = 0x7f080069
com.tytfizikkamp:attr/initialActivityCount = 0x7f030229
com.tytfizikkamp:dimen/m3_comp_search_bar_avatar_size = 0x7f06016f
com.tytfizikkamp:dimen/m3_comp_switch_disabled_track_opacity = 0x7f060192
com.tytfizikkamp:attr/fadeDuration = 0x7f0301c2
com.tytfizikkamp:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f060190
com.tytfizikkamp:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f06018e
com.tytfizikkamp:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f060187
com.tytfizikkamp:id/tag_on_receive_content_mime_types = 0x7f0801b5
com.tytfizikkamp:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0600b1
com.tytfizikkamp:color/material_dynamic_neutral_variant99 = 0x7f05023c
com.tytfizikkamp:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f060186
com.tytfizikkamp:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0c009d
com.tytfizikkamp:anim/rns_slide_out_to_bottom = 0x7f010049
com.tytfizikkamp:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0600bf
com.tytfizikkamp:drawable/abc_switch_thumb_material = 0x7f070069
com.tytfizikkamp:dimen/mtrl_calendar_month_vertical_padding = 0x7f06028b
com.tytfizikkamp:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f110130
com.tytfizikkamp:dimen/m3_comp_slider_active_handle_width = 0x7f060184
com.tytfizikkamp:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f060304
com.tytfizikkamp:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f060181
com.tytfizikkamp:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0c0098
com.tytfizikkamp:attr/colorSurfaceBright = 0x7f030113
com.tytfizikkamp:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f060180
com.tytfizikkamp:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f060211
com.tytfizikkamp:color/m3_sys_color_light_error = 0x7f0501ce
com.tytfizikkamp:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f06014e
com.tytfizikkamp:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f06017e
com.tytfizikkamp:attr/collapsingToolbarLayoutStyle = 0x7f0300e3
com.tytfizikkamp:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f06017d
com.tytfizikkamp:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f06017b
com.tytfizikkamp:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f06017a
com.tytfizikkamp:styleable/TextAppearance = 0x7f120089
com.tytfizikkamp:attr/triggerReceiver = 0x7f030490
com.tytfizikkamp:attr/shapeAppearanceCornerMedium = 0x7f03039d
com.tytfizikkamp:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f060176
com.tytfizikkamp:anim/fragment_fast_out_extra_slow_in = 0x7f010022
com.tytfizikkamp:dimen/m3_comp_search_view_docked_header_container_height = 0x7f060175
com.tytfizikkamp:drawable/mtrl_ic_indeterminate = 0x7f0700c4
com.tytfizikkamp:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f060173
com.tytfizikkamp:dimen/m3_comp_search_bar_container_height = 0x7f060171
com.tytfizikkamp:dimen/m3_comp_search_bar_container_elevation = 0x7f060170
com.tytfizikkamp:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f1103a5
com.tytfizikkamp:id/design_menu_item_action_area = 0x7f080094
com.tytfizikkamp:id/default_activity_button = 0x7f080091
com.tytfizikkamp:dimen/m3_comp_scrim_container_opacity = 0x7f06016e
com.tytfizikkamp:attr/expandedTitleTextColor = 0x7f0301b0
com.tytfizikkamp:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f06016b
com.tytfizikkamp:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f060256
com.tytfizikkamp:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f060168
com.tytfizikkamp:style/Base.V14.Theme.MaterialComponents = 0x7f110094
com.tytfizikkamp:dimen/abc_switch_padding = 0x7f06003e
com.tytfizikkamp:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f070026
com.tytfizikkamp:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f060167
com.tytfizikkamp:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f060161
com.tytfizikkamp:macro/m3_comp_filled_button_container_color = 0x7f0c0043
com.tytfizikkamp:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f06015b
com.tytfizikkamp:dimen/m3_comp_outlined_text_field_outline_width = 0x7f06015a
com.tytfizikkamp:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f0601f9
com.tytfizikkamp:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f060156
com.tytfizikkamp:style/Widget.Design.CollapsingToolbar = 0x7f11034f
com.tytfizikkamp:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f060155
com.tytfizikkamp:dimen/m3_comp_outlined_card_icon_size = 0x7f060153
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_outline = 0x7f05018e
com.tytfizikkamp:dimen/m3_comp_outlined_card_container_elevation = 0x7f060151
com.tytfizikkamp:dimen/mtrl_btn_corner_radius = 0x7f06025a
com.tytfizikkamp:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f06014f
com.tytfizikkamp:string/state_mixed_description = 0x7f1000d7
com.tytfizikkamp:dimen/mtrl_calendar_header_content_padding = 0x7f06027f
com.tytfizikkamp:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f06014d
com.tytfizikkamp:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f0502a8
com.tytfizikkamp:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f06014b
com.tytfizikkamp:dimen/design_bottom_sheet_elevation = 0x7f06006c
com.tytfizikkamp:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f060147
com.tytfizikkamp:dimen/m3_comp_progress_indicator_active_indicator_track_space = 0x7f060163
com.tytfizikkamp:color/m3_ref_palette_tertiary40 = 0x7f050147
com.tytfizikkamp:dimen/mtrl_calendar_days_of_week_height = 0x7f06027d
com.tytfizikkamp:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f060145
com.tytfizikkamp:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f060143
com.tytfizikkamp:dimen/m3_comp_navigation_drawer_icon_size = 0x7f060142
com.tytfizikkamp:styleable/AppCompatSeekBar = 0x7f12000f
com.tytfizikkamp:attr/tabGravity = 0x7f0303f1
com.tytfizikkamp:drawable/$avd_hide_password__1 = 0x7f070001
com.tytfizikkamp:anim/m3_motion_fade_exit = 0x7f01002a
com.tytfizikkamp:attr/hintTextColor = 0x7f030211
com.tytfizikkamp:attr/textAppearanceTitleSmall = 0x7f030435
com.tytfizikkamp:dimen/mtrl_fab_min_touch_target = 0x7f0602b8
com.tytfizikkamp:dimen/m3_comp_navigation_bar_icon_size = 0x7f06013d
com.tytfizikkamp:styleable/StateListDrawableItem = 0x7f120082
com.tytfizikkamp:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f06013b
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f1101e1
com.tytfizikkamp:drawable/mtrl_tabs_default_indicator = 0x7f0700d4
com.tytfizikkamp:string/catalyst_perf_monitor = 0x7f100042
com.tytfizikkamp:attr/fastScrollVerticalThumbDrawable = 0x7f0301c8
com.tytfizikkamp:id/accessibility_state_expanded = 0x7f08003c
com.tytfizikkamp:dimen/m3_comp_navigation_bar_container_height = 0x7f06013a
com.tytfizikkamp:dimen/m3_comp_navigation_bar_container_elevation = 0x7f060139
com.tytfizikkamp:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f110465
com.tytfizikkamp:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0c0082
com.tytfizikkamp:dimen/m3_comp_menu_container_elevation = 0x7f060136
com.tytfizikkamp:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f060135
com.tytfizikkamp:dimen/m3_chip_icon_size = 0x7f0600fa
com.tytfizikkamp:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f060133
com.tytfizikkamp:dimen/m3_comp_input_chip_container_elevation = 0x7f060131
com.tytfizikkamp:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f070017
com.tytfizikkamp:color/m3_ref_palette_primary40 = 0x7f05012d
com.tytfizikkamp:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f060130
com.tytfizikkamp:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f06012f
com.tytfizikkamp:layout/design_bottom_sheet_dialog = 0x7f0b0020
com.tytfizikkamp:attr/actionBarSplitStyle = 0x7f030004
com.tytfizikkamp:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
com.tytfizikkamp:dimen/mtrl_btn_padding_right = 0x7f060268
com.tytfizikkamp:color/m3_sys_color_tertiary_fixed = 0x7f0501f9
com.tytfizikkamp:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f06012e
com.tytfizikkamp:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0c0153
com.tytfizikkamp:color/design_default_color_on_error = 0x7f050043
com.tytfizikkamp:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f06012b
com.tytfizikkamp:dimen/m3_comp_filled_card_icon_size = 0x7f060129
com.tytfizikkamp:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f060127
com.tytfizikkamp:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f060213
com.tytfizikkamp:attr/contrast = 0x7f030138
com.tytfizikkamp:attr/windowActionBar = 0x7f0304a3
com.tytfizikkamp:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f060126
com.tytfizikkamp:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f11045f
com.tytfizikkamp:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f11035b
com.tytfizikkamp:color/material_grey_900 = 0x7f05026a
com.tytfizikkamp:attr/roundBottomEnd = 0x7f03037e
com.tytfizikkamp:dimen/m3_comp_filled_card_container_elevation = 0x7f060125
com.tytfizikkamp:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f060124
com.tytfizikkamp:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f11018c
com.tytfizikkamp:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f0700d1
com.tytfizikkamp:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f060122
com.tytfizikkamp:style/Base.ThemeOverlay.AppCompat = 0x7f11007b
com.tytfizikkamp:dimen/m3_comp_fab_primary_small_icon_size = 0x7f060121
com.tytfizikkamp:style/Widget.MaterialComponents.NavigationRailView = 0x7f110451
com.tytfizikkamp:attr/materialDividerStyle = 0x7f0302d8
com.tytfizikkamp:dimen/m3_comp_fab_primary_small_container_height = 0x7f060120
com.tytfizikkamp:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f06011f
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f1102ea
com.tytfizikkamp:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f06011e
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1103bd
com.tytfizikkamp:dimen/m3_comp_fab_primary_large_icon_size = 0x7f06011d
com.tytfizikkamp:drawable/$avd_show_password__1 = 0x7f070004
com.tytfizikkamp:dimen/m3_comp_fab_primary_large_container_height = 0x7f06011c
com.tytfizikkamp:style/Base.V14.Theme.MaterialComponents.Light = 0x7f110098
com.tytfizikkamp:attr/showAnimationBehavior = 0x7f0303a6
com.tytfizikkamp:color/ripple_material_dark = 0x7f0502f9
com.tytfizikkamp:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f060118
com.tytfizikkamp:dimen/m3_comp_fab_primary_container_height = 0x7f060117
com.tytfizikkamp:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f060111
com.tytfizikkamp:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f110091
com.tytfizikkamp:color/m3_sys_color_dark_surface_container = 0x7f050174
com.tytfizikkamp:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f06010f
com.tytfizikkamp:id/radio = 0x7f080160
com.tytfizikkamp:dimen/m3_comp_extended_fab_primary_container_height = 0x7f06010e
com.tytfizikkamp:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f06010d
com.tytfizikkamp:string/abc_menu_delete_shortcut_label = 0x7f10000a
com.tytfizikkamp:dimen/m3_comp_elevated_card_icon_size = 0x7f06010c
com.tytfizikkamp:color/m3_ref_palette_error95 = 0x7f050101
com.tytfizikkamp:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f060104
com.tytfizikkamp:dimen/design_navigation_padding_bottom = 0x7f06007d
com.tytfizikkamp:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f060102
com.tytfizikkamp:drawable/mtrl_checkbox_button_icon = 0x7f0700b3
com.tytfizikkamp:styleable/MaterialButton = 0x7f12004f
com.tytfizikkamp:string/rn_tab_description = 0x7f1000cb
com.tytfizikkamp:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f06023c
com.tytfizikkamp:dimen/m3_comp_badge_large_size = 0x7f060100
com.tytfizikkamp:id/accessibility_custom_action_20 = 0x7f080020
com.tytfizikkamp:string/mtrl_switch_track_path = 0x7f1000c0
com.tytfizikkamp:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f0600ff
com.tytfizikkamp:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f0600fd
com.tytfizikkamp:dimen/m3_comp_assist_chip_container_height = 0x7f0600fb
com.tytfizikkamp:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f110432
com.tytfizikkamp:id/action_context_bar = 0x7f080046
com.tytfizikkamp:dimen/m3_chip_corner_size = 0x7f0600f5
com.tytfizikkamp:id/mtrl_picker_header_selection_text = 0x7f080124
com.tytfizikkamp:dimen/m3_searchview_elevation = 0x7f0601e4
com.tytfizikkamp:color/abc_decor_view_status_guard = 0x7f050005
com.tytfizikkamp:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f060152
com.tytfizikkamp:string/error_icon_content_description = 0x7f100051
com.tytfizikkamp:color/m3_ref_palette_dynamic_tertiary60 = 0x7f0500f0
com.tytfizikkamp:dimen/m3_chip_checked_hovered_translation_z = 0x7f0600f4
com.tytfizikkamp:dimen/m3_carousel_small_item_size_max = 0x7f0600f2
com.tytfizikkamp:animator/m3_extended_fab_state_list_animator = 0x7f020014
com.tytfizikkamp:dimen/m3_carousel_gone_size = 0x7f0600f0
com.tytfizikkamp:style/Widget.Material3.FloatingActionButton.Primary = 0x7f1103a7
com.tytfizikkamp:dimen/m3_carousel_extra_small_item_size = 0x7f0600ef
com.tytfizikkamp:dimen/m3_carousel_debug_keyline_width = 0x7f0600ee
com.tytfizikkamp:attr/colorOnPrimary = 0x7f0300f3
com.tytfizikkamp:dimen/m3_card_elevation = 0x7f0600eb
com.tytfizikkamp:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f110096
com.tytfizikkamp:drawable/abc_control_background_material = 0x7f07003a
com.tytfizikkamp:dimen/m3_card_elevated_disabled_z = 0x7f0600e7
com.tytfizikkamp:style/Widget.AppCompat.ActionMode = 0x7f110303
com.tytfizikkamp:attr/autoSizeMaxTextSize = 0x7f03003f
com.tytfizikkamp:dimen/m3_btn_translation_z_hovered = 0x7f0600e4
com.tytfizikkamp:dimen/m3_btn_translation_z_base = 0x7f0600e3
com.tytfizikkamp:dimen/m3_btn_text_btn_padding_right = 0x7f0600e2
com.tytfizikkamp:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0600e0
com.tytfizikkamp:attr/errorIconTintMode = 0x7f0301a2
com.tytfizikkamp:dimen/m3_btn_stroke_size = 0x7f0600de
com.tytfizikkamp:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f06016c
com.tytfizikkamp:dimen/m3_btn_padding_top = 0x7f0600dd
com.tytfizikkamp:macro/m3_comp_filled_text_field_container_color = 0x7f0c004b
com.tytfizikkamp:dimen/m3_btn_padding_right = 0x7f0600dc
com.tytfizikkamp:drawable/abc_list_selector_background_transition_holo_dark = 0x7f070052
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f11003b
com.tytfizikkamp:id/material_clock_period_am_button = 0x7f0800fa
com.tytfizikkamp:dimen/m3_btn_padding_bottom = 0x7f0600da
com.tytfizikkamp:style/Base.Theme.Material3.Dark.Dialog = 0x7f11005e
com.tytfizikkamp:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
com.tytfizikkamp:dimen/m3_btn_max_width = 0x7f0600d9
com.tytfizikkamp:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0c0152
com.tytfizikkamp:attr/layout_constraintCircleRadius = 0x7f03026a
com.tytfizikkamp:dimen/m3_btn_icon_only_default_padding = 0x7f0600d4
com.tytfizikkamp:anim/rns_slide_in_from_right = 0x7f010048
com.tytfizikkamp:attr/roundPercent = 0x7f030382
com.tytfizikkamp:drawable/abc_list_selector_disabled_holo_light = 0x7f070055
com.tytfizikkamp:macro/m3_comp_top_app_bar_small_on_scroll_container_color = 0x7f0c0173
com.tytfizikkamp:attr/colorSurfaceDim = 0x7f030119
com.tytfizikkamp:attr/chipSpacingVertical = 0x7f0300c3
com.tytfizikkamp:attr/editTextBackground = 0x7f030188
com.tytfizikkamp:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0602de
com.tytfizikkamp:dimen/m3_btn_icon_btn_padding_left = 0x7f0600d2
com.tytfizikkamp:dimen/m3_btn_elevated_btn_elevation = 0x7f0600d0
com.tytfizikkamp:style/Animation.Material3.SideSheetDialog = 0x7f110009
com.tytfizikkamp:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f060208
com.tytfizikkamp:integer/m3_sys_shape_corner_full_corner_family = 0x7f090022
com.tytfizikkamp:attr/counterOverflowTextAppearance = 0x7f030149
com.tytfizikkamp:attr/suffixText = 0x7f0303e6
com.tytfizikkamp:color/material_on_background_emphasis_high_type = 0x7f050270
com.tytfizikkamp:dimen/m3_btn_disabled_elevation = 0x7f0600ce
com.tytfizikkamp:attr/chipIconSize = 0x7f0300bc
com.tytfizikkamp:dimen/m3_btn_dialog_btn_spacing = 0x7f0600cd
com.tytfizikkamp:dimen/m3_comp_navigation_rail_container_width = 0x7f060149
com.tytfizikkamp:dimen/design_fab_translation_z_hovered_focused = 0x7f060074
com.tytfizikkamp:attr/textAppearanceSmallPopupMenu = 0x7f030430
com.tytfizikkamp:dimen/m3_btn_dialog_btn_min_width = 0x7f0600cc
com.tytfizikkamp:dimen/m3_bottomappbar_height = 0x7f0600ca
com.tytfizikkamp:dimen/m3_bottomappbar_fab_end_margin = 0x7f0600c9
com.tytfizikkamp:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f110106
com.tytfizikkamp:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0c00ac
com.tytfizikkamp:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0600c3
com.tytfizikkamp:dimen/m3_bottom_nav_min_height = 0x7f0600c2
com.tytfizikkamp:color/m3_sys_color_dark_on_surface_variant = 0x7f050169
com.tytfizikkamp:dimen/m3_bottom_nav_item_padding_top = 0x7f0600c1
com.tytfizikkamp:dimen/m3_badge_with_text_vertical_padding = 0x7f0600bc
com.tytfizikkamp:dimen/m3_badge_with_text_vertical_offset = 0x7f0600bb
com.tytfizikkamp:style/Base.Theme.MaterialComponents = 0x7f110068
com.tytfizikkamp:dimen/m3_badge_with_text_size = 0x7f0600ba
com.tytfizikkamp:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0600b0
com.tytfizikkamp:dimen/m3_appbar_size_medium = 0x7f0600ac
com.tytfizikkamp:style/Theme.Material3.Dark = 0x7f110239
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f110162
com.tytfizikkamp:style/Platform.AppCompat.Light = 0x7f11013f
com.tytfizikkamp:dimen/m3_appbar_size_large = 0x7f0600ab
com.tytfizikkamp:macro/m3_comp_time_picker_clock_dial_color = 0x7f0c014c
com.tytfizikkamp:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
com.tytfizikkamp:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0600a8
com.tytfizikkamp:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0c001b
com.tytfizikkamp:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0600a5
com.tytfizikkamp:style/Theme.Material3.Dark.NoActionBar = 0x7f11023f
com.tytfizikkamp:dimen/mtrl_navigation_rail_active_text_size = 0x7f0602cc
com.tytfizikkamp:dimen/m3_alert_dialog_action_top_padding = 0x7f06009f
com.tytfizikkamp:dimen/item_touch_helper_swipe_escape_velocity = 0x7f06009d
com.tytfizikkamp:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0c016d
com.tytfizikkamp:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f060301
com.tytfizikkamp:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f06009c
com.tytfizikkamp:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f06009b
com.tytfizikkamp:dimen/m3_extended_fab_start_padding = 0x7f0601b4
com.tytfizikkamp:dimen/highlight_alpha_material_light = 0x7f060096
com.tytfizikkamp:dimen/highlight_alpha_material_colored = 0x7f060094
com.tytfizikkamp:dimen/fastscroll_margin = 0x7f060092
com.tytfizikkamp:dimen/disabled_alpha_material_light = 0x7f060090
com.tytfizikkamp:attr/buttonBarStyle = 0x7f030090
com.tytfizikkamp:dimen/disabled_alpha_material_dark = 0x7f06008f
com.tytfizikkamp:dimen/design_textinput_caption_translate_y = 0x7f06008e
com.tytfizikkamp:style/Animation.Material3.SideSheetDialog.Left = 0x7f11000a
com.tytfizikkamp:attr/cornerSizeTopLeft = 0x7f030145
com.tytfizikkamp:dimen/notification_content_margin_start = 0x7f060310
com.tytfizikkamp:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f060113
com.tytfizikkamp:dimen/design_tab_text_size_2line = 0x7f06008d
com.tytfizikkamp:attr/closeIcon = 0x7f0300d1
com.tytfizikkamp:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0601aa
com.tytfizikkamp:styleable/MaterialCalendar = 0x7f120051
com.tytfizikkamp:dimen/design_tab_max_width = 0x7f06008a
com.tytfizikkamp:dimen/design_snackbar_text_size = 0x7f060089
com.tytfizikkamp:dimen/design_snackbar_padding_vertical_2lines = 0x7f060088
com.tytfizikkamp:string/image_description = 0x7f100058
com.tytfizikkamp:dimen/design_snackbar_max_width = 0x7f060084
com.tytfizikkamp:id/search_plate = 0x7f080187
com.tytfizikkamp:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0601c9
com.tytfizikkamp:dimen/design_snackbar_extra_spacing_horizontal = 0x7f060083
com.tytfizikkamp:anim/mtrl_bottom_sheet_slide_out = 0x7f010030
com.tytfizikkamp:drawable/ic_mtrl_chip_close_circle = 0x7f070097
com.tytfizikkamp:attr/textAppearanceHeadline5 = 0x7f03041f
com.tytfizikkamp:dimen/design_snackbar_elevation = 0x7f060082
com.tytfizikkamp:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0c0104
com.tytfizikkamp:id/material_clock_display_and_toggle = 0x7f0800f6
com.tytfizikkamp:dimen/mtrl_card_checked_icon_size = 0x7f06029d
com.tytfizikkamp:dimen/design_snackbar_background_corner_radius = 0x7f060081
com.tytfizikkamp:attr/layout_constraintHeight_max = 0x7f030272
com.tytfizikkamp:dimen/design_snackbar_action_inline_max_width = 0x7f06007f
com.tytfizikkamp:dimen/design_navigation_icon_padding = 0x7f060077
com.tytfizikkamp:dimen/design_navigation_elevation = 0x7f060076
com.tytfizikkamp:dimen/m3_card_hovered_z = 0x7f0600ec
com.tytfizikkamp:dimen/design_fab_translation_z_pressed = 0x7f060075
com.tytfizikkamp:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f110378
com.tytfizikkamp:attr/textAppearanceSubtitle1 = 0x7f030431
com.tytfizikkamp:dimen/design_fab_size_normal = 0x7f060073
com.tytfizikkamp:dimen/design_fab_elevation = 0x7f060070
com.tytfizikkamp:attr/tooltipForegroundColor = 0x7f030474
com.tytfizikkamp:attr/motionEasingLinear = 0x7f030317
com.tytfizikkamp:dimen/design_fab_border_width = 0x7f06006f
com.tytfizikkamp:dimen/design_bottom_sheet_peek_height_min = 0x7f06006e
com.tytfizikkamp:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f060280
com.tytfizikkamp:dimen/mtrl_calendar_year_width = 0x7f06029b
com.tytfizikkamp:dimen/design_bottom_navigation_shadow_height = 0x7f06006a
com.tytfizikkamp:dimen/design_bottom_navigation_item_min_width = 0x7f060067
com.tytfizikkamp:dimen/design_bottom_navigation_icon_size = 0x7f060065
com.tytfizikkamp:dimen/design_bottom_navigation_height = 0x7f060064
com.tytfizikkamp:dimen/design_bottom_navigation_margin = 0x7f060069
com.tytfizikkamp:color/mtrl_btn_ripple_color = 0x7f0502b9
com.tytfizikkamp:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f060144
com.tytfizikkamp:drawable/abc_list_divider_mtrl_alpha = 0x7f07004d
com.tytfizikkamp:dimen/design_bottom_navigation_active_item_max_width = 0x7f060060
com.tytfizikkamp:style/Base.V24.Theme.Material3.Light = 0x7f1100b6
com.tytfizikkamp:dimen/design_appbar_elevation = 0x7f06005f
com.tytfizikkamp:string/state_off_description = 0x7f1000d8
com.tytfizikkamp:dimen/def_drawer_elevation = 0x7f06005e
com.tytfizikkamp:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1102c7
com.tytfizikkamp:dimen/m3_ripple_pressed_alpha = 0x7f0601d9
com.tytfizikkamp:id/open_search_bar_text_view = 0x7f08013d
com.tytfizikkamp:color/material_personalized_color_on_primary = 0x7f050284
com.tytfizikkamp:dimen/compat_notification_large_icon_max_width = 0x7f06005d
com.tytfizikkamp:attr/statusBarForeground = 0x7f0303d7
com.tytfizikkamp:dimen/compat_button_padding_vertical_material = 0x7f06005a
com.tytfizikkamp:color/m3_checkbox_button_icon_tint = 0x7f05006f
com.tytfizikkamp:dimen/compat_button_padding_horizontal_material = 0x7f060059
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f110077
com.tytfizikkamp:dimen/cardview_default_radius = 0x7f060055
com.tytfizikkamp:dimen/cardview_compat_inset_shadow = 0x7f060053
com.tytfizikkamp:id/mtrl_calendar_months = 0x7f08011a
com.tytfizikkamp:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f0700aa
com.tytfizikkamp:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
com.tytfizikkamp:dimen/abc_text_size_title_material_toolbar = 0x7f060050
com.tytfizikkamp:dimen/abc_text_size_title_material = 0x7f06004f
com.tytfizikkamp:id/shortcut = 0x7f08018d
com.tytfizikkamp:attr/mock_showDiagonals = 0x7f0302fe
com.tytfizikkamp:attr/fontProviderFetchTimeout = 0x7f0301f3
com.tytfizikkamp:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
com.tytfizikkamp:dimen/abc_text_size_small_material = 0x7f06004c
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Display1 = 0x7f11001e
com.tytfizikkamp:dimen/abc_text_size_menu_material = 0x7f06004b
com.tytfizikkamp:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0c013e
com.tytfizikkamp:drawable/abc_list_selector_holo_light = 0x7f070057
com.tytfizikkamp:dimen/mtrl_progress_circular_size_extra_small = 0x7f0602db
com.tytfizikkamp:macro/m3_comp_menu_container_color = 0x7f0c005d
com.tytfizikkamp:dimen/abc_text_size_large_material = 0x7f060048
com.tytfizikkamp:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f06012a
com.tytfizikkamp:drawable/mtrl_dropdown_arrow = 0x7f0700bc
com.tytfizikkamp:attr/contentInsetLeft = 0x7f03012c
com.tytfizikkamp:dimen/abc_text_size_display_4_material = 0x7f060046
com.tytfizikkamp:style/Widget.Material3.Button.TextButton.Dialog = 0x7f110374
com.tytfizikkamp:id/FUNCTION = 0x7f080004
com.tytfizikkamp:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0602df
com.tytfizikkamp:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1100da
com.tytfizikkamp:dimen/abc_text_size_display_1_material = 0x7f060043
com.tytfizikkamp:dimen/abc_text_size_button_material = 0x7f060041
com.tytfizikkamp:id/search_close_btn = 0x7f080183
com.tytfizikkamp:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f060119
com.tytfizikkamp:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
com.tytfizikkamp:dimen/abc_star_small = 0x7f06003d
com.tytfizikkamp:dimen/abc_star_medium = 0x7f06003c
com.tytfizikkamp:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f060177
com.tytfizikkamp:string/material_motion_easing_decelerated = 0x7f100074
com.tytfizikkamp:dimen/abc_star_big = 0x7f06003b
com.tytfizikkamp:dimen/abc_seekbar_track_background_height_material = 0x7f060038
com.tytfizikkamp:dimen/abc_search_view_preferred_height = 0x7f060036
com.tytfizikkamp:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
com.tytfizikkamp:dimen/abc_progress_bar_height_material = 0x7f060035
com.tytfizikkamp:attr/indicatorInset = 0x7f030226
com.tytfizikkamp:dimen/abc_panel_menu_list_width = 0x7f060034
com.tytfizikkamp:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1100eb
com.tytfizikkamp:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
com.tytfizikkamp:string/mtrl_switch_thumb_group_name = 0x7f1000b9
com.tytfizikkamp:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f060291
com.tytfizikkamp:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0c006f
com.tytfizikkamp:id/tag_window_insets_animation_callback = 0x7f0801bc
com.tytfizikkamp:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f0601fc
com.tytfizikkamp:dimen/material_clock_hand_padding = 0x7f060229
com.tytfizikkamp:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
com.tytfizikkamp:dimen/abc_list_item_height_large_material = 0x7f060030
com.tytfizikkamp:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
com.tytfizikkamp:id/submenuarrow = 0x7f0801ab
com.tytfizikkamp:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
com.tytfizikkamp:style/Widget.MaterialComponents.TimePicker.Display = 0x7f110474
com.tytfizikkamp:attr/tabTextColor = 0x7f03040a
com.tytfizikkamp:dimen/mtrl_calendar_header_text_padding = 0x7f060285
com.tytfizikkamp:dimen/mtrl_calendar_navigation_top_padding = 0x7f06028e
com.tytfizikkamp:attr/layout_editor_absoluteX = 0x7f03028c
com.tytfizikkamp:dimen/abc_disabled_alpha_material_light = 0x7f060028
com.tytfizikkamp:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
com.tytfizikkamp:drawable/ic_call_decline_low = 0x7f07008d
com.tytfizikkamp:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f1103fb
com.tytfizikkamp:dimen/abc_disabled_alpha_material_dark = 0x7f060027
com.tytfizikkamp:dimen/abc_dialog_title_divider_material = 0x7f060026
com.tytfizikkamp:macro/m3_comp_badge_large_label_text_type = 0x7f0c0004
com.tytfizikkamp:dimen/abc_dialog_padding_material = 0x7f060024
com.tytfizikkamp:dimen/abc_dialog_min_width_major = 0x7f060022
com.tytfizikkamp:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
com.tytfizikkamp:dimen/abc_dialog_fixed_width_major = 0x7f06001e
com.tytfizikkamp:dimen/m3_chip_dragged_translation_z = 0x7f0600f7
com.tytfizikkamp:integer/m3_sys_motion_duration_short2 = 0x7f09001c
com.tytfizikkamp:id/decelerateAndComplete = 0x7f08008f
com.tytfizikkamp:anim/abc_popup_exit = 0x7f010004
com.tytfizikkamp:dimen/abc_dialog_corner_radius_material = 0x7f06001b
com.tytfizikkamp:attr/commitIcon = 0x7f030121
com.tytfizikkamp:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f07001c
com.tytfizikkamp:style/Widget.Material3.Button.ElevatedButton = 0x7f11036a
com.tytfizikkamp:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0600c7
com.tytfizikkamp:macro/m3_sys_color_dark_surface_tint = 0x7f0c0175
com.tytfizikkamp:attr/extendedFloatingActionButtonStyle = 0x7f0301b5
com.tytfizikkamp:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0600c0
com.tytfizikkamp:attr/autoSizeMinTextSize = 0x7f030040
com.tytfizikkamp:dimen/abc_control_padding_material = 0x7f06001a
com.tytfizikkamp:layout/fps_view = 0x7f0b002f
com.tytfizikkamp:attr/dialogTheme = 0x7f030169
com.tytfizikkamp:attr/searchPrefixText = 0x7f030392
com.tytfizikkamp:dimen/abc_dropdownitem_icon_width = 0x7f060029
com.tytfizikkamp:color/design_dark_default_color_primary_variant = 0x7f05003c
com.tytfizikkamp:color/mtrl_textinput_filled_box_default_background_color = 0x7f0502ec
com.tytfizikkamp:dimen/abc_control_corner_material = 0x7f060018
com.tytfizikkamp:dimen/abc_config_prefDialogWidth = 0x7f060017
com.tytfizikkamp:color/m3_ref_palette_primary70 = 0x7f050130
com.tytfizikkamp:attr/forceApplySystemWindowInsetTop = 0x7f0301fa
com.tytfizikkamp:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
com.tytfizikkamp:id/month_grid = 0x7f08010e
com.tytfizikkamp:id/mini = 0x7f08010c
com.tytfizikkamp:color/m3_sys_color_dark_on_error_container = 0x7f050163
com.tytfizikkamp:dimen/abc_button_padding_vertical_material = 0x7f060015
com.tytfizikkamp:style/TextAppearance.Compat.Notification.Line2 = 0x7f1101cf
com.tytfizikkamp:drawable/abc_textfield_default_mtrl_alpha = 0x7f070072
com.tytfizikkamp:dimen/abc_button_padding_horizontal_material = 0x7f060014
com.tytfizikkamp:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f110280
com.tytfizikkamp:attr/lastBaselineToBottomHeight = 0x7f030256
com.tytfizikkamp:color/design_fab_stroke_end_outer_color = 0x7f050052
com.tytfizikkamp:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f0700b7
com.tytfizikkamp:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f11015c
com.tytfizikkamp:attr/buttonBarNeutralButtonStyle = 0x7f03008e
com.tytfizikkamp:dimen/abc_alert_dialog_button_dimen = 0x7f060011
com.tytfizikkamp:layout/abc_alert_dialog_title_material = 0x7f0b000a
com.tytfizikkamp:dimen/m3_bottom_sheet_elevation = 0x7f0600c4
com.tytfizikkamp:style/Base.V26.Theme.AppCompat = 0x7f1100b8
com.tytfizikkamp:dimen/abc_action_button_min_width_material = 0x7f06000e
com.tytfizikkamp:id/actions = 0x7f08004f
com.tytfizikkamp:dimen/abc_action_button_min_height_material = 0x7f06000d
com.tytfizikkamp:style/Widget.Material3.NavigationView = 0x7f1103d8
com.tytfizikkamp:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f0601f7
com.tytfizikkamp:integer/material_motion_duration_medium_1 = 0x7f090028
com.tytfizikkamp:attr/scrimVisibleHeightTrigger = 0x7f03038f
com.tytfizikkamp:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
com.tytfizikkamp:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f110190
com.tytfizikkamp:style/Animation.Design.BottomSheetDialog = 0x7f110007
com.tytfizikkamp:id/react_test_id = 0x7f080162
com.tytfizikkamp:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
com.tytfizikkamp:id/beginning = 0x7f080067
com.tytfizikkamp:color/mtrl_chip_background_color = 0x7f0502c4
com.tytfizikkamp:dimen/abc_action_bar_stacked_max_height = 0x7f060009
com.tytfizikkamp:attr/actionModeFindDrawable = 0x7f030016
com.tytfizikkamp:drawable/m3_bottom_sheet_drag_handle = 0x7f07009d
com.tytfizikkamp:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
com.tytfizikkamp:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
com.tytfizikkamp:dimen/material_clock_face_margin_top = 0x7f060227
com.tytfizikkamp:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f06011a
com.tytfizikkamp:color/design_dark_default_color_on_surface = 0x7f050039
com.tytfizikkamp:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
com.tytfizikkamp:dimen/abc_action_bar_default_height_material = 0x7f060002
com.tytfizikkamp:color/m3_sys_color_light_on_surface_variant = 0x7f0501db
com.tytfizikkamp:dimen/abc_action_bar_content_inset_material = 0x7f060000
com.tytfizikkamp:string/mtrl_timepicker_cancel = 0x7f1000c1
com.tytfizikkamp:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0602a8
com.tytfizikkamp:dimen/m3_searchbar_margin_vertical = 0x7f0601de
com.tytfizikkamp:color/tooltip_background_light = 0x7f050306
com.tytfizikkamp:color/tooltip_background_dark = 0x7f050305
com.tytfizikkamp:color/switch_thumb_normal_material_dark = 0x7f050303
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral96 = 0x7f0500b4
com.tytfizikkamp:color/primary_dark_material_dark = 0x7f0502f1
com.tytfizikkamp:color/switch_thumb_material_light = 0x7f050302
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f110072
com.tytfizikkamp:id/mtrl_picker_title_text = 0x7f08012a
com.tytfizikkamp:attr/displayOptions = 0x7f03016a
com.tytfizikkamp:dimen/tooltip_margin = 0x7f06031e
com.tytfizikkamp:color/switch_thumb_disabled_material_light = 0x7f050300
com.tytfizikkamp:drawable/m3_tabs_rounded_line_indicator = 0x7f0700a4
com.tytfizikkamp:attr/thumbIcon = 0x7f030448
com.tytfizikkamp:color/m3_ref_palette_error90 = 0x7f050100
com.tytfizikkamp:color/switch_thumb_disabled_material_dark = 0x7f0502ff
com.tytfizikkamp:color/secondary_text_disabled_material_dark = 0x7f0502fd
com.tytfizikkamp:attr/flow_maxElementsWrap = 0x7f0301e6
com.tytfizikkamp:color/ripple_material_light = 0x7f0502fa
com.tytfizikkamp:style/TextAppearance.Design.Placeholder = 0x7f1101d8
com.tytfizikkamp:attr/switchPadding = 0x7f0303ec
com.tytfizikkamp:color/primary_text_disabled_material_light = 0x7f0502f8
com.tytfizikkamp:style/ShapeAppearance.Material3.Corner.Medium = 0x7f11017a
com.tytfizikkamp:drawable/abc_ic_menu_overflow_material = 0x7f070044
com.tytfizikkamp:color/primary_text_disabled_material_dark = 0x7f0502f7
com.tytfizikkamp:color/primary_text_default_material_light = 0x7f0502f6
com.tytfizikkamp:style/Theme.AppCompat.Dialog = 0x7f11021f
com.tytfizikkamp:color/primary_material_dark = 0x7f0502f3
com.tytfizikkamp:color/primary_dark_material_light = 0x7f0502f2
com.tytfizikkamp:color/m3_timepicker_display_ripple_color = 0x7f05020e
com.tytfizikkamp:color/notification_action_color_filter = 0x7f0502ef
com.tytfizikkamp:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0602bd
com.tytfizikkamp:color/mtrl_textinput_hovered_box_stroke_color = 0x7f0502ee
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f110074
com.tytfizikkamp:attr/chipIcon = 0x7f0300ba
com.tytfizikkamp:color/mtrl_textinput_disabled_color = 0x7f0502eb
com.tytfizikkamp:color/mtrl_text_btn_text_color_selector = 0x7f0502e9
com.tytfizikkamp:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0c0020
com.tytfizikkamp:id/original_important_for_ax = 0x7f08014b
com.tytfizikkamp:color/mtrl_tabs_ripple_color = 0x7f0502e8
com.tytfizikkamp:layout/mtrl_search_view = 0x7f0b0064
com.tytfizikkamp:color/mtrl_tabs_legacy_text_color_selector = 0x7f0502e7
com.tytfizikkamp:color/m3_ref_palette_dynamic_primary20 = 0x7f0500d2
com.tytfizikkamp:color/mtrl_tabs_icon_color_selector_colored = 0x7f0502e6
com.tytfizikkamp:attr/tabPaddingStart = 0x7f030402
com.tytfizikkamp:color/mtrl_tabs_colored_ripple_color = 0x7f0502e4
com.tytfizikkamp:color/m3_sys_color_dark_surface = 0x7f050172
com.tytfizikkamp:color/m3_textfield_input_text_color = 0x7f050206
com.tytfizikkamp:color/mtrl_popupmenu_overlay_color = 0x7f0502de
com.tytfizikkamp:string/menuitem_description = 0x7f100084
com.tytfizikkamp:color/mtrl_outlined_stroke_color = 0x7f0502dd
com.tytfizikkamp:color/mtrl_outlined_icon_tint = 0x7f0502dc
com.tytfizikkamp:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0c0105
com.tytfizikkamp:id/transition_transform = 0x7f0801e0
com.tytfizikkamp:color/mtrl_on_surface_ripple_color = 0x7f0502db
com.tytfizikkamp:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0c00d2
com.tytfizikkamp:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f0502da
com.tytfizikkamp:attr/cardElevation = 0x7f03009e
com.tytfizikkamp:attr/logoDescription = 0x7f0302b2
com.tytfizikkamp:drawable/design_snackbar_background = 0x7f070086
com.tytfizikkamp:color/mtrl_navigation_item_icon_tint = 0x7f0502d8
com.tytfizikkamp:color/mtrl_navigation_item_background_color = 0x7f0502d7
com.tytfizikkamp:color/mtrl_navigation_bar_item_tint = 0x7f0502d5
com.tytfizikkamp:dimen/material_clock_display_padding = 0x7f060224
com.tytfizikkamp:color/mtrl_navigation_bar_colored_item_tint = 0x7f0502d3
com.tytfizikkamp:color/dim_foreground_disabled_material_dark = 0x7f050057
com.tytfizikkamp:color/mtrl_filled_stroke_color = 0x7f0502d1
com.tytfizikkamp:color/mtrl_filled_icon_tint = 0x7f0502d0
com.tytfizikkamp:color/mtrl_filled_background_color = 0x7f0502cf
com.tytfizikkamp:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f110155
com.tytfizikkamp:attr/endIconContentDescription = 0x7f030192
com.tytfizikkamp:attr/layout_constraintEnd_toStartOf = 0x7f03026d
com.tytfizikkamp:color/mtrl_fab_ripple_color = 0x7f0502ce
com.tytfizikkamp:color/mtrl_fab_icon_text_color_selector = 0x7f0502cd
com.tytfizikkamp:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f110479
com.tytfizikkamp:color/mtrl_choice_chip_text_color = 0x7f0502ca
com.tytfizikkamp:dimen/m3_alert_dialog_action_bottom_padding = 0x7f06009e
com.tytfizikkamp:color/mtrl_choice_chip_ripple_color = 0x7f0502c9
com.tytfizikkamp:color/mtrl_chip_surface_color = 0x7f0502c6
com.tytfizikkamp:style/Widget.Material3.SearchBar = 0x7f1103df
com.tytfizikkamp:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f110135
com.tytfizikkamp:id/time = 0x7f0801cd
com.tytfizikkamp:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f07001b
com.tytfizikkamp:dimen/design_snackbar_padding_horizontal = 0x7f060086
com.tytfizikkamp:color/mtrl_chip_close_icon_tint = 0x7f0502c5
com.tytfizikkamp:drawable/mtrl_switch_thumb_unchecked = 0x7f0700cf
com.tytfizikkamp:color/mtrl_card_view_foreground = 0x7f0502c2
com.tytfizikkamp:color/mtrl_btn_text_color_selector = 0x7f0502be
com.tytfizikkamp:dimen/mtrl_slider_halo_radius = 0x7f0602e6
com.tytfizikkamp:attr/listChoiceIndicatorSingleAnimated = 0x7f0302a3
com.tytfizikkamp:color/mtrl_btn_stroke_color_selector = 0x7f0502ba
com.tytfizikkamp:attr/tabMaxWidth = 0x7f0303fc
com.tytfizikkamp:color/material_timepicker_clockface = 0x7f0502b6
com.tytfizikkamp:drawable/ic_mtrl_checked_circle = 0x7f070094
com.tytfizikkamp:drawable/m3_selection_control_ripple = 0x7f0700a1
com.tytfizikkamp:color/material_timepicker_button_stroke = 0x7f0502b4
com.tytfizikkamp:string/material_minute_selection = 0x7f100071
com.tytfizikkamp:id/design_navigation_view = 0x7f080097
com.tytfizikkamp:color/material_timepicker_button_background = 0x7f0502b3
com.tytfizikkamp:color/material_slider_thumb_color = 0x7f0502b2
com.tytfizikkamp:style/Theme.Design = 0x7f110230
com.tytfizikkamp:color/material_slider_inactive_tick_marks_color = 0x7f0502b0
com.tytfizikkamp:color/material_slider_halo_color = 0x7f0502af
com.tytfizikkamp:style/Widget.Material3.Button = 0x7f110369
com.tytfizikkamp:string/bottomsheet_drag_handle_clicked = 0x7f100023
com.tytfizikkamp:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
com.tytfizikkamp:style/ShapeAppearance.MaterialComponents = 0x7f110182
com.tytfizikkamp:attr/fabAlignmentModeEndMargin = 0x7f0301ba
com.tytfizikkamp:color/material_personalized_color_text_primary_inverse = 0x7f0502a5
com.tytfizikkamp:color/material_personalized_color_text_hint_foreground_inverse = 0x7f0502a4
com.tytfizikkamp:style/Platform.V25.AppCompat = 0x7f110149
com.tytfizikkamp:color/material_personalized_color_tertiary_container = 0x7f0502a3
com.tytfizikkamp:attr/closeIconEndPadding = 0x7f0300d3
com.tytfizikkamp:color/m3_dynamic_default_color_secondary_text = 0x7f050083
com.tytfizikkamp:color/m3_ref_palette_secondary80 = 0x7f05013e
com.tytfizikkamp:color/material_personalized_color_surface_inverse = 0x7f0502a0
com.tytfizikkamp:id/activity_chooser_view_content = 0x7f080050
com.tytfizikkamp:attr/layout_goneMarginTop = 0x7f030293
com.tytfizikkamp:attr/region_heightMoreThan = 0x7f030374
com.tytfizikkamp:color/material_personalized_color_surface_container_low = 0x7f05029d
com.tytfizikkamp:integer/bottom_sheet_slide_duration = 0x7f090003
com.tytfizikkamp:color/material_personalized_color_surface_container_high = 0x7f05029b
com.tytfizikkamp:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0c00ed
com.tytfizikkamp:color/material_personalized_color_surface = 0x7f050298
com.tytfizikkamp:color/material_personalized_color_secondary_text = 0x7f050296
com.tytfizikkamp:id/mtrl_calendar_year_selector_frame = 0x7f08011d
com.tytfizikkamp:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f06015f
com.tytfizikkamp:color/material_personalized_color_secondary_container = 0x7f050295
com.tytfizikkamp:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f060169
com.tytfizikkamp:attr/dividerPadding = 0x7f030170
com.tytfizikkamp:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0600be
com.tytfizikkamp:color/material_personalized_color_secondary = 0x7f050294
com.tytfizikkamp:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f070018
com.tytfizikkamp:color/material_personalized_color_primary_text = 0x7f050292
com.tytfizikkamp:color/material_personalized_color_primary_inverse = 0x7f050291
com.tytfizikkamp:attr/textAppearanceDisplayMedium = 0x7f030419
com.tytfizikkamp:color/material_personalized_color_primary = 0x7f05028f
com.tytfizikkamp:dimen/m3_comp_progress_indicator_track_thickness = 0x7f060165
com.tytfizikkamp:color/material_personalized_color_on_surface_variant = 0x7f05028a
com.tytfizikkamp:animator/fragment_open_enter = 0x7f020007
com.tytfizikkamp:color/material_personalized_color_on_surface_inverse = 0x7f050289
com.tytfizikkamp:color/material_personalized_color_on_secondary_container = 0x7f050287
com.tytfizikkamp:attr/floatingActionButtonSmallSurfaceStyle = 0x7f0301d5
com.tytfizikkamp:color/material_personalized_color_on_secondary = 0x7f050286
com.tytfizikkamp:attr/activityChooserViewStyle = 0x7f030025
com.tytfizikkamp:color/material_personalized_color_on_primary_container = 0x7f050285
com.tytfizikkamp:drawable/paused_in_debugger_background = 0x7f0700e8
com.tytfizikkamp:dimen/design_snackbar_padding_vertical = 0x7f060087
com.tytfizikkamp:dimen/m3_appbar_size_compact = 0x7f0600aa
com.tytfizikkamp:color/material_personalized_color_on_error_container = 0x7f050283
com.tytfizikkamp:color/material_personalized_color_on_background = 0x7f050281
com.tytfizikkamp:color/material_personalized_color_control_normal = 0x7f05027e
com.tytfizikkamp:layout/abc_list_menu_item_checkbox = 0x7f0b000e
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_on_background = 0x7f050183
com.tytfizikkamp:anim/m3_side_sheet_enter_from_left = 0x7f01002b
com.tytfizikkamp:dimen/mtrl_textinput_box_stroke_width_default = 0x7f060300
com.tytfizikkamp:styleable/GenericDraweeHierarchy = 0x7f12003a
com.tytfizikkamp:color/material_personalized_color_control_highlight = 0x7f05027d
com.tytfizikkamp:id/centerInside = 0x7f080072
com.tytfizikkamp:color/material_personalized_color_control_activated = 0x7f05027c
com.tytfizikkamp:attr/materialCalendarMonth = 0x7f0302cb
com.tytfizikkamp:attr/pressedStateOverlayImage = 0x7f030363
com.tytfizikkamp:color/material_personalized_color_background = 0x7f05027b
com.tytfizikkamp:color/material_personalized__highlighted_text_inverse = 0x7f05027a
com.tytfizikkamp:color/material_on_surface_emphasis_medium = 0x7f050277
com.tytfizikkamp:color/m3_radiobutton_button_tint = 0x7f05009c
com.tytfizikkamp:color/material_on_surface_disabled = 0x7f050275
com.tytfizikkamp:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1100d7
com.tytfizikkamp:attr/horizontalOffsetWithText = 0x7f030215
com.tytfizikkamp:color/m3_ref_palette_dynamic_primary60 = 0x7f0500d6
com.tytfizikkamp:color/material_on_primary_emphasis_medium = 0x7f050274
com.tytfizikkamp:color/material_on_primary_disabled = 0x7f050272
com.tytfizikkamp:integer/show_password_duration = 0x7f090043
com.tytfizikkamp:attr/layout_constraintHeight_default = 0x7f030271
com.tytfizikkamp:color/material_on_background_emphasis_medium = 0x7f050271
com.tytfizikkamp:style/Widget.Material3.Button.TextButton = 0x7f110373
com.tytfizikkamp:color/material_on_background_disabled = 0x7f05026f
com.tytfizikkamp:color/material_harmonized_color_on_error_container = 0x7f05026e
com.tytfizikkamp:dimen/m3_alert_dialog_icon_size = 0x7f0600a3
com.tytfizikkamp:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f110324
com.tytfizikkamp:style/Theme.Design.Light.BottomSheetDialog = 0x7f110233
com.tytfizikkamp:attr/snackbarTextViewStyle = 0x7f0303bd
com.tytfizikkamp:attr/navigationContentDescription = 0x7f030327
com.tytfizikkamp:color/material_harmonized_color_error_container = 0x7f05026c
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0500c4
com.tytfizikkamp:id/SHOW_PATH = 0x7f080009
com.tytfizikkamp:color/material_harmonized_color_error = 0x7f05026b
com.tytfizikkamp:drawable/m3_avd_show_password = 0x7f07009c
com.tytfizikkamp:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f070036
com.tytfizikkamp:attr/layout_constraintLeft_creator = 0x7f030278
com.tytfizikkamp:drawable/abc_ratingbar_material = 0x7f07005b
com.tytfizikkamp:color/material_grey_300 = 0x7f050265
com.tytfizikkamp:attr/borderWidth = 0x7f030078
com.tytfizikkamp:color/material_grey_100 = 0x7f050264
com.tytfizikkamp:color/material_dynamic_tertiary99 = 0x7f050263
com.tytfizikkamp:color/material_dynamic_tertiary95 = 0x7f050262
com.tytfizikkamp:attr/searchIcon = 0x7f030391
com.tytfizikkamp:color/material_dynamic_tertiary80 = 0x7f050260
com.tytfizikkamp:color/material_dynamic_tertiary60 = 0x7f05025e
com.tytfizikkamp:integer/mtrl_switch_thumb_motion_duration = 0x7f090036
com.tytfizikkamp:color/material_dynamic_tertiary20 = 0x7f05025a
com.tytfizikkamp:color/m3_slider_inactive_track_color_legacy = 0x7f050156
com.tytfizikkamp:color/material_dynamic_tertiary10 = 0x7f050258
com.tytfizikkamp:color/material_dynamic_secondary99 = 0x7f050256
com.tytfizikkamp:color/material_dynamic_secondary80 = 0x7f050253
com.tytfizikkamp:color/material_dynamic_secondary40 = 0x7f05024f
com.tytfizikkamp:string/abc_shareactionprovider_share_with = 0x7f100018
com.tytfizikkamp:color/m3_ref_palette_dynamic_secondary90 = 0x7f0500e6
com.tytfizikkamp:color/material_dynamic_secondary20 = 0x7f05024d
com.tytfizikkamp:color/material_dynamic_secondary100 = 0x7f05024c
com.tytfizikkamp:color/material_dynamic_secondary0 = 0x7f05024a
com.tytfizikkamp:attr/actionModeCutDrawable = 0x7f030015
com.tytfizikkamp:color/material_dynamic_primary95 = 0x7f050248
com.tytfizikkamp:integer/mtrl_calendar_selection_text_lines = 0x7f090031
com.tytfizikkamp:drawable/m3_tabs_line_indicator = 0x7f0700a3
com.tytfizikkamp:color/material_dynamic_primary80 = 0x7f050246
com.tytfizikkamp:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1100a5
com.tytfizikkamp:color/material_dynamic_primary70 = 0x7f050245
com.tytfizikkamp:string/mtrl_picker_out_of_range = 0x7f1000a6
com.tytfizikkamp:color/material_dynamic_primary60 = 0x7f050244
com.tytfizikkamp:color/material_dynamic_primary50 = 0x7f050243
com.tytfizikkamp:color/m3_chip_ripple_color = 0x7f050073
com.tytfizikkamp:color/material_dynamic_primary40 = 0x7f050242
com.tytfizikkamp:drawable/m3_password_eye = 0x7f07009e
com.tytfizikkamp:drawable/ic_resume = 0x7f070098
com.tytfizikkamp:attr/state_lifted = 0x7f0303d4
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral20 = 0x7f0500a4
com.tytfizikkamp:color/material_dynamic_primary30 = 0x7f050241
com.tytfizikkamp:dimen/mtrl_calendar_year_corner = 0x7f060297
com.tytfizikkamp:color/material_dynamic_primary100 = 0x7f05023f
com.tytfizikkamp:color/material_dynamic_primary0 = 0x7f05023d
com.tytfizikkamp:macro/m3_comp_outlined_card_container_color = 0x7f0c00a8
com.tytfizikkamp:macro/m3_comp_filled_icon_button_container_color = 0x7f0c0048
com.tytfizikkamp:color/material_dynamic_neutral_variant90 = 0x7f05023a
com.tytfizikkamp:dimen/mtrl_alert_dialog_background_inset_top = 0x7f06024a
com.tytfizikkamp:color/material_dynamic_neutral_variant80 = 0x7f050239
com.tytfizikkamp:styleable/ImageFilterView = 0x7f12003d
com.tytfizikkamp:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f060251
com.tytfizikkamp:attr/motionDurationShort4 = 0x7f030310
com.tytfizikkamp:color/material_dynamic_neutral_variant70 = 0x7f050238
com.tytfizikkamp:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f070016
com.tytfizikkamp:color/m3_radiobutton_ripple_tint = 0x7f05009d
com.tytfizikkamp:color/material_dynamic_neutral_variant60 = 0x7f050237
com.tytfizikkamp:dimen/m3_btn_icon_only_icon_padding = 0x7f0600d6
com.tytfizikkamp:color/material_dynamic_neutral_variant50 = 0x7f050236
com.tytfizikkamp:macro/m3_comp_elevated_button_container_color = 0x7f0c0029
com.tytfizikkamp:id/path = 0x7f080155
com.tytfizikkamp:color/material_dynamic_neutral_variant100 = 0x7f050232
com.tytfizikkamp:color/material_dynamic_neutral99 = 0x7f05022f
com.tytfizikkamp:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f11031f
com.tytfizikkamp:dimen/m3_comp_switch_track_width = 0x7f060199
com.tytfizikkamp:color/material_dynamic_neutral80 = 0x7f05022c
com.tytfizikkamp:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0602a5
com.tytfizikkamp:color/material_personalized_hint_foreground = 0x7f0502a9
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f110079
com.tytfizikkamp:color/material_dynamic_neutral60 = 0x7f05022a
com.tytfizikkamp:color/material_dynamic_neutral50 = 0x7f050229
com.tytfizikkamp:color/material_dynamic_neutral40 = 0x7f050228
com.tytfizikkamp:dimen/mtrl_btn_text_btn_icon_padding = 0x7f06026d
com.tytfizikkamp:style/TextAppearance.AppCompat.Title = 0x7f1101b7
com.tytfizikkamp:anim/rns_ios_from_right_background_close = 0x7f01003e
com.tytfizikkamp:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0600af
com.tytfizikkamp:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
com.tytfizikkamp:color/m3_sys_color_dark_on_background = 0x7f050161
com.tytfizikkamp:color/material_dynamic_neutral30 = 0x7f050227
com.tytfizikkamp:attr/appBarLayoutStyle = 0x7f030036
com.tytfizikkamp:dimen/design_tab_text_size = 0x7f06008c
com.tytfizikkamp:color/material_dynamic_neutral100 = 0x7f050225
com.tytfizikkamp:dimen/notification_top_pad = 0x7f06031a
com.tytfizikkamp:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0c00b2
com.tytfizikkamp:attr/divider = 0x7f03016b
com.tytfizikkamp:color/material_dynamic_neutral10 = 0x7f050224
com.tytfizikkamp:color/material_dynamic_color_light_on_error_container = 0x7f050222
com.tytfizikkamp:color/material_dynamic_secondary30 = 0x7f05024e
com.tytfizikkamp:color/material_dynamic_color_light_on_error = 0x7f050221
com.tytfizikkamp:color/material_dynamic_color_light_error = 0x7f05021f
com.tytfizikkamp:color/mtrl_choice_chip_background_color = 0x7f0502c8
com.tytfizikkamp:attr/cornerSizeTopRight = 0x7f030146
com.tytfizikkamp:color/material_dynamic_color_dark_on_error_container = 0x7f05021e
com.tytfizikkamp:attr/haloRadius = 0x7f030202
com.tytfizikkamp:dimen/m3_comp_fab_primary_icon_size = 0x7f06011b
com.tytfizikkamp:color/material_dynamic_color_dark_on_error = 0x7f05021d
com.tytfizikkamp:color/material_divider_color = 0x7f05021a
com.tytfizikkamp:color/material_deep_teal_200 = 0x7f050218
com.tytfizikkamp:id/ignoreRequest = 0x7f0800db
com.tytfizikkamp:color/material_cursor_color = 0x7f050217
com.tytfizikkamp:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1100aa
com.tytfizikkamp:color/material_blue_grey_950 = 0x7f050216
com.tytfizikkamp:macro/m3_comp_time_picker_container_color = 0x7f0c014e
com.tytfizikkamp:id/notification_main_column = 0x7f080139
com.tytfizikkamp:attr/autofillInlineSuggestionEndIconStyle = 0x7f030046
com.tytfizikkamp:color/material_blue_grey_900 = 0x7f050215
com.tytfizikkamp:id/graph = 0x7f0800cd
com.tytfizikkamp:color/m3_tonal_button_ripple_color_selector = 0x7f050213
com.tytfizikkamp:attr/textInputFilledStyle = 0x7f03043b
com.tytfizikkamp:color/m3_timepicker_secondary_text_button_text_color = 0x7f050211
com.tytfizikkamp:dimen/mtrl_btn_elevation = 0x7f06025e
com.tytfizikkamp:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0301cc
com.tytfizikkamp:color/mtrl_calendar_selected_range = 0x7f0502c1
com.tytfizikkamp:color/material_personalized_color_error = 0x7f05027f
com.tytfizikkamp:style/TextAppearance.Design.Counter = 0x7f1101d3
com.tytfizikkamp:color/m3_timepicker_clock_text_color = 0x7f05020c
com.tytfizikkamp:macro/m3_comp_filled_text_field_input_text_type = 0x7f0c0050
com.tytfizikkamp:color/m3_timepicker_button_text_color = 0x7f05020b
com.tytfizikkamp:color/m3_timepicker_button_ripple_color = 0x7f05020a
com.tytfizikkamp:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0c0097
com.tytfizikkamp:color/m3_textfield_label_color = 0x7f050207
com.tytfizikkamp:drawable/mtrl_switch_thumb_pressed = 0x7f0700cc
com.tytfizikkamp:bool/abc_action_bar_embed_tabs = 0x7f040000
com.tytfizikkamp:dimen/tooltip_y_offset_non_touch = 0x7f060322
com.tytfizikkamp:id/fitEnd = 0x7f0800be
com.tytfizikkamp:color/m3_textfield_indicator_text_color = 0x7f050205
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f1102fa
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f1101df
com.tytfizikkamp:color/m3_text_button_ripple_color_selector = 0x7f050203
com.tytfizikkamp:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0600df
com.tytfizikkamp:styleable/TextInputEditText = 0x7f12008a
com.tytfizikkamp:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f11004d
com.tytfizikkamp:attr/paddingBottomNoButtons = 0x7f03033c
com.tytfizikkamp:color/m3_text_button_foreground_color_selector = 0x7f050202
com.tytfizikkamp:color/m3_tabs_text_color_secondary = 0x7f050200
com.tytfizikkamp:color/m3_tabs_ripple_color_secondary = 0x7f0501fe
com.tytfizikkamp:style/Platform.V21.AppCompat = 0x7f110147
com.tytfizikkamp:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f060110
com.tytfizikkamp:color/m3_tabs_icon_color_secondary = 0x7f0501fc
com.tytfizikkamp:id/accessibility_custom_action_26 = 0x7f080026
com.tytfizikkamp:attr/onTouchUp = 0x7f030338
com.tytfizikkamp:color/m3_sys_color_tertiary_fixed_dim = 0x7f0501fa
com.tytfizikkamp:string/mtrl_picker_date_header_title = 0x7f10009c
com.tytfizikkamp:string/material_motion_easing_linear = 0x7f100076
com.tytfizikkamp:attr/emojiCompatEnabled = 0x7f03018f
com.tytfizikkamp:dimen/abc_text_size_subhead_material = 0x7f06004d
com.tytfizikkamp:color/m3_sys_color_secondary_fixed_dim = 0x7f0501f8
com.tytfizikkamp:color/m3_sys_color_secondary_fixed = 0x7f0501f7
com.tytfizikkamp:color/m3_sys_color_primary_fixed_dim = 0x7f0501f6
com.tytfizikkamp:color/m3_ref_palette_neutral70 = 0x7f050111
com.tytfizikkamp:color/m3_sys_color_primary_fixed = 0x7f0501f5
com.tytfizikkamp:id/row_index_key = 0x7f080175
com.tytfizikkamp:dimen/mtrl_card_dragged_z = 0x7f06029f
com.tytfizikkamp:color/m3_sys_color_on_tertiary_fixed = 0x7f0501f3
com.tytfizikkamp:attr/autofillInlineSuggestionSubtitle = 0x7f030048
com.tytfizikkamp:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0601bc
com.tytfizikkamp:attr/tickMarkTint = 0x7f030458
com.tytfizikkamp:color/m3_sys_color_on_primary_fixed_variant = 0x7f0501f0
com.tytfizikkamp:color/m3_sys_color_on_primary_fixed = 0x7f0501ef
com.tytfizikkamp:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f060219
com.tytfizikkamp:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0c0069
com.tytfizikkamp:id/line1 = 0x7f0800ec
com.tytfizikkamp:color/m3_sys_color_light_surface_variant = 0x7f0501ec
com.tytfizikkamp:id/accessibility_custom_action_18 = 0x7f08001d
com.tytfizikkamp:integer/design_tab_indicator_anim_duration_ms = 0x7f090007
com.tytfizikkamp:color/m3_sys_color_light_surface_dim = 0x7f0501eb
com.tytfizikkamp:string/catalyst_dismiss_button = 0x7f100039
com.tytfizikkamp:color/m3_sys_color_light_surface_container_lowest = 0x7f0501ea
com.tytfizikkamp:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1100b7
com.tytfizikkamp:color/m3_ref_palette_dynamic_primary10 = 0x7f0500d0
com.tytfizikkamp:color/m3_sys_color_light_surface_container_low = 0x7f0501e9
com.tytfizikkamp:color/m3_sys_color_light_surface_container_highest = 0x7f0501e8
com.tytfizikkamp:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f11014d
com.tytfizikkamp:color/m3_sys_color_light_surface_container_high = 0x7f0501e7
com.tytfizikkamp:color/m3_sys_color_light_surface_container = 0x7f0501e6
com.tytfizikkamp:color/m3_sys_color_light_primary_container = 0x7f0501e1
com.tytfizikkamp:dimen/mtrl_calendar_year_vertical_padding = 0x7f06029a
com.tytfizikkamp:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0c00d1
com.tytfizikkamp:dimen/m3_chip_hovered_translation_z = 0x7f0600f9
com.tytfizikkamp:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0c0060
com.tytfizikkamp:color/m3_sys_color_light_primary = 0x7f0501e0
com.tytfizikkamp:color/m3_sys_color_light_outline = 0x7f0501de
com.tytfizikkamp:color/m3_sys_color_light_on_secondary_container = 0x7f0501d9
com.tytfizikkamp:color/m3_sys_color_light_on_primary_container = 0x7f0501d7
com.tytfizikkamp:color/m3_sys_color_light_on_primary = 0x7f0501d6
com.tytfizikkamp:color/m3_sys_color_light_on_error_container = 0x7f0501d5
com.tytfizikkamp:styleable/FloatingActionButton_Behavior_Layout = 0x7f120033
com.tytfizikkamp:color/m3_sys_color_light_on_error = 0x7f0501d4
com.tytfizikkamp:dimen/design_navigation_icon_size = 0x7f060078
com.tytfizikkamp:color/m3_sys_color_light_inverse_primary = 0x7f0501d1
com.tytfizikkamp:attr/suffixTextColor = 0x7f0303e8
com.tytfizikkamp:dimen/mtrl_calendar_year_horizontal_padding = 0x7f060299
com.tytfizikkamp:id/open_search_view_content_container = 0x7f080140
com.tytfizikkamp:color/m3_sys_color_light_surface = 0x7f0501e4
com.tytfizikkamp:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0501cb
com.tytfizikkamp:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0501ca
com.tytfizikkamp:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0501c9
com.tytfizikkamp:attr/materialCircleRadius = 0x7f0302d4
com.tytfizikkamp:color/m3_sys_color_dynamic_primary_fixed = 0x7f0501c7
com.tytfizikkamp:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0501c6
com.tytfizikkamp:style/Widget.Material3.SideSheet = 0x7f1103e4
com.tytfizikkamp:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0501c5
com.tytfizikkamp:string/spinbutton_description = 0x7f1000d3
com.tytfizikkamp:dimen/m3_comp_outlined_card_outline_width = 0x7f060154
com.tytfizikkamp:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0501c4
com.tytfizikkamp:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0501c1
com.tytfizikkamp:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0501c0
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1101c2
com.tytfizikkamp:attr/colorOnPrimarySurface = 0x7f0300f7
com.tytfizikkamp:color/m3_sys_color_dynamic_light_tertiary = 0x7f0501bf
com.tytfizikkamp:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f070021
com.tytfizikkamp:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0501ba
com.tytfizikkamp:id/notification_main_column_container = 0x7f08013a
com.tytfizikkamp:dimen/mtrl_tooltip_cornerSize = 0x7f060308
com.tytfizikkamp:id/accessibility_custom_action_4 = 0x7f08002d
com.tytfizikkamp:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0501bd
com.tytfizikkamp:attr/startIconMinSize = 0x7f0303c9
com.tytfizikkamp:color/m3_sys_color_dynamic_light_surface_bright = 0x7f0501b7
com.tytfizikkamp:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f060107
com.tytfizikkamp:color/m3_sys_color_dynamic_light_secondary_container = 0x7f0501b5
com.tytfizikkamp:color/m3_sys_color_dynamic_light_secondary = 0x7f0501b4
com.tytfizikkamp:animator/m3_card_state_list_anim = 0x7f02000d
com.tytfizikkamp:color/m3_sys_color_dynamic_light_outline_variant = 0x7f0501b1
com.tytfizikkamp:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f0501af
com.tytfizikkamp:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f0501ae
com.tytfizikkamp:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f0501ad
com.tytfizikkamp:color/m3_sys_color_dynamic_light_on_surface = 0x7f0501ac
com.tytfizikkamp:attr/snackbarStyle = 0x7f0303bc
com.tytfizikkamp:color/m3_sys_color_dynamic_light_on_secondary = 0x7f0501aa
com.tytfizikkamp:color/material_dynamic_tertiary70 = 0x7f05025f
com.tytfizikkamp:id/navigation_bar_item_icon_container = 0x7f08012e
com.tytfizikkamp:color/m3_sys_color_dynamic_light_on_primary = 0x7f0501a8
com.tytfizikkamp:color/m3_sys_color_dynamic_light_on_error_container = 0x7f0501a7
com.tytfizikkamp:color/m3_sys_color_dynamic_light_on_error = 0x7f0501a6
com.tytfizikkamp:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1100e4
com.tytfizikkamp:dimen/m3_simple_item_color_hovered_alpha = 0x7f0601ea
com.tytfizikkamp:color/m3_slider_halo_color_legacy = 0x7f050154
com.tytfizikkamp:color/m3_sys_color_dynamic_light_error_container = 0x7f0501a1
com.tytfizikkamp:layout/design_navigation_item_subheader = 0x7f0b0029
com.tytfizikkamp:color/m3_sys_color_dynamic_light_error = 0x7f0501a0
com.tytfizikkamp:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f110047
com.tytfizikkamp:attr/chipGroupStyle = 0x7f0300b9
com.tytfizikkamp:drawable/m3_popupmenu_background_overlay = 0x7f07009f
com.tytfizikkamp:styleable/MaterialSwitch = 0x7f120059
com.tytfizikkamp:style/Base.Widget.AppCompat.Button.Colored = 0x7f1100d4
com.tytfizikkamp:color/m3_sys_color_dynamic_light_background = 0x7f05019f
com.tytfizikkamp:id/mtrl_calendar_day_selector_frame = 0x7f080116
com.tytfizikkamp:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f0602fd
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f05019e
com.tytfizikkamp:color/m3_sys_color_dark_on_secondary_container = 0x7f050167
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f050199
com.tytfizikkamp:layout/material_chip_input_combo = 0x7f0b0037
com.tytfizikkamp:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f060137
com.tytfizikkamp:attr/fontProviderCerts = 0x7f0301f0
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_surface_container = 0x7f050196
com.tytfizikkamp:id/accessibility_custom_action_2 = 0x7f08001f
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f050195
com.tytfizikkamp:attr/closeItemLayout = 0x7f0300d8
com.tytfizikkamp:attr/listPreferredItemPaddingEnd = 0x7f0302ac
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_surface = 0x7f050194
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1103c7
com.tytfizikkamp:id/tag_unhandled_key_listeners = 0x7f0801bb
com.tytfizikkamp:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f070073
com.tytfizikkamp:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f060215
com.tytfizikkamp:id/withText = 0x7f0801f3
com.tytfizikkamp:id/mtrl_picker_text_input_range_end = 0x7f080128
com.tytfizikkamp:drawable/$m3_avd_show_password__0 = 0x7f070009
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_secondary = 0x7f050192
com.tytfizikkamp:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0c002f
com.tytfizikkamp:integer/hide_password_duration = 0x7f090008
com.tytfizikkamp:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0501cc
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_primary = 0x7f050190
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f05018f
com.tytfizikkamp:id/mtrl_view_tag_bottom_padding = 0x7f08012b
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f05018d
com.tytfizikkamp:attr/drawableStartCompat = 0x7f03017c
com.tytfizikkamp:color/notification_icon_bg_color = 0x7f0502f0
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f05018c
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_on_surface = 0x7f05018a
com.tytfizikkamp:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f11007e
com.tytfizikkamp:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0c0157
com.tytfizikkamp:attr/onPositiveCross = 0x7f030336
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f050188
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f110441
com.tytfizikkamp:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f060239
com.tytfizikkamp:attr/motionEasingStandard = 0x7f030319
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f050187
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_on_error_container = 0x7f050185
com.tytfizikkamp:style/Widget.Design.FloatingActionButton = 0x7f110350
com.tytfizikkamp:color/material_timepicker_clock_text_color = 0x7f0502b5
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_on_error = 0x7f050184
com.tytfizikkamp:id/BOTTOM_START = 0x7f080002
com.tytfizikkamp:anim/catalyst_push_up_in = 0x7f01001a
com.tytfizikkamp:dimen/mtrl_calendar_bottom_padding = 0x7f060275
com.tytfizikkamp:layout/mtrl_picker_fullscreen = 0x7f0b005b
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f050181
com.tytfizikkamp:color/mtrl_navigation_bar_colored_ripple_color = 0x7f0502d4
com.tytfizikkamp:attr/cardViewStyle = 0x7f0300a3
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f050180
com.tytfizikkamp:interpolator/m3_sys_motion_easing_emphasized = 0x7f0a0007
com.tytfizikkamp:dimen/mtrl_navigation_item_icon_size = 0x7f0602c9
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_error_container = 0x7f05017f
com.tytfizikkamp:attr/progressBarImageScaleType = 0x7f030367
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_error = 0x7f05017e
com.tytfizikkamp:string/mtrl_picker_announce_current_selection = 0x7f100097
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_background = 0x7f05017d
com.tytfizikkamp:style/TextAppearance.Compat.Notification.Title = 0x7f1101d1
com.tytfizikkamp:color/m3_sys_color_dark_tertiary_container = 0x7f05017c
com.tytfizikkamp:macro/m3_comp_extended_fab_surface_container_color = 0x7f0c0032
com.tytfizikkamp:animator/m3_card_elevated_state_list_anim = 0x7f02000c
com.tytfizikkamp:color/m3_sys_color_dark_tertiary = 0x7f05017b
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f05019b
com.tytfizikkamp:color/m3_sys_color_dark_surface_dim = 0x7f050179
com.tytfizikkamp:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0c0074
com.tytfizikkamp:color/m3_sys_color_dark_surface_container_lowest = 0x7f050178
com.tytfizikkamp:color/m3_sys_color_dark_secondary_container = 0x7f050171
com.tytfizikkamp:color/m3_sys_color_dark_secondary = 0x7f050170
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f05018b
com.tytfizikkamp:id/SYM = 0x7f08000b
com.tytfizikkamp:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f070074
com.tytfizikkamp:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f110199
com.tytfizikkamp:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0c0094
com.tytfizikkamp:dimen/mtrl_slider_label_square_side = 0x7f0602e9
com.tytfizikkamp:color/mtrl_indicator_text_color = 0x7f0502d2
com.tytfizikkamp:attr/titleMarginTop = 0x7f030468
com.tytfizikkamp:color/m3_sys_color_dark_on_tertiary = 0x7f05016a
com.tytfizikkamp:dimen/abc_button_inset_vertical_material = 0x7f060013
com.tytfizikkamp:attr/overlay = 0x7f03033a
com.tytfizikkamp:color/mtrl_fab_bg_color_selector = 0x7f0502cc
com.tytfizikkamp:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1103cd
com.tytfizikkamp:color/m3_sys_color_dark_on_surface = 0x7f050168
com.tytfizikkamp:style/Widget.Material3.Badge.AdjustToBounds = 0x7f11035f
com.tytfizikkamp:attr/colorControlHighlight = 0x7f0300ea
com.tytfizikkamp:color/m3_sys_color_dark_on_secondary = 0x7f050166
com.tytfizikkamp:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f11023a
com.tytfizikkamp:attr/fabCustomSize = 0x7f0301c0
com.tytfizikkamp:color/m3_sys_color_dark_on_primary_container = 0x7f050165
com.tytfizikkamp:color/m3_sys_color_dark_on_primary = 0x7f050164
com.tytfizikkamp:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f060200
com.tytfizikkamp:string/abc_prepend_shortcut_label = 0x7f100011
com.tytfizikkamp:dimen/m3_comp_fab_primary_container_elevation = 0x7f060116
com.tytfizikkamp:color/m3_sys_color_dark_on_error = 0x7f050162
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f05019a
com.tytfizikkamp:attr/indeterminateAnimationType = 0x7f030221
com.tytfizikkamp:color/m3_sys_color_dark_inverse_surface = 0x7f050160
com.tytfizikkamp:styleable/StateSet = 0x7f120083
com.tytfizikkamp:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f06019b
com.tytfizikkamp:style/Base.V24.Theme.Material3.Dark = 0x7f1100b4
com.tytfizikkamp:color/m3_sys_color_dark_inverse_primary = 0x7f05015f
com.tytfizikkamp:dimen/m3_sys_elevation_level2 = 0x7f0601f3
com.tytfizikkamp:color/m3_sys_color_dark_inverse_on_surface = 0x7f05015e
com.tytfizikkamp:color/abc_primary_text_material_dark = 0x7f05000b
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0500cb
com.tytfizikkamp:dimen/tooltip_horizontal_padding = 0x7f06031d
com.tytfizikkamp:color/m3_ref_palette_dynamic_secondary100 = 0x7f0500de
com.tytfizikkamp:color/m3_sys_color_dark_error_container = 0x7f05015d
com.tytfizikkamp:color/m3_sys_color_dark_error = 0x7f05015c
com.tytfizikkamp:layout/mtrl_calendar_month = 0x7f0b0050
com.tytfizikkamp:color/m3_sys_color_dark_background = 0x7f05015b
com.tytfizikkamp:id/material_minute_text_input = 0x7f080100
com.tytfizikkamp:attr/buttonIconDimen = 0x7f030094
com.tytfizikkamp:attr/tabSecondaryStyle = 0x7f030405
com.tytfizikkamp:color/m3_slider_thumb_color_legacy = 0x7f050158
com.tytfizikkamp:color/m3_slider_thumb_color = 0x7f050157
com.tytfizikkamp:anim/linear_indeterminate_line2_head_interpolator = 0x7f010025
com.tytfizikkamp:dimen/m3_bottom_sheet_modal_elevation = 0x7f0600c5
com.tytfizikkamp:color/m3_simple_item_ripple_color = 0x7f050151
com.tytfizikkamp:color/m3_ref_palette_white = 0x7f05014f
com.tytfizikkamp:color/m3_ref_palette_tertiary99 = 0x7f05014e
com.tytfizikkamp:color/m3_ref_palette_tertiary90 = 0x7f05014c
com.tytfizikkamp:drawable/abc_tab_indicator_mtrl_alpha = 0x7f07006c
com.tytfizikkamp:string/catalyst_loading_from_url = 0x7f100040
com.tytfizikkamp:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f060196
com.tytfizikkamp:color/m3_sys_color_light_surface_bright = 0x7f0501e5
com.tytfizikkamp:attr/itemHorizontalPadding = 0x7f030233
com.tytfizikkamp:color/m3_ref_palette_tertiary80 = 0x7f05014b
com.tytfizikkamp:dimen/mtrl_bottomappbar_height = 0x7f060259
com.tytfizikkamp:color/m3_ref_palette_tertiary70 = 0x7f05014a
com.tytfizikkamp:color/m3_ref_palette_tertiary60 = 0x7f050149
com.tytfizikkamp:color/m3_ref_palette_tertiary50 = 0x7f050148
com.tytfizikkamp:color/m3_ref_palette_tertiary30 = 0x7f050146
com.tytfizikkamp:color/m3_ref_palette_tertiary100 = 0x7f050144
com.tytfizikkamp:drawable/abc_list_divider_material = 0x7f07004c
com.tytfizikkamp:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f060209
com.tytfizikkamp:color/m3_ref_palette_tertiary10 = 0x7f050143
com.tytfizikkamp:macro/m3_comp_progress_indicator_track_color = 0x7f0c00d5
com.tytfizikkamp:dimen/design_bottom_navigation_item_max_width = 0x7f060066
com.tytfizikkamp:color/m3_ref_palette_secondary90 = 0x7f05013f
com.tytfizikkamp:id/legacy = 0x7f0800eb
com.tytfizikkamp:dimen/m3_comp_input_chip_container_height = 0x7f060132
com.tytfizikkamp:dimen/design_bottom_navigation_label_padding = 0x7f060068
com.tytfizikkamp:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f110409
com.tytfizikkamp:attr/colorOnContainer = 0x7f0300ef
com.tytfizikkamp:color/m3_ref_palette_secondary60 = 0x7f05013c
com.tytfizikkamp:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f060194
com.tytfizikkamp:layout/design_menu_item_action_area = 0x7f0b0025
com.tytfizikkamp:attr/expandedTitleMarginBottom = 0x7f0301ab
com.tytfizikkamp:color/m3_ref_palette_secondary20 = 0x7f050138
com.tytfizikkamp:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f11009a
com.tytfizikkamp:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0c00b7
com.tytfizikkamp:color/m3_ref_palette_secondary10 = 0x7f050136
com.tytfizikkamp:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
com.tytfizikkamp:id/navigation_bar_item_active_indicator_view = 0x7f08012d
com.tytfizikkamp:color/m3_ref_palette_dynamic_tertiary70 = 0x7f0500f1
com.tytfizikkamp:color/m3_ref_palette_primary90 = 0x7f050132
com.tytfizikkamp:dimen/m3_badge_with_text_offset = 0x7f0600b9
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Body1 = 0x7f11001a
com.tytfizikkamp:attr/itemIconTint = 0x7f030237
com.tytfizikkamp:color/m3_ref_palette_primary50 = 0x7f05012e
com.tytfizikkamp:attr/flow_horizontalGap = 0x7f0301e0
com.tytfizikkamp:attr/startIconContentDescription = 0x7f0303c7
com.tytfizikkamp:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f060138
com.tytfizikkamp:color/m3_ref_palette_primary100 = 0x7f05012a
com.tytfizikkamp:dimen/material_clock_display_width = 0x7f060225
com.tytfizikkamp:styleable/RecyclerView = 0x7f120073
com.tytfizikkamp:color/m3_timepicker_time_input_stroke_color = 0x7f050212
com.tytfizikkamp:color/m3_sys_color_dark_surface_container_high = 0x7f050175
com.tytfizikkamp:color/m3_ref_palette_primary0 = 0x7f050128
com.tytfizikkamp:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f060292
com.tytfizikkamp:attr/fastScrollHorizontalThumbDrawable = 0x7f0301c6
com.tytfizikkamp:color/m3_ref_palette_neutral_variant99 = 0x7f050127
com.tytfizikkamp:color/m3_ref_palette_neutral_variant95 = 0x7f050126
com.tytfizikkamp:color/m3_ref_palette_neutral_variant80 = 0x7f050124
com.tytfizikkamp:color/m3_ref_palette_neutral_variant70 = 0x7f050123
com.tytfizikkamp:color/m3_ref_palette_neutral_variant60 = 0x7f050122
com.tytfizikkamp:color/m3_ref_palette_neutral_variant40 = 0x7f050120
com.tytfizikkamp:color/m3_ref_palette_neutral22 = 0x7f050109
com.tytfizikkamp:color/m3_ref_palette_neutral_variant100 = 0x7f05011d
com.tytfizikkamp:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f110321
com.tytfizikkamp:string/mtrl_picker_announce_current_range_selection = 0x7f100096
com.tytfizikkamp:color/m3_ref_palette_neutral98 = 0x7f050119
com.tytfizikkamp:color/m3_ref_palette_neutral96 = 0x7f050118
com.tytfizikkamp:attr/materialTimePickerTheme = 0x7f0302e5
com.tytfizikkamp:attr/fontVariationSettings = 0x7f0301f8
com.tytfizikkamp:color/m3_ref_palette_neutral95 = 0x7f050117
com.tytfizikkamp:attr/backgroundStacked = 0x7f030054
com.tytfizikkamp:color/material_on_surface_emphasis_high_type = 0x7f050276
com.tytfizikkamp:styleable/LinearLayoutCompat_Layout = 0x7f120049
com.tytfizikkamp:attr/labelBehavior = 0x7f030252
com.tytfizikkamp:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f060112
com.tytfizikkamp:color/m3_ref_palette_neutral94 = 0x7f050116
com.tytfizikkamp:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f060185
com.tytfizikkamp:color/m3_ref_palette_neutral90 = 0x7f050114
com.tytfizikkamp:dimen/mtrl_switch_thumb_elevation = 0x7f0602f8
com.tytfizikkamp:color/m3_ref_palette_neutral60 = 0x7f050110
com.tytfizikkamp:color/material_dynamic_primary10 = 0x7f05023e
com.tytfizikkamp:color/m3_sys_color_light_secondary_container = 0x7f0501e3
com.tytfizikkamp:color/m3_ref_palette_neutral6 = 0x7f05010f
com.tytfizikkamp:id/title = 0x7f0801ce
com.tytfizikkamp:dimen/mtrl_textinput_counter_margin_start = 0x7f060302
com.tytfizikkamp:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f110433
com.tytfizikkamp:dimen/mtrl_slider_tick_min_spacing = 0x7f0602ec
com.tytfizikkamp:dimen/m3_comp_navigation_rail_container_elevation = 0x7f060148
com.tytfizikkamp:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0c0169
com.tytfizikkamp:dimen/m3_comp_time_picker_container_elevation = 0x7f0601a1
com.tytfizikkamp:id/action_bar_subtitle = 0x7f080043
com.tytfizikkamp:attr/autoShowKeyboard = 0x7f03003e
com.tytfizikkamp:id/checked = 0x7f080078
com.tytfizikkamp:attr/autoSizePresetSizes = 0x7f030041
com.tytfizikkamp:color/m3_ref_palette_error99 = 0x7f050102
com.tytfizikkamp:id/TOP_END = 0x7f08000c
com.tytfizikkamp:style/Widget.Material3.DrawerLayout = 0x7f11039a
com.tytfizikkamp:color/m3_ref_palette_error70 = 0x7f0500fe
com.tytfizikkamp:attr/titleMarginEnd = 0x7f030466
com.tytfizikkamp:attr/clockNumberTextColor = 0x7f0300d0
com.tytfizikkamp:color/m3_ref_palette_error40 = 0x7f0500fb
com.tytfizikkamp:color/m3_ref_palette_error100 = 0x7f0500f8
com.tytfizikkamp:attr/barLength = 0x7f030068
com.tytfizikkamp:id/motion_base = 0x7f080114
com.tytfizikkamp:attr/shapeAppearanceSmallComponent = 0x7f0303a2
com.tytfizikkamp:color/m3_ref_palette_error0 = 0x7f0500f6
com.tytfizikkamp:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0600b2
com.tytfizikkamp:color/m3_ref_palette_dynamic_primary100 = 0x7f0500d1
com.tytfizikkamp:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f11046e
com.tytfizikkamp:color/m3_ref_palette_dynamic_tertiary99 = 0x7f0500f5
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1103b5
com.tytfizikkamp:color/m3_ref_palette_dynamic_tertiary80 = 0x7f0500f2
com.tytfizikkamp:string/material_clock_display_divider = 0x7f10006c
com.tytfizikkamp:color/m3_ref_palette_dynamic_tertiary50 = 0x7f0500ef
com.tytfizikkamp:id/focusCrop = 0x7f0800c5
com.tytfizikkamp:id/dragLeft = 0x7f0800a1
com.tytfizikkamp:color/m3_ref_palette_dynamic_tertiary30 = 0x7f0500ed
com.tytfizikkamp:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1100c9
com.tytfizikkamp:dimen/mtrl_high_ripple_focused_alpha = 0x7f0602bc
com.tytfizikkamp:dimen/m3_comp_search_view_container_elevation = 0x7f060174
com.tytfizikkamp:attr/errorAccessibilityLabel = 0x7f03019c
com.tytfizikkamp:color/m3_ref_palette_dynamic_tertiary20 = 0x7f0500ec
com.tytfizikkamp:color/m3_ref_palette_dynamic_tertiary100 = 0x7f0500eb
com.tytfizikkamp:color/m3_ref_palette_dynamic_tertiary0 = 0x7f0500e9
com.tytfizikkamp:dimen/material_textinput_max_width = 0x7f060243
com.tytfizikkamp:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1102bf
com.tytfizikkamp:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f11027b
com.tytfizikkamp:attr/layout_constraintGuide_begin = 0x7f03026e
com.tytfizikkamp:dimen/m3_comp_filled_button_container_elevation = 0x7f060123
com.tytfizikkamp:attr/drawPath = 0x7f030176
com.tytfizikkamp:attr/badgeStyle = 0x7f03005c
com.tytfizikkamp:color/design_dark_default_color_on_error = 0x7f050036
com.tytfizikkamp:color/m3_ref_palette_dynamic_secondary40 = 0x7f0500e1
com.tytfizikkamp:anim/design_snackbar_in = 0x7f010020
com.tytfizikkamp:color/m3_ref_palette_dynamic_secondary30 = 0x7f0500e0
com.tytfizikkamp:attr/brightness = 0x7f03008b
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0500c2
com.tytfizikkamp:attr/buttonTintMode = 0x7f03009b
com.tytfizikkamp:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1103d0
com.tytfizikkamp:dimen/m3_comp_badge_size = 0x7f060101
com.tytfizikkamp:color/m3_ref_palette_dynamic_primary95 = 0x7f0500da
com.tytfizikkamp:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f06012d
com.tytfizikkamp:color/m3_ref_palette_dynamic_primary70 = 0x7f0500d7
com.tytfizikkamp:attr/floatingActionButtonSmallTertiaryStyle = 0x7f0301d6
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f110167
com.tytfizikkamp:color/m3_ref_palette_dynamic_primary40 = 0x7f0500d4
com.tytfizikkamp:macro/m3_comp_filled_tonal_button_container_color = 0x7f0c0052
com.tytfizikkamp:color/mtrl_switch_thumb_icon_tint = 0x7f0502e0
com.tytfizikkamp:attr/layout_keyline = 0x7f030295
com.tytfizikkamp:color/m3_ref_palette_dynamic_primary30 = 0x7f0500d3
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant98 = 0x7f0500cd
com.tytfizikkamp:dimen/abc_edit_text_inset_top_material = 0x7f06002e
com.tytfizikkamp:color/m3_ref_palette_tertiary0 = 0x7f050142
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant96 = 0x7f0500cc
com.tytfizikkamp:attr/windowFixedHeightMinor = 0x7f0304a7
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant87 = 0x7f0500c7
com.tytfizikkamp:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f11010e
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant6 = 0x7f0500c3
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0500c1
com.tytfizikkamp:attr/flow_verticalStyle = 0x7f0301eb
com.tytfizikkamp:attr/tabIconTint = 0x7f0303f2
com.tytfizikkamp:attr/firstBaselineToTopHeight = 0x7f0301ca
com.tytfizikkamp:dimen/mtrl_btn_text_btn_padding_right = 0x7f06026f
com.tytfizikkamp:attr/textAppearanceHeadline4 = 0x7f03041e
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0500bc
com.tytfizikkamp:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f110085
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant17 = 0x7f0500bb
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0500b7
com.tytfizikkamp:dimen/material_cursor_inset = 0x7f060231
com.tytfizikkamp:color/m3_dynamic_dark_hint_foreground = 0x7f050080
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral99 = 0x7f0500b6
com.tytfizikkamp:attr/overlayImage = 0x7f03033b
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral94 = 0x7f0500b2
com.tytfizikkamp:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f11026d
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f1101eb
com.tytfizikkamp:color/mtrl_btn_text_btn_ripple_color = 0x7f0502bc
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral90 = 0x7f0500b0
com.tytfizikkamp:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0600c6
com.tytfizikkamp:attr/colorSecondary = 0x7f03010d
com.tytfizikkamp:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f110219
com.tytfizikkamp:attr/labelStyle = 0x7f030253
com.tytfizikkamp:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0c0034
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral87 = 0x7f0500af
com.tytfizikkamp:color/m3_navigation_rail_ripple_color_selector = 0x7f050099
com.tytfizikkamp:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0c00ab
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral40 = 0x7f0500a9
com.tytfizikkamp:dimen/m3_comp_slider_active_handle_leading_space = 0x7f060183
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral4 = 0x7f0500a8
com.tytfizikkamp:id/spread_inside = 0x7f08019d
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral12 = 0x7f0500a2
com.tytfizikkamp:macro/m3_comp_navigation_drawer_headline_color = 0x7f0c0084
com.tytfizikkamp:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f06018d
com.tytfizikkamp:anim/abc_slide_in_top = 0x7f010007
com.tytfizikkamp:dimen/tooltip_vertical_padding = 0x7f060321
com.tytfizikkamp:color/m3_ref_palette_black = 0x7f05009e
com.tytfizikkamp:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f06023b
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Chip = 0x7f110206
com.tytfizikkamp:integer/mtrl_chip_anim_duration = 0x7f090035
com.tytfizikkamp:attr/hoveredFocusedTranslationZ = 0x7f030216
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral0 = 0x7f05009f
com.tytfizikkamp:color/abc_background_cache_hint_selector_material_light = 0x7f050001
com.tytfizikkamp:attr/rippleColor = 0x7f03037b
com.tytfizikkamp:color/bright_foreground_inverse_material_light = 0x7f050024
com.tytfizikkamp:id/endToStart = 0x7f0800af
com.tytfizikkamp:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f050098
com.tytfizikkamp:color/m3_ref_palette_neutral20 = 0x7f050108
com.tytfizikkamp:color/m3_navigation_item_ripple_color = 0x7f050095
com.tytfizikkamp:macro/m3_comp_divider_color = 0x7f0c0028
com.tytfizikkamp:attr/textAppearanceDisplaySmall = 0x7f03041a
com.tytfizikkamp:dimen/mtrl_textinput_end_icon_margin_start = 0x7f060303
com.tytfizikkamp:color/m3_sys_color_dynamic_light_surface_container = 0x7f0501b8
com.tytfizikkamp:style/Widget.Design.BottomSheet.Modal = 0x7f11034e
com.tytfizikkamp:color/m3_navigation_item_icon_tint = 0x7f050094
com.tytfizikkamp:color/material_dynamic_neutral_variant10 = 0x7f050231
com.tytfizikkamp:color/m3_hint_foreground = 0x7f05008e
com.tytfizikkamp:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f110175
com.tytfizikkamp:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0c00c4
com.tytfizikkamp:attr/actionViewClass = 0x7f030023
com.tytfizikkamp:color/m3_highlighted_text = 0x7f05008d
com.tytfizikkamp:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
com.tytfizikkamp:attr/simpleItems = 0x7f0303b5
com.tytfizikkamp:attr/showMarker = 0x7f0303aa
com.tytfizikkamp:color/m3_fab_efab_background_color_selector = 0x7f050089
com.tytfizikkamp:color/m3_efab_ripple_color_selector = 0x7f050087
com.tytfizikkamp:dimen/m3_comp_bottom_app_bar_container_height = 0x7f060103
com.tytfizikkamp:color/material_on_primary_emphasis_high_type = 0x7f050273
com.tytfizikkamp:attr/checkMarkTintMode = 0x7f0300a9
com.tytfizikkamp:color/m3_dynamic_hint_foreground = 0x7f050085
com.tytfizikkamp:attr/layout_constraintRight_toRightOf = 0x7f03027d
com.tytfizikkamp:attr/cursorColor = 0x7f03014f
com.tytfizikkamp:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f1101fa
com.tytfizikkamp:color/design_dark_default_color_background = 0x7f050033
com.tytfizikkamp:color/m3_dynamic_default_color_primary_text = 0x7f050082
com.tytfizikkamp:dimen/abc_dialog_min_width_minor = 0x7f060023
com.tytfizikkamp:id/useLogo = 0x7f0801e6
com.tytfizikkamp:color/material_dynamic_neutral_variant30 = 0x7f050234
com.tytfizikkamp:attr/circularProgressIndicatorStyle = 0x7f0300cb
com.tytfizikkamp:color/m3_dynamic_dark_primary_text_disable_only = 0x7f050081
com.tytfizikkamp:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f070046
com.tytfizikkamp:style/Theme.Design.Light.NoActionBar = 0x7f110234
com.tytfizikkamp:attr/thumbColor = 0x7f030445
com.tytfizikkamp:color/m3_default_color_secondary_text = 0x7f05007c
com.tytfizikkamp:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f110191
com.tytfizikkamp:color/m3_default_color_primary_text = 0x7f05007b
com.tytfizikkamp:dimen/m3_bottomappbar_horizontal_padding = 0x7f0600cb
com.tytfizikkamp:id/mtrl_calendar_days_of_week = 0x7f080117
com.tytfizikkamp:dimen/mtrl_tooltip_arrowSize = 0x7f060307
com.tytfizikkamp:attr/tooltipText = 0x7f030477
com.tytfizikkamp:attr/buttonPanelSideLayout = 0x7f030097
com.tytfizikkamp:color/m3_dark_hint_foreground = 0x7f050079
com.tytfizikkamp:macro/m3_comp_input_chip_container_shape = 0x7f0c005b
com.tytfizikkamp:color/m3_dark_highlighted_text = 0x7f050078
com.tytfizikkamp:layout/mtrl_picker_header_toggle = 0x7f0b0060
com.tytfizikkamp:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
com.tytfizikkamp:attr/colorSurfaceContainerHighest = 0x7f030116
com.tytfizikkamp:attr/flow_verticalBias = 0x7f0301e9
com.tytfizikkamp:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f060158
com.tytfizikkamp:color/m3_chip_stroke_color = 0x7f050074
com.tytfizikkamp:color/m3_sys_color_light_on_surface = 0x7f0501da
com.tytfizikkamp:id/mtrl_picker_text_input_range_start = 0x7f080129
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant12 = 0x7f0500ba
com.tytfizikkamp:attr/layout_goneMarginLeft = 0x7f030290
com.tytfizikkamp:style/Widget.Material3.CheckedTextView = 0x7f11037f
com.tytfizikkamp:attr/dividerHorizontal = 0x7f03016d
com.tytfizikkamp:style/Widget.AppCompat.DrawerArrowToggle = 0x7f110311
com.tytfizikkamp:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f06023d
com.tytfizikkamp:attr/textAppearanceListItemSecondary = 0x7f03042a
com.tytfizikkamp:color/m3_primary_text_disable_only = 0x7f05009b
com.tytfizikkamp:attr/tabIndicatorGravity = 0x7f0303f9
com.tytfizikkamp:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1102be
com.tytfizikkamp:attr/materialTimePickerTitleStyle = 0x7f0302e6
com.tytfizikkamp:color/m3_card_stroke_color = 0x7f05006e
com.tytfizikkamp:dimen/m3_searchbar_elevation = 0x7f0601db
com.tytfizikkamp:drawable/abc_spinner_mtrl_am_alpha = 0x7f070065
com.tytfizikkamp:attr/itemTextAppearanceActive = 0x7f030249
com.tytfizikkamp:drawable/ic_m3_chip_check = 0x7f070091
com.tytfizikkamp:color/m3_button_foreground_color_selector = 0x7f050066
com.tytfizikkamp:styleable/ThemeEnforcement = 0x7f12008c
com.tytfizikkamp:attr/textAppearancePopupMenuHeader = 0x7f03042d
com.tytfizikkamp:dimen/m3_sys_elevation_level1 = 0x7f0601f2
com.tytfizikkamp:color/m3_ref_palette_dynamic_tertiary90 = 0x7f0500f3
com.tytfizikkamp:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0602ae
com.tytfizikkamp:attr/textAppearanceBodyLarge = 0x7f030413
com.tytfizikkamp:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f110099
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral95 = 0x7f0500b3
com.tytfizikkamp:style/Widget.Autofill.InlineSuggestionStartIconStyle = 0x7f110347
com.tytfizikkamp:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1101b2
com.tytfizikkamp:color/m3_appbar_overlay_color = 0x7f050061
com.tytfizikkamp:style/Widget.AppCompat.Light.ActionButton = 0x7f11031e
com.tytfizikkamp:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0601cd
com.tytfizikkamp:anim/m3_bottom_sheet_slide_out = 0x7f010028
com.tytfizikkamp:color/error_color_material_light = 0x7f05005c
com.tytfizikkamp:color/design_dark_default_color_on_primary = 0x7f050037
com.tytfizikkamp:color/m3_ref_palette_tertiary20 = 0x7f050145
com.tytfizikkamp:color/m3_navigation_bar_ripple_color_selector = 0x7f050092
com.tytfizikkamp:color/m3_sys_color_dark_outline_variant = 0x7f05016d
com.tytfizikkamp:color/dim_foreground_material_light = 0x7f05005a
com.tytfizikkamp:id/action_bar_container = 0x7f080040
com.tytfizikkamp:color/m3_sys_color_light_on_background = 0x7f0501d3
com.tytfizikkamp:color/material_dynamic_neutral20 = 0x7f050226
com.tytfizikkamp:color/m3_ref_palette_primary80 = 0x7f050131
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral70 = 0x7f0500ad
com.tytfizikkamp:color/dim_foreground_disabled_material_light = 0x7f050058
com.tytfizikkamp:drawable/mtrl_popupmenu_background = 0x7f0700c6
com.tytfizikkamp:color/design_icon_tint = 0x7f050055
com.tytfizikkamp:attr/dropdownListPreferredItemHeight = 0x7f030185
com.tytfizikkamp:color/design_fab_stroke_top_outer_color = 0x7f050054
com.tytfizikkamp:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1100dd
com.tytfizikkamp:color/material_slider_active_tick_marks_color = 0x7f0502ad
com.tytfizikkamp:attr/iconifiedByDefault = 0x7f03021f
com.tytfizikkamp:color/m3_elevated_chip_background_color = 0x7f050088
com.tytfizikkamp:color/design_fab_stroke_end_inner_color = 0x7f050051
com.tytfizikkamp:style/TextAppearance.Material3.LabelSmall = 0x7f1101f9
com.tytfizikkamp:color/design_fab_shadow_start_color = 0x7f050050
com.tytfizikkamp:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f060201
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0500ce
com.tytfizikkamp:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0c00fe
com.tytfizikkamp:attr/defaultDuration = 0x7f03015f
com.tytfizikkamp:attr/nestedScrollViewStyle = 0x7f03032e
com.tytfizikkamp:dimen/m3_comp_slider_stop_indicator_size = 0x7f060189
com.tytfizikkamp:color/design_fab_shadow_end_color = 0x7f05004e
com.tytfizikkamp:styleable/MotionScene = 0x7f120065
com.tytfizikkamp:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f0602f1
com.tytfizikkamp:color/design_default_color_surface = 0x7f05004c
com.tytfizikkamp:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1100ae
com.tytfizikkamp:id/match_parent = 0x7f0800f4
com.tytfizikkamp:attr/actionProviderClass = 0x7f030021
com.tytfizikkamp:attr/actionBarDivider = 0x7f030000
com.tytfizikkamp:color/design_default_color_primary_variant = 0x7f050049
com.tytfizikkamp:color/material_personalized_color_primary_container = 0x7f050290
com.tytfizikkamp:attr/buttonBarNegativeButtonStyle = 0x7f03008d
com.tytfizikkamp:color/m3_ref_palette_primary30 = 0x7f05012c
com.tytfizikkamp:attr/foregroundInsidePadding = 0x7f0301fc
com.tytfizikkamp:attr/roundTopStart = 0x7f030386
com.tytfizikkamp:color/design_default_color_primary_dark = 0x7f050048
com.tytfizikkamp:layout/design_text_input_start_icon = 0x7f0b002d
com.tytfizikkamp:dimen/fastscroll_minimum_range = 0x7f060093
com.tytfizikkamp:color/m3_dynamic_dark_default_color_secondary_text = 0x7f05007e
com.tytfizikkamp:attr/lineSpacing = 0x7f03029f
com.tytfizikkamp:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1100f1
com.tytfizikkamp:color/design_default_color_primary = 0x7f050047
com.tytfizikkamp:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1101d2
com.tytfizikkamp:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f07007f
com.tytfizikkamp:macro/m3_comp_filled_button_label_text_type = 0x7f0c0045
com.tytfizikkamp:color/material_personalized_color_on_error = 0x7f050282
com.tytfizikkamp:dimen/clock_face_margin_start = 0x7f060056
com.tytfizikkamp:attr/layout_goneMarginRight = 0x7f030291
com.tytfizikkamp:color/design_default_color_on_surface = 0x7f050046
com.tytfizikkamp:attr/listPreferredItemPaddingRight = 0x7f0302ae
com.tytfizikkamp:color/design_default_color_on_background = 0x7f050042
com.tytfizikkamp:id/uniform = 0x7f0801e3
com.tytfizikkamp:attr/logoScaleType = 0x7f0302b3
com.tytfizikkamp:attr/layout_constraintWidth_default = 0x7f030287
com.tytfizikkamp:color/m3_ref_palette_dynamic_secondary50 = 0x7f0500e2
com.tytfizikkamp:attr/layout_scrollFlags = 0x7f030298
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral98 = 0x7f0500b5
com.tytfizikkamp:color/design_dark_default_color_secondary_variant = 0x7f05003e
com.tytfizikkamp:color/design_dark_default_color_secondary = 0x7f05003d
com.tytfizikkamp:attr/checkedState = 0x7f0300b4
com.tytfizikkamp:styleable/MenuGroup = 0x7f12005e
com.tytfizikkamp:attr/layout_constraintStart_toEndOf = 0x7f03027e
com.tytfizikkamp:color/design_dark_default_color_primary_dark = 0x7f05003b
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral22 = 0x7f0500a5
com.tytfizikkamp:color/design_dark_default_color_primary = 0x7f05003a
com.tytfizikkamp:anim/m3_bottom_sheet_slide_in = 0x7f010027
com.tytfizikkamp:attr/percentWidth = 0x7f030350
com.tytfizikkamp:color/design_dark_default_color_error = 0x7f050034
com.tytfizikkamp:color/design_box_stroke_color = 0x7f050032
com.tytfizikkamp:attr/state_dragged = 0x7f0303d0
com.tytfizikkamp:color/design_bottom_navigation_shadow_color = 0x7f050031
com.tytfizikkamp:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f120031
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f110444
com.tytfizikkamp:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0c0017
com.tytfizikkamp:attr/colorTertiaryContainer = 0x7f03011e
com.tytfizikkamp:animator/fragment_close_enter = 0x7f020003
com.tytfizikkamp:color/catalyst_logbox_background = 0x7f05002f
com.tytfizikkamp:macro/m3_comp_time_picker_container_shape = 0x7f0c014f
com.tytfizikkamp:attr/layout = 0x7f030258
com.tytfizikkamp:animator/m3_extended_fab_show_motion_spec = 0x7f020013
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f1102e8
com.tytfizikkamp:color/cardview_shadow_end_color = 0x7f05002d
com.tytfizikkamp:drawable/mtrl_navigation_bar_item_background = 0x7f0700c5
com.tytfizikkamp:styleable/Badge = 0x7f120014
com.tytfizikkamp:id/view_tree_lifecycle_owner = 0x7f0801ed
com.tytfizikkamp:attr/waveOffset = 0x7f03049f
com.tytfizikkamp:dimen/mtrl_btn_text_btn_padding_left = 0x7f06026e
com.tytfizikkamp:dimen/m3_extended_fab_end_padding = 0x7f0601b1
com.tytfizikkamp:attr/crossfade = 0x7f03014d
com.tytfizikkamp:id/month_navigation_fragment_toggle = 0x7f080110
com.tytfizikkamp:color/button_material_dark = 0x7f050027
com.tytfizikkamp:id/ghost_view_holder = 0x7f0800cb
com.tytfizikkamp:color/m3_text_button_background_color_selector = 0x7f050201
com.tytfizikkamp:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1100a7
com.tytfizikkamp:attr/circleRadius = 0x7f0300ca
com.tytfizikkamp:attr/buttonCompat = 0x7f030091
com.tytfizikkamp:color/bright_foreground_disabled_material_dark = 0x7f050021
com.tytfizikkamp:dimen/mtrl_shape_corner_size_medium_component = 0x7f0602e4
com.tytfizikkamp:color/background_material_dark = 0x7f05001f
com.tytfizikkamp:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0c013f
com.tytfizikkamp:id/material_clock_level = 0x7f0800f9
com.tytfizikkamp:color/material_dynamic_neutral_variant20 = 0x7f050233
com.tytfizikkamp:id/material_value_index = 0x7f080108
com.tytfizikkamp:color/background_floating_material_dark = 0x7f05001d
com.tytfizikkamp:style/TextAppearance.Material3.TitleSmall = 0x7f110200
com.tytfizikkamp:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
com.tytfizikkamp:id/accessibility_custom_action_3 = 0x7f08002a
com.tytfizikkamp:color/abc_tint_spinner = 0x7f050017
com.tytfizikkamp:color/material_blue_grey_800 = 0x7f050214
com.tytfizikkamp:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0c00b8
com.tytfizikkamp:color/abc_tint_seek_thumb = 0x7f050016
com.tytfizikkamp:color/abc_secondary_text_material_dark = 0x7f050011
com.tytfizikkamp:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1100d3
com.tytfizikkamp:attr/colorSwitchThumbNormal = 0x7f03011c
com.tytfizikkamp:color/abc_search_url_text = 0x7f05000d
com.tytfizikkamp:id/accessibility_custom_action_22 = 0x7f080022
com.tytfizikkamp:anim/rns_fade_in = 0x7f010037
com.tytfizikkamp:color/abc_primary_text_disable_only_material_light = 0x7f05000a
com.tytfizikkamp:attr/titlePositionInterpolator = 0x7f03046a
com.tytfizikkamp:dimen/compat_control_corner_material = 0x7f06005b
com.tytfizikkamp:color/abc_hint_foreground_material_dark = 0x7f050007
com.tytfizikkamp:style/TextAppearance.Material3.BodySmall = 0x7f1101f0
com.tytfizikkamp:attr/startIconDrawable = 0x7f0303c8
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_on_primary = 0x7f050186
com.tytfizikkamp:attr/layout_constraintTag = 0x7f030280
com.tytfizikkamp:id/clockwise = 0x7f08007e
com.tytfizikkamp:color/abc_decor_view_status_guard_light = 0x7f050006
com.tytfizikkamp:drawable/mtrl_bottomsheet_drag_handle = 0x7f0700b0
com.tytfizikkamp:animator/m3_chip_state_list_anim = 0x7f02000e
com.tytfizikkamp:attr/shortcutMatchRequired = 0x7f0303a4
com.tytfizikkamp:color/abc_color_highlight_material = 0x7f050004
com.tytfizikkamp:color/abc_btn_colored_text_material = 0x7f050003
com.tytfizikkamp:color/abc_secondary_text_material_light = 0x7f050012
com.tytfizikkamp:bool/mtrl_btn_textappearance_all_caps = 0x7f040002
com.tytfizikkamp:attr/colorBackgroundFloating = 0x7f0300e6
com.tytfizikkamp:id/material_timepicker_cancel_button = 0x7f080103
com.tytfizikkamp:attr/motionDebug = 0x7f030300
com.tytfizikkamp:string/side_sheet_accessibility_pane_title = 0x7f1000d1
com.tytfizikkamp:string/abc_menu_space_shortcut_label = 0x7f10000f
com.tytfizikkamp:attr/waveShape = 0x7f0304a1
com.tytfizikkamp:attr/shapeCornerFamily = 0x7f0303a3
com.tytfizikkamp:attr/shouldRemoveExpandedCorners = 0x7f0303a5
com.tytfizikkamp:attr/yearTodayStyle = 0x7f0304af
com.tytfizikkamp:color/material_dynamic_tertiary40 = 0x7f05025c
com.tytfizikkamp:color/cardview_dark_background = 0x7f05002b
com.tytfizikkamp:attr/yearStyle = 0x7f0304ae
com.tytfizikkamp:attr/paddingRightSystemWindowInsets = 0x7f030340
com.tytfizikkamp:anim/abc_tooltip_enter = 0x7f01000a
com.tytfizikkamp:attr/windowFixedWidthMajor = 0x7f0304a8
com.tytfizikkamp:color/material_slider_inactive_track_color = 0x7f0502b1
com.tytfizikkamp:dimen/m3_datepicker_elevation = 0x7f0601ae
com.tytfizikkamp:attr/tabIndicatorColor = 0x7f0303f7
com.tytfizikkamp:attr/windowActionModeOverlay = 0x7f0304a5
com.tytfizikkamp:attr/liftOnScroll = 0x7f03029a
com.tytfizikkamp:color/m3_ref_palette_secondary100 = 0x7f050137
com.tytfizikkamp:dimen/notification_main_column_padding_top = 0x7f060313
com.tytfizikkamp:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f1102a9
com.tytfizikkamp:attr/windowActionBarOverlay = 0x7f0304a4
com.tytfizikkamp:attr/waveVariesBy = 0x7f0304a2
com.tytfizikkamp:styleable/KeyFramesVelocity = 0x7f120043
com.tytfizikkamp:id/decelerate = 0x7f08008e
com.tytfizikkamp:attr/layout_collapseMode = 0x7f03025f
com.tytfizikkamp:attr/viewInflaterClass = 0x7f03049a
com.tytfizikkamp:attr/verticalOffset = 0x7f030497
com.tytfizikkamp:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f110138
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant94 = 0x7f0500ca
com.tytfizikkamp:attr/counterTextAppearance = 0x7f03014b
com.tytfizikkamp:color/material_personalized_color_surface_bright = 0x7f050299
com.tytfizikkamp:attr/values = 0x7f030496
com.tytfizikkamp:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f110063
com.tytfizikkamp:drawable/abc_spinner_textfield_background_material = 0x7f070066
com.tytfizikkamp:dimen/m3_side_sheet_margin_detached = 0x7f0601e6
com.tytfizikkamp:attr/useDrawerArrowDrawable = 0x7f030494
com.tytfizikkamp:attr/triggerId = 0x7f03048f
com.tytfizikkamp:attr/state_liftable = 0x7f0303d3
com.tytfizikkamp:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f110052
com.tytfizikkamp:attr/transitionPathRotate = 0x7f03048d
com.tytfizikkamp:color/material_personalized_hint_foreground_inverse = 0x7f0502aa
com.tytfizikkamp:anim/mtrl_card_lowers_interpolator = 0x7f010031
com.tytfizikkamp:attr/transitionShapeAppearance = 0x7f03048e
com.tytfizikkamp:attr/motion_postLayoutCollision = 0x7f030323
com.tytfizikkamp:attr/transitionEasing = 0x7f03048b
com.tytfizikkamp:style/ThemeOverlay.Material3.BottomAppBar = 0x7f11029a
com.tytfizikkamp:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0c00b5
com.tytfizikkamp:attr/warmth = 0x7f03049d
com.tytfizikkamp:attr/trackTint = 0x7f030488
com.tytfizikkamp:animator/mtrl_card_state_list_anim = 0x7f020017
com.tytfizikkamp:attr/trackThickness = 0x7f030487
com.tytfizikkamp:styleable/LinearLayoutCompat = 0x7f120048
com.tytfizikkamp:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1102ba
com.tytfizikkamp:attr/showPaths = 0x7f0303ac
com.tytfizikkamp:attr/trackHeight = 0x7f030484
com.tytfizikkamp:color/m3_ref_palette_neutral100 = 0x7f050105
com.tytfizikkamp:id/fullscreen_header = 0x7f0800c9
com.tytfizikkamp:attr/trackDecorationTintMode = 0x7f030483
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f11044b
com.tytfizikkamp:color/m3_ref_palette_dynamic_primary99 = 0x7f0500db
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0500c8
com.tytfizikkamp:attr/actionModeCloseContentDescription = 0x7f030012
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f1101e8
com.tytfizikkamp:string/catalyst_open_debugger_error = 0x7f100041
com.tytfizikkamp:id/search_src_text = 0x7f080188
com.tytfizikkamp:animator/m3_appbar_state_list_animator = 0x7f020009
com.tytfizikkamp:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0c0107
com.tytfizikkamp:attr/lastItemDecorated = 0x7f030257
com.tytfizikkamp:attr/textAppearanceBody1 = 0x7f030411
com.tytfizikkamp:attr/trackDecoration = 0x7f030481
com.tytfizikkamp:styleable/FlowLayout = 0x7f120034
com.tytfizikkamp:attr/trackCornerRadius = 0x7f030480
com.tytfizikkamp:macro/m3_comp_radio_button_selected_icon_color = 0x7f0c00dc
com.tytfizikkamp:attr/trackColorInactive = 0x7f03047f
com.tytfizikkamp:id/triangle = 0x7f0801e1
com.tytfizikkamp:id/SHOW_PROGRESS = 0x7f08000a
com.tytfizikkamp:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f070014
com.tytfizikkamp:color/call_notification_decline_color = 0x7f05002a
com.tytfizikkamp:attr/elevationOverlayColor = 0x7f03018d
com.tytfizikkamp:id/rn_redbox_report_label = 0x7f080171
com.tytfizikkamp:id/accessibility_custom_action_28 = 0x7f080028
com.tytfizikkamp:id/accessibility_value = 0x7f08003d
com.tytfizikkamp:attr/flow_lastHorizontalBias = 0x7f0301e2
com.tytfizikkamp:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f100062
com.tytfizikkamp:attr/layout_goneMarginEnd = 0x7f03028f
com.tytfizikkamp:attr/touchRegionId = 0x7f03047b
com.tytfizikkamp:color/m3_ref_palette_neutral10 = 0x7f050104
com.tytfizikkamp:color/m3_ref_palette_secondary99 = 0x7f050141
com.tytfizikkamp:macro/m3_comp_search_view_header_input_text_type = 0x7f0c00f5
com.tytfizikkamp:attr/keyPositionType = 0x7f03024e
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar = 0x7f110438
com.tytfizikkamp:attr/windowMinWidthMinor = 0x7f0304ab
com.tytfizikkamp:color/androidx_core_ripple_material_light = 0x7f05001b
com.tytfizikkamp:attr/cornerSizeBottomRight = 0x7f030144
com.tytfizikkamp:attr/chipSurfaceColor = 0x7f0300c9
com.tytfizikkamp:attr/touchAnchorSide = 0x7f03047a
com.tytfizikkamp:layout/mtrl_auto_complete_simple_item = 0x7f0b004b
com.tytfizikkamp:color/material_grey_600 = 0x7f050267
com.tytfizikkamp:layout/alert_title_layout = 0x7f0b001c
com.tytfizikkamp:id/NO_DEBUG = 0x7f080006
com.tytfizikkamp:color/material_dynamic_tertiary90 = 0x7f050261
com.tytfizikkamp:attr/topInsetScrimEnabled = 0x7f030478
com.tytfizikkamp:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f110365
com.tytfizikkamp:attr/imageButtonStyle = 0x7f030220
com.tytfizikkamp:attr/tooltipFrameBackground = 0x7f030475
com.tytfizikkamp:color/material_grey_50 = 0x7f050266
com.tytfizikkamp:color/abc_tint_default = 0x7f050014
com.tytfizikkamp:dimen/mtrl_alert_dialog_background_inset_end = 0x7f060248
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f110443
com.tytfizikkamp:id/spacer = 0x7f080198
com.tytfizikkamp:attr/titleTextStyle = 0x7f03046e
com.tytfizikkamp:style/TextAppearance.AppCompat.Button = 0x7f1101a0
com.tytfizikkamp:color/m3_ref_palette_neutral87 = 0x7f050113
com.tytfizikkamp:attr/theme = 0x7f030443
com.tytfizikkamp:color/material_dynamic_color_light_error_container = 0x7f050220
com.tytfizikkamp:color/material_personalized__highlighted_text = 0x7f050279
com.tytfizikkamp:attr/titleTextAppearance = 0x7f03046b
com.tytfizikkamp:color/secondary_text_default_material_dark = 0x7f0502fb
com.tytfizikkamp:attr/titleCentered = 0x7f030461
com.tytfizikkamp:drawable/test_level_drawable = 0x7f0700ee
com.tytfizikkamp:attr/title = 0x7f030460
com.tytfizikkamp:dimen/design_navigation_max_width = 0x7f06007c
com.tytfizikkamp:color/design_default_color_secondary = 0x7f05004a
com.tytfizikkamp:style/Widget.Material3.BottomSheet = 0x7f110366
com.tytfizikkamp:attr/collapsingToolbarLayoutMediumSize = 0x7f0300e1
com.tytfizikkamp:color/abc_search_url_text_selected = 0x7f050010
com.tytfizikkamp:drawable/ic_clock_black_24dp = 0x7f07008f
com.tytfizikkamp:dimen/abc_dialog_fixed_height_major = 0x7f06001c
com.tytfizikkamp:color/bright_foreground_disabled_material_light = 0x7f050022
com.tytfizikkamp:attr/tickVisible = 0x7f03045c
com.tytfizikkamp:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f1103f9
com.tytfizikkamp:dimen/m3_searchbar_padding_start = 0x7f0601e0
com.tytfizikkamp:attr/titleEnabled = 0x7f030463
com.tytfizikkamp:dimen/hint_alpha_material_light = 0x7f060098
com.tytfizikkamp:dimen/fastscroll_default_thickness = 0x7f060091
com.tytfizikkamp:id/dialog_button = 0x7f080098
com.tytfizikkamp:attr/titleTextColor = 0x7f03046c
com.tytfizikkamp:drawable/$m3_avd_hide_password__0 = 0x7f070006
com.tytfizikkamp:attr/tickRadiusInactive = 0x7f03045b
com.tytfizikkamp:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f0501a2
com.tytfizikkamp:attr/shapeAppearanceCornerSmall = 0x7f03039e
com.tytfizikkamp:attr/tickRadiusActive = 0x7f03045a
com.tytfizikkamp:color/m3_ref_palette_neutral50 = 0x7f05010e
com.tytfizikkamp:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f050210
com.tytfizikkamp:color/m3_ref_palette_error60 = 0x7f0500fd
com.tytfizikkamp:dimen/mtrl_badge_size = 0x7f06024e
com.tytfizikkamp:attr/contentPaddingLeft = 0x7f030133
com.tytfizikkamp:color/m3_ref_palette_dynamic_secondary80 = 0x7f0500e5
com.tytfizikkamp:string/abc_search_hint = 0x7f100012
com.tytfizikkamp:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0c00db
com.tytfizikkamp:id/wrap = 0x7f0801f6
com.tytfizikkamp:id/design_menu_item_text = 0x7f080096
com.tytfizikkamp:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0501be
com.tytfizikkamp:attr/tickColorInactive = 0x7f030456
com.tytfizikkamp:id/direct = 0x7f08009a
com.tytfizikkamp:attr/tickColorActive = 0x7f030455
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f050193
com.tytfizikkamp:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f110092
com.tytfizikkamp:color/primary_text_default_material_dark = 0x7f0502f5
com.tytfizikkamp:color/m3_ref_palette_dynamic_primary80 = 0x7f0500d8
com.tytfizikkamp:dimen/m3_navigation_rail_label_padding_horizontal = 0x7f0601d5
com.tytfizikkamp:attr/thumbRadius = 0x7f03044c
com.tytfizikkamp:color/m3_sys_color_light_background = 0x7f0501cd
com.tytfizikkamp:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f11042d
com.tytfizikkamp:attr/thumbIconTint = 0x7f03044a
com.tytfizikkamp:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0c0142
com.tytfizikkamp:color/material_personalized_color_on_surface = 0x7f050288
com.tytfizikkamp:id/m3_side_sheet = 0x7f0800f1
com.tytfizikkamp:attr/progressBarStyle = 0x7f030369
com.tytfizikkamp:color/m3_chip_background_color = 0x7f050072
com.tytfizikkamp:attr/popupMenuStyle = 0x7f03035c
com.tytfizikkamp:color/background_floating_material_light = 0x7f05001e
com.tytfizikkamp:attr/fabCradleVerticalOffset = 0x7f0301bf
com.tytfizikkamp:attr/thumbElevation = 0x7f030446
com.tytfizikkamp:attr/thickness = 0x7f030444
com.tytfizikkamp:attr/roundTopRight = 0x7f030385
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1102e3
com.tytfizikkamp:attr/passwordToggleContentDescription = 0x7f030348
com.tytfizikkamp:attr/textStartPadding = 0x7f030442
com.tytfizikkamp:color/bright_foreground_inverse_material_dark = 0x7f050023
com.tytfizikkamp:color/material_personalized_color_on_tertiary = 0x7f05028b
com.tytfizikkamp:attr/textInputStyle = 0x7f030440
com.tytfizikkamp:attr/textInputOutlinedStyle = 0x7f03043f
com.tytfizikkamp:drawable/abc_seekbar_thumb_material = 0x7f070062
com.tytfizikkamp:id/expand_activities_button = 0x7f0800b4
com.tytfizikkamp:attr/materialIconButtonFilledStyle = 0x7f0302d9
com.tytfizikkamp:attr/textInputLayoutFocusedRectEnabled = 0x7f03043c
com.tytfizikkamp:attr/materialTimePickerStyle = 0x7f0302e4
com.tytfizikkamp:dimen/design_snackbar_action_text_color_alpha = 0x7f060080
com.tytfizikkamp:style/Widget.AppCompat.RatingBar.Small = 0x7f110337
com.tytfizikkamp:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0600ae
com.tytfizikkamp:attr/textInputFilledExposedDropdownMenuStyle = 0x7f03043a
com.tytfizikkamp:attr/textInputFilledDenseStyle = 0x7f030439
com.tytfizikkamp:dimen/material_input_text_to_prefix_suffix_padding = 0x7f060241
com.tytfizikkamp:color/m3_textfield_filled_background_color = 0x7f050204
com.tytfizikkamp:attr/actionBarStyle = 0x7f030005
com.tytfizikkamp:attr/strokeWidth = 0x7f0303da
com.tytfizikkamp:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f11041a
com.tytfizikkamp:attr/textAppearanceSearchResultTitle = 0x7f03042f
com.tytfizikkamp:attr/textAppearanceSearchResultSubtitle = 0x7f03042e
com.tytfizikkamp:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1102c4
com.tytfizikkamp:attr/textAppearanceHeadlineMedium = 0x7f030422
com.tytfizikkamp:id/normal = 0x7f080137
com.tytfizikkamp:attr/layout_insetEdge = 0x7f030294
com.tytfizikkamp:attr/textAppearanceListItem = 0x7f030429
com.tytfizikkamp:dimen/m3_appbar_scrim_height_trigger = 0x7f0600a7
com.tytfizikkamp:id/top = 0x7f0801d2
com.tytfizikkamp:attr/layout_constraintWidth_max = 0x7f030288
com.tytfizikkamp:attr/textAppearanceHeadlineSmall = 0x7f030423
com.tytfizikkamp:dimen/compat_button_inset_horizontal_material = 0x7f060057
com.tytfizikkamp:attr/textAppearanceHeadline6 = 0x7f030420
com.tytfizikkamp:attr/textAppearanceHeadline3 = 0x7f03041d
com.tytfizikkamp:color/m3_slider_inactive_track_color = 0x7f050155
com.tytfizikkamp:attr/backgroundColor = 0x7f03004c
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f110168
com.tytfizikkamp:attr/textAppearanceHeadline1 = 0x7f03041b
com.tytfizikkamp:attr/materialDividerHeavyStyle = 0x7f0302d7
com.tytfizikkamp:drawable/abc_text_select_handle_middle_mtrl = 0x7f07006f
com.tytfizikkamp:integer/m3_sys_shape_corner_medium_corner_family = 0x7f090024
com.tytfizikkamp:attr/textAppearanceDisplayLarge = 0x7f030418
com.tytfizikkamp:attr/textAppearanceCaption = 0x7f030417
com.tytfizikkamp:color/highlighted_text_material_light = 0x7f050060
com.tytfizikkamp:color/material_dynamic_neutral95 = 0x7f05022e
com.tytfizikkamp:attr/textAppearanceBodySmall = 0x7f030415
com.tytfizikkamp:attr/chainUseRtl = 0x7f0300a6
com.tytfizikkamp:attr/textAppearanceBodyMedium = 0x7f030414
com.tytfizikkamp:drawable/btn_checkbox_checked_mtrl = 0x7f07007a
com.tytfizikkamp:id/scrollIndicatorUp = 0x7f08017d
com.tytfizikkamp:attr/fontWeight = 0x7f0301f9
com.tytfizikkamp:attr/telltales_velocityMode = 0x7f03040f
com.tytfizikkamp:attr/thumbStrokeColor = 0x7f03044d
com.tytfizikkamp:attr/telltales_tailScale = 0x7f03040e
com.tytfizikkamp:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f070025
com.tytfizikkamp:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f06017c
com.tytfizikkamp:drawable/notification_template_icon_low_bg = 0x7f0700e5
com.tytfizikkamp:attr/behavior_expandedOffset = 0x7f03006f
com.tytfizikkamp:style/Widget.Material3.CollapsingToolbar.Large = 0x7f110394
com.tytfizikkamp:attr/telltales_tailColor = 0x7f03040d
com.tytfizikkamp:attr/selectableItemBackground = 0x7f030395
com.tytfizikkamp:attr/selectorSize = 0x7f030398
com.tytfizikkamp:attr/targetId = 0x7f03040c
com.tytfizikkamp:attr/windowFixedWidthMinor = 0x7f0304a9
com.tytfizikkamp:attr/barrierDirection = 0x7f03006a
com.tytfizikkamp:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0c014a
com.tytfizikkamp:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0c00b3
com.tytfizikkamp:attr/flow_verticalAlign = 0x7f0301e8
com.tytfizikkamp:attr/triggerSlack = 0x7f030491
com.tytfizikkamp:attr/tabTextAppearance = 0x7f030409
com.tytfizikkamp:color/material_dynamic_tertiary30 = 0x7f05025b
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral50 = 0x7f0500aa
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral10 = 0x7f0500a0
com.tytfizikkamp:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f110391
com.tytfizikkamp:attr/drawerLayoutStyle = 0x7f030182
com.tytfizikkamp:attr/tabSelectedTextColor = 0x7f030407
com.tytfizikkamp:styleable/FloatingActionButton = 0x7f120032
com.tytfizikkamp:macro/m3_comp_navigation_drawer_modal_container_color = 0x7f0c0092
com.tytfizikkamp:macro/m3_comp_fab_primary_container_shape = 0x7f0c0037
com.tytfizikkamp:attr/contentInsetStart = 0x7f03012e
com.tytfizikkamp:attr/tabRippleColor = 0x7f030404
com.tytfizikkamp:attr/checkboxStyle = 0x7f0300aa
com.tytfizikkamp:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f06010a
com.tytfizikkamp:attr/useCompatPadding = 0x7f030493
com.tytfizikkamp:drawable/material_ic_edit_black_24dp = 0x7f0700a9
com.tytfizikkamp:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f110462
com.tytfizikkamp:dimen/m3_slider_thumb_elevation = 0x7f0601ec
com.tytfizikkamp:color/m3_ref_palette_neutral_variant10 = 0x7f05011c
com.tytfizikkamp:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f1102ab
com.tytfizikkamp:attr/tabPaddingTop = 0x7f030403
com.tytfizikkamp:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f110405
com.tytfizikkamp:id/open_search_view_status_bar_spacer = 0x7f080148
com.tytfizikkamp:color/m3_sys_color_light_on_tertiary = 0x7f0501dc
com.tytfizikkamp:anim/rns_slide_out_to_left = 0x7f01004a
com.tytfizikkamp:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0c016c
com.tytfizikkamp:attr/tabMode = 0x7f0303fe
com.tytfizikkamp:attr/tabIndicatorHeight = 0x7f0303fa
com.tytfizikkamp:dimen/m3_large_fab_size = 0x7f0601bb
com.tytfizikkamp:attr/tabIndicatorFullWidth = 0x7f0303f8
com.tytfizikkamp:color/abc_tint_btn_checkable = 0x7f050013
com.tytfizikkamp:attr/tabIndicatorAnimationMode = 0x7f0303f6
com.tytfizikkamp:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f1102a5
com.tytfizikkamp:dimen/m3_navigation_rail_icon_size = 0x7f0601cc
com.tytfizikkamp:color/m3_button_background_color_selector = 0x7f050065
com.tytfizikkamp:id/view_tag_instance_handle = 0x7f0801ea
com.tytfizikkamp:attr/autofillInlineSuggestionTitle = 0x7f030049
com.tytfizikkamp:id/transition_pause_alpha = 0x7f0801dd
com.tytfizikkamp:id/design_bottom_sheet = 0x7f080093
com.tytfizikkamp:attr/tabIndicator = 0x7f0303f4
com.tytfizikkamp:attr/tabIconTintMode = 0x7f0303f3
com.tytfizikkamp:attr/indicatorColor = 0x7f030223
com.tytfizikkamp:attr/materialAlertDialogTitlePanelStyle = 0x7f0302bc
com.tytfizikkamp:attr/switchTextAppearance = 0x7f0303ee
com.tytfizikkamp:attr/switchStyle = 0x7f0303ed
com.tytfizikkamp:attr/suffixTextAppearance = 0x7f0303e7
com.tytfizikkamp:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f110187
com.tytfizikkamp:style/Base.Widget.AppCompat.RatingBar = 0x7f1100f2
com.tytfizikkamp:id/clip_horizontal = 0x7f08007c
com.tytfizikkamp:attr/boxStrokeColor = 0x7f030087
com.tytfizikkamp:color/m3_ref_palette_dynamic_secondary20 = 0x7f0500df
com.tytfizikkamp:attr/colorSecondaryFixedDim = 0x7f030110
com.tytfizikkamp:attr/subtitleTextStyle = 0x7f0303e5
com.tytfizikkamp:style/Platform.MaterialComponents = 0x7f110140
com.tytfizikkamp:attr/subtitleTextAppearance = 0x7f0303e3
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral60 = 0x7f0500ac
com.tytfizikkamp:style/Base.Widget.AppCompat.Spinner = 0x7f1100f9
com.tytfizikkamp:attr/textAppearanceHeadline2 = 0x7f03041c
com.tytfizikkamp:attr/subtitleCentered = 0x7f0303e2
com.tytfizikkamp:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1102b9
com.tytfizikkamp:attr/subheaderInsetStart = 0x7f0303de
com.tytfizikkamp:dimen/hint_pressed_alpha_material_light = 0x7f06009a
com.tytfizikkamp:attr/subheaderColor = 0x7f0303dc
com.tytfizikkamp:attr/strokeColor = 0x7f0303d9
com.tytfizikkamp:attr/statusBarScrim = 0x7f0303d8
com.tytfizikkamp:attr/state_indeterminate = 0x7f0303d2
com.tytfizikkamp:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f110325
com.tytfizikkamp:attr/state_error = 0x7f0303d1
com.tytfizikkamp:attr/state_collapsible = 0x7f0303cf
com.tytfizikkamp:color/mtrl_btn_text_btn_bg_color_selector = 0x7f0502bb
com.tytfizikkamp:attr/state_collapsed = 0x7f0303ce
com.tytfizikkamp:attr/state_above_anchor = 0x7f0303cd
com.tytfizikkamp:attr/coplanarSiblingViewId = 0x7f03013b
com.tytfizikkamp:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f11009b
com.tytfizikkamp:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f060128
com.tytfizikkamp:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.tytfizikkamp:attr/staggered = 0x7f0303c5
com.tytfizikkamp:id/always = 0x7f080056
com.tytfizikkamp:drawable/abc_btn_colored_material = 0x7f07002f
com.tytfizikkamp:style/Base.Widget.Material3.CardView = 0x7f110103
com.tytfizikkamp:attr/splitTrack = 0x7f0303c2
com.tytfizikkamp:style/DialogAnimationFade = 0x7f110127
com.tytfizikkamp:color/material_on_surface_stroke = 0x7f050278
com.tytfizikkamp:attr/viewAspectRatio = 0x7f030499
com.tytfizikkamp:attr/thumbTint = 0x7f030450
com.tytfizikkamp:attr/spinnerStyle = 0x7f0303c1
com.tytfizikkamp:id/accessibility_custom_action_10 = 0x7f080015
com.tytfizikkamp:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f060255
com.tytfizikkamp:layout/abc_alert_dialog_material = 0x7f0b0009
com.tytfizikkamp:attr/spinBars = 0x7f0303bf
com.tytfizikkamp:attr/snackbarButtonStyle = 0x7f0303bb
com.tytfizikkamp:attr/sliderStyle = 0x7f0303ba
com.tytfizikkamp:dimen/m3_side_sheet_standard_elevation = 0x7f0601e8
com.tytfizikkamp:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f060157
com.tytfizikkamp:color/abc_primary_text_material_light = 0x7f05000c
com.tytfizikkamp:attr/materialButtonStyle = 0x7f0302bf
com.tytfizikkamp:attr/sizePercent = 0x7f0303b9
com.tytfizikkamp:color/m3_tabs_icon_color = 0x7f0501fb
com.tytfizikkamp:attr/singleChoiceItemLayout = 0x7f0303b6
com.tytfizikkamp:dimen/abc_control_inset_material = 0x7f060019
com.tytfizikkamp:color/m3_ref_palette_secondary70 = 0x7f05013d
com.tytfizikkamp:style/Widget.MaterialComponents.Slider = 0x7f11045d
com.tytfizikkamp:attr/helperTextEnabled = 0x7f030206
com.tytfizikkamp:attr/boxCornerRadiusTopEnd = 0x7f030085
com.tytfizikkamp:attr/simpleItemSelectedColor = 0x7f0303b3
com.tytfizikkamp:attr/thumbTextPadding = 0x7f03044f
com.tytfizikkamp:dimen/design_bottom_navigation_active_item_min_width = 0x7f060061
com.tytfizikkamp:id/dragUp = 0x7f0800a4
com.tytfizikkamp:anim/rns_default_exit_out = 0x7f010035
com.tytfizikkamp:attr/insetForeground = 0x7f03022a
com.tytfizikkamp:attr/simpleItemLayout = 0x7f0303b2
com.tytfizikkamp:attr/showMotionSpec = 0x7f0303ab
com.tytfizikkamp:dimen/m3_card_dragged_z = 0x7f0600e6
com.tytfizikkamp:dimen/mtrl_navigation_item_icon_padding = 0x7f0602c8
com.tytfizikkamp:attr/shapeAppearanceOverlay = 0x7f0303a1
com.tytfizikkamp:style/Widget.AppCompat.CompoundButton.Switch = 0x7f110310
com.tytfizikkamp:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f050091
com.tytfizikkamp:attr/shapeAppearanceMediumComponent = 0x7f0303a0
com.tytfizikkamp:attr/statusBarBackground = 0x7f0303d6
com.tytfizikkamp:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0501c3
com.tytfizikkamp:style/Base.Widget.AppCompat.ListMenuView = 0x7f1100e8
com.tytfizikkamp:attr/shapeAppearanceCornerLarge = 0x7f03039c
com.tytfizikkamp:color/m3_ref_palette_dynamic_secondary95 = 0x7f0500e7
com.tytfizikkamp:dimen/abc_text_size_body_1_material = 0x7f06003f
com.tytfizikkamp:anim/m3_side_sheet_enter_from_right = 0x7f01002c
com.tytfizikkamp:id/view_tree_saved_state_registry_owner = 0x7f0801ef
com.tytfizikkamp:attr/shapeAppearanceCornerExtraLarge = 0x7f03039a
com.tytfizikkamp:drawable/node_modules_reactnavigation_elements_lib_module_assets_clearicon = 0x7f0700d8
com.tytfizikkamp:color/material_dynamic_primary20 = 0x7f050240
com.tytfizikkamp:attr/shapeAppearance = 0x7f030399
com.tytfizikkamp:attr/floatingActionButtonPrimaryStyle = 0x7f0301d0
com.tytfizikkamp:attr/selectionRequired = 0x7f030397
com.tytfizikkamp:layout/mtrl_alert_dialog_actions = 0x7f0b0046
com.tytfizikkamp:attr/selectableItemBackgroundBorderless = 0x7f030396
com.tytfizikkamp:dimen/m3_card_elevated_elevation = 0x7f0600e9
com.tytfizikkamp:id/tag_on_apply_window_listener = 0x7f0801b3
com.tytfizikkamp:anim/linear_indeterminate_line1_head_interpolator = 0x7f010023
com.tytfizikkamp:attr/autofillInlineSuggestionChip = 0x7f030045
com.tytfizikkamp:attr/flow_padding = 0x7f0301e7
com.tytfizikkamp:attr/seekBarStyle = 0x7f030394
com.tytfizikkamp:color/m3_ref_palette_dynamic_secondary0 = 0x7f0500dc
com.tytfizikkamp:attr/behavior_fitToContents = 0x7f030070
com.tytfizikkamp:attr/tabUnboundedRipple = 0x7f03040b
com.tytfizikkamp:dimen/m3_btn_disabled_translation_z = 0x7f0600cf
com.tytfizikkamp:attr/scrimBackground = 0x7f03038e
com.tytfizikkamp:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f11040c
com.tytfizikkamp:attr/scrimAnimationDuration = 0x7f03038d
com.tytfizikkamp:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.tytfizikkamp:color/switch_thumb_material_dark = 0x7f050301
com.tytfizikkamp:color/design_default_color_on_secondary = 0x7f050045
com.tytfizikkamp:string/material_motion_easing_standard = 0x7f100077
com.tytfizikkamp:attr/saturation = 0x7f03038c
com.tytfizikkamp:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f0601f8
com.tytfizikkamp:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1100a2
com.tytfizikkamp:color/m3_chip_assist_text_color = 0x7f050071
com.tytfizikkamp:style/TextAppearance.Material3.BodyLarge = 0x7f1101ee
com.tytfizikkamp:id/mtrl_card_checked_layer_id = 0x7f08011e
com.tytfizikkamp:attr/roundingBorderPadding = 0x7f03038a
com.tytfizikkamp:id/accelerate = 0x7f08000e
com.tytfizikkamp:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f11011a
com.tytfizikkamp:attr/roundingBorderColor = 0x7f030389
com.tytfizikkamp:attr/dropDownListViewStyle = 0x7f030184
com.tytfizikkamp:attr/roundedCornerRadius = 0x7f030388
com.tytfizikkamp:color/m3_sys_color_light_on_secondary = 0x7f0501d8
com.tytfizikkamp:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f09003a
com.tytfizikkamp:attr/fastScrollHorizontalTrackDrawable = 0x7f0301c7
com.tytfizikkamp:color/m3_ref_palette_dynamic_secondary60 = 0x7f0500e3
com.tytfizikkamp:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f060195
com.tytfizikkamp:color/m3_ref_palette_secondary30 = 0x7f050139
com.tytfizikkamp:attr/badgeVerticalPadding = 0x7f030060
com.tytfizikkamp:color/m3_ref_palette_error20 = 0x7f0500f9
com.tytfizikkamp:style/Theme.AppCompat = 0x7f110216
com.tytfizikkamp:attr/roundTopLeft = 0x7f030384
com.tytfizikkamp:attr/fabCradleRoundedCornerRadius = 0x7f0301be
com.tytfizikkamp:drawable/notification_bg_normal = 0x7f0700e0
com.tytfizikkamp:macro/m3_comp_search_bar_supporting_text_color = 0x7f0c00ee
com.tytfizikkamp:attr/roundBottomStart = 0x7f030381
com.tytfizikkamp:string/abc_searchview_description_search = 0x7f100015
com.tytfizikkamp:attr/roundBottomRight = 0x7f030380
com.tytfizikkamp:drawable/notification_bg_low_normal = 0x7f0700de
com.tytfizikkamp:dimen/autofill_inline_suggestion_icon_size = 0x7f060052
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f110078
com.tytfizikkamp:attr/backgroundTintMode = 0x7f030056
com.tytfizikkamp:attr/srcCompat = 0x7f0303c3
com.tytfizikkamp:styleable/AlertDialog = 0x7f120006
com.tytfizikkamp:dimen/m3_badge_size = 0x7f0600b6
com.tytfizikkamp:style/TextAppearance.Material3.SearchView.Prefix = 0x7f1101fd
com.tytfizikkamp:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f060202
com.tytfizikkamp:attr/roundBottomLeft = 0x7f03037f
com.tytfizikkamp:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0602a6
com.tytfizikkamp:style/Widget.AppCompat.RatingBar = 0x7f110335
com.tytfizikkamp:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f11018e
com.tytfizikkamp:color/material_personalized_primary_inverse_text_disable_only = 0x7f0502ab
com.tytfizikkamp:styleable/Layout = 0x7f120047
com.tytfizikkamp:id/accessibility_hint = 0x7f080033
com.tytfizikkamp:attr/roundAsCircle = 0x7f03037d
com.tytfizikkamp:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f110087
com.tytfizikkamp:attr/removeEmbeddedFabElevation = 0x7f030377
com.tytfizikkamp:attr/region_widthLessThan = 0x7f030375
com.tytfizikkamp:attr/region_heightLessThan = 0x7f030373
com.tytfizikkamp:attr/region_widthMoreThan = 0x7f030376
com.tytfizikkamp:dimen/mtrl_extended_fab_translation_z_base = 0x7f0602b4
com.tytfizikkamp:attr/ratingBarStyleIndicator = 0x7f030370
com.tytfizikkamp:attr/radioButtonStyle = 0x7f03036d
com.tytfizikkamp:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0301ce
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0500c6
com.tytfizikkamp:color/m3_ref_palette_neutral92 = 0x7f050115
com.tytfizikkamp:id/enterAlwaysCollapsed = 0x7f0800b1
com.tytfizikkamp:attr/itemShapeAppearance = 0x7f03023e
com.tytfizikkamp:color/design_default_color_error = 0x7f050041
com.tytfizikkamp:attr/passwordToggleTint = 0x7f03034b
com.tytfizikkamp:attr/queryHint = 0x7f03036b
com.tytfizikkamp:attr/queryBackground = 0x7f03036a
com.tytfizikkamp:attr/yearSelectedStyle = 0x7f0304ad
com.tytfizikkamp:color/abc_tint_edittext = 0x7f050015
com.tytfizikkamp:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f06013c
com.tytfizikkamp:color/mtrl_btn_text_color_disabled = 0x7f0502bd
com.tytfizikkamp:color/m3_navigation_item_background_color = 0x7f050093
com.tytfizikkamp:attr/customStringValue = 0x7f03015a
com.tytfizikkamp:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f110361
com.tytfizikkamp:attr/progressBarImage = 0x7f030366
com.tytfizikkamp:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0c0091
com.tytfizikkamp:macro/m3_comp_dialog_headline_type = 0x7f0c0025
com.tytfizikkamp:integer/m3_sys_motion_duration_medium1 = 0x7f090017
com.tytfizikkamp:color/m3_sys_color_dark_on_tertiary_container = 0x7f05016b
com.tytfizikkamp:style/Widget.Material3.Button.OutlinedButton = 0x7f110371
com.tytfizikkamp:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
com.tytfizikkamp:dimen/hint_alpha_material_dark = 0x7f060097
com.tytfizikkamp:attr/progressBarAutoRotateInterval = 0x7f030365
com.tytfizikkamp:styleable/SwipeRefreshLayout = 0x7f120084
com.tytfizikkamp:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1101bc
com.tytfizikkamp:attr/prefixTextColor = 0x7f030361
com.tytfizikkamp:attr/tabContentStart = 0x7f0303f0
com.tytfizikkamp:color/material_dynamic_tertiary100 = 0x7f050259
com.tytfizikkamp:attr/shapeAppearanceLargeComponent = 0x7f03039f
com.tytfizikkamp:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f060146
com.tytfizikkamp:attr/prefixTextAppearance = 0x7f030360
com.tytfizikkamp:color/m3_ref_palette_neutral99 = 0x7f05011a
com.tytfizikkamp:drawable/btn_radio_on_mtrl = 0x7f070080
com.tytfizikkamp:drawable/notification_bg_normal_pressed = 0x7f0700e1
com.tytfizikkamp:dimen/m3_btn_icon_btn_padding_right = 0x7f0600d3
com.tytfizikkamp:attr/toolbarNavigationButtonStyle = 0x7f030471
com.tytfizikkamp:attr/prefixText = 0x7f03035f
com.tytfizikkamp:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1100cf
com.tytfizikkamp:drawable/abc_ic_ab_back_material = 0x7f07003d
com.tytfizikkamp:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0c00c1
com.tytfizikkamp:attr/flow_firstVerticalBias = 0x7f0301dc
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Body1 = 0x7f110202
com.tytfizikkamp:attr/popupWindowStyle = 0x7f03035e
com.tytfizikkamp:color/material_timepicker_modebutton_tint = 0x7f0502b7
com.tytfizikkamp:attr/tooltipStyle = 0x7f030476
com.tytfizikkamp:integer/m3_sys_motion_path = 0x7f09001f
com.tytfizikkamp:dimen/m3_comp_navigation_drawer_container_width = 0x7f06013f
com.tytfizikkamp:attr/navigationMode = 0x7f03032a
com.tytfizikkamp:attr/placeholder_emptyVisibility = 0x7f03035a
com.tytfizikkamp:style/Platform.MaterialComponents.Light.Dialog = 0x7f110143
com.tytfizikkamp:dimen/compat_button_inset_vertical_material = 0x7f060058
com.tytfizikkamp:color/m3_ref_palette_neutral4 = 0x7f05010c
com.tytfizikkamp:color/m3_ref_palette_neutral0 = 0x7f050103
com.tytfizikkamp:attr/actionModeCopyDrawable = 0x7f030014
com.tytfizikkamp:dimen/mtrl_navigation_elevation = 0x7f0602c6
com.tytfizikkamp:attr/placeholderTextColor = 0x7f030359
com.tytfizikkamp:dimen/m3_navigation_item_horizontal_padding = 0x7f0601c1
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f110265
com.tytfizikkamp:dimen/abc_search_view_preferred_width = 0x7f060037
com.tytfizikkamp:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f11023d
com.tytfizikkamp:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f060216
com.tytfizikkamp:attr/placeholderImage = 0x7f030355
com.tytfizikkamp:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f11046f
com.tytfizikkamp:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1102cd
com.tytfizikkamp:attr/badgeText = 0x7f03005d
com.tytfizikkamp:drawable/abc_ic_go_search_api_material = 0x7f070041
com.tytfizikkamp:dimen/mtrl_tooltip_minWidth = 0x7f06030a
com.tytfizikkamp:dimen/mtrl_card_elevation = 0x7f0602a0
com.tytfizikkamp:attr/perpendicularPath_percent = 0x7f030353
com.tytfizikkamp:attr/percentX = 0x7f030351
com.tytfizikkamp:drawable/ic_call_answer_low = 0x7f070089
com.tytfizikkamp:color/m3_bottom_sheet_drag_handle_color = 0x7f050064
com.tytfizikkamp:attr/tabInlineLabel = 0x7f0303fb
com.tytfizikkamp:attr/pivotAnchor = 0x7f030354
com.tytfizikkamp:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f110298
com.tytfizikkamp:color/material_dynamic_neutral_variant0 = 0x7f050230
com.tytfizikkamp:id/textStart = 0x7f0801c2
com.tytfizikkamp:attr/actionBarTabStyle = 0x7f030007
com.tytfizikkamp:attr/dividerThickness = 0x7f030171
com.tytfizikkamp:attr/titleTextEllipsize = 0x7f03046d
com.tytfizikkamp:color/design_default_color_secondary_variant = 0x7f05004b
com.tytfizikkamp:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f110136
com.tytfizikkamp:attr/percentY = 0x7f030352
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f1101e5
com.tytfizikkamp:attr/drawerLayoutCornerSize = 0x7f030181
com.tytfizikkamp:attr/bottomAppBarStyle = 0x7f03007a
com.tytfizikkamp:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0600c8
com.tytfizikkamp:attr/path_percent = 0x7f03034e
com.tytfizikkamp:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0600a4
com.tytfizikkamp:color/material_dynamic_secondary95 = 0x7f050255
com.tytfizikkamp:style/ShapeAppearanceOverlay.Material3.Button = 0x7f110188
com.tytfizikkamp:attr/passwordToggleTintMode = 0x7f03034c
com.tytfizikkamp:attr/spanCount = 0x7f0303be
com.tytfizikkamp:string/material_motion_easing_emphasized = 0x7f100075
com.tytfizikkamp:id/marquee = 0x7f0800f2
com.tytfizikkamp:attr/passwordToggleDrawable = 0x7f030349
com.tytfizikkamp:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f0700ba
com.tytfizikkamp:dimen/m3_btn_padding_left = 0x7f0600db
com.tytfizikkamp:color/m3_ref_palette_neutral30 = 0x7f05010b
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f11016e
com.tytfizikkamp:attr/panelBackground = 0x7f030345
com.tytfizikkamp:attr/paddingTopSystemWindowInsets = 0x7f030344
com.tytfizikkamp:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0c00e8
com.tytfizikkamp:drawable/autofill_inline_suggestion_chip_background = 0x7f070077
com.tytfizikkamp:attr/paddingTopNoTitle = 0x7f030343
com.tytfizikkamp:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
com.tytfizikkamp:string/material_timepicker_text_input_mode_description = 0x7f100081
com.tytfizikkamp:attr/paddingStartSystemWindowInsets = 0x7f030342
com.tytfizikkamp:attr/chipMinTouchTargetSize = 0x7f0300c0
com.tytfizikkamp:attr/limitBoundsTo = 0x7f03029d
com.tytfizikkamp:attr/tickMark = 0x7f030457
com.tytfizikkamp:attr/chipIconEnabled = 0x7f0300bb
com.tytfizikkamp:anim/abc_fade_out = 0x7f010001
com.tytfizikkamp:color/m3_button_ripple_color_selector = 0x7f050069
com.tytfizikkamp:attr/tabBackground = 0x7f0303ef
com.tytfizikkamp:dimen/mtrl_slider_thumb_radius = 0x7f0602eb
com.tytfizikkamp:attr/paddingBottomSystemWindowInsets = 0x7f03033d
com.tytfizikkamp:attr/textLocale = 0x7f030441
com.tytfizikkamp:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0c008a
com.tytfizikkamp:attr/motionProgress = 0x7f030320
com.tytfizikkamp:dimen/mtrl_extended_fab_start_padding = 0x7f0602b1
com.tytfizikkamp:color/m3_ref_palette_dynamic_primary0 = 0x7f0500cf
com.tytfizikkamp:dimen/mtrl_high_ripple_default_alpha = 0x7f0602bb
com.tytfizikkamp:attr/backgroundTint = 0x7f030055
com.tytfizikkamp:attr/showDelay = 0x7f0303a8
com.tytfizikkamp:attr/fontProviderPackage = 0x7f0301f4
com.tytfizikkamp:color/m3_fab_efab_foreground_color_selector = 0x7f05008a
com.tytfizikkamp:dimen/mtrl_badge_text_size = 0x7f060250
com.tytfizikkamp:color/m3_dark_default_color_secondary_text = 0x7f050077
com.tytfizikkamp:attr/overlapAnchor = 0x7f030339
com.tytfizikkamp:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1102b8
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f11003c
com.tytfizikkamp:attr/textAppearanceLargePopupMenu = 0x7f030427
com.tytfizikkamp:attr/marginHorizontal = 0x7f0302b4
com.tytfizikkamp:dimen/material_clock_number_text_size = 0x7f06022b
com.tytfizikkamp:attr/submitBackground = 0x7f0303e0
com.tytfizikkamp:dimen/material_emphasis_high_type = 0x7f060236
com.tytfizikkamp:attr/onShow = 0x7f030337
com.tytfizikkamp:id/textEnd = 0x7f0801bf
com.tytfizikkamp:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f06028f
com.tytfizikkamp:style/CardView = 0x7f110124
com.tytfizikkamp:attr/preserveIconSpacing = 0x7f030362
com.tytfizikkamp:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0c0033
com.tytfizikkamp:id/easeIn = 0x7f0800a6
com.tytfizikkamp:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f060296
com.tytfizikkamp:attr/onHide = 0x7f030334
com.tytfizikkamp:attr/materialSearchViewToolbarStyle = 0x7f0302e1
com.tytfizikkamp:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0301cf
com.tytfizikkamp:string/m3_sys_motion_easing_standard = 0x7f100069
com.tytfizikkamp:attr/numericModifiers = 0x7f030331
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Menu = 0x7f11002a
com.tytfizikkamp:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0c014d
com.tytfizikkamp:color/mtrl_switch_track_tint = 0x7f0502e3
com.tytfizikkamp:id/navigation_bar_item_icon_view = 0x7f08012f
com.tytfizikkamp:attr/number = 0x7f030330
com.tytfizikkamp:dimen/m3_comp_divider_thickness = 0x7f060108
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1103c4
com.tytfizikkamp:attr/nestedScrollable = 0x7f03032f
com.tytfizikkamp:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f0700b8
com.tytfizikkamp:attr/nestedScrollFlags = 0x7f03032d
com.tytfizikkamp:attr/roundingBorderWidth = 0x7f03038b
com.tytfizikkamp:attr/tabSelectedTextAppearance = 0x7f030406
com.tytfizikkamp:attr/navigationIcon = 0x7f030328
com.tytfizikkamp:attr/compatShadowEnabled = 0x7f030122
com.tytfizikkamp:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f110372
com.tytfizikkamp:attr/multiChoiceItemLayout = 0x7f030326
com.tytfizikkamp:attr/dialogPreferredPadding = 0x7f030168
com.tytfizikkamp:attr/failureImage = 0x7f0301c3
com.tytfizikkamp:attr/moveWhenScrollAtTop = 0x7f030325
com.tytfizikkamp:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1101b1
com.tytfizikkamp:string/character_counter_overflowed_content_description = 0x7f10004c
com.tytfizikkamp:dimen/abc_text_size_body_2_material = 0x7f060040
com.tytfizikkamp:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
com.tytfizikkamp:attr/fastScrollEnabled = 0x7f0301c5
com.tytfizikkamp:attr/motionStagger = 0x7f030321
com.tytfizikkamp:attr/materialCalendarHeaderToggleButton = 0x7f0302ca
com.tytfizikkamp:attr/motionPathRotate = 0x7f03031f
com.tytfizikkamp:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0c0148
com.tytfizikkamp:anim/linear_indeterminate_line1_tail_interpolator = 0x7f010024
com.tytfizikkamp:color/design_snackbar_background_color = 0x7f050056
com.tytfizikkamp:styleable/SwitchCompat = 0x7f120085
com.tytfizikkamp:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f110387
com.tytfizikkamp:attr/motionInterpolator = 0x7f03031d
com.tytfizikkamp:styleable/Autofill.InlineSuggestion = 0x7f120013
com.tytfizikkamp:attr/motionEasingStandardDecelerateInterpolator = 0x7f03031b
com.tytfizikkamp:attr/motionEasingStandardAccelerateInterpolator = 0x7f03031a
com.tytfizikkamp:attr/editTextColor = 0x7f030189
com.tytfizikkamp:attr/expandedTitleMarginEnd = 0x7f0301ac
com.tytfizikkamp:attr/shapeAppearanceCornerExtraSmall = 0x7f03039b
com.tytfizikkamp:integer/m3_sys_motion_duration_short4 = 0x7f09001e
com.tytfizikkamp:attr/buttonIconTintMode = 0x7f030096
com.tytfizikkamp:attr/itemSpacing = 0x7f030245
com.tytfizikkamp:attr/motionDurationShort1 = 0x7f03030d
com.tytfizikkamp:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f0700ce
com.tytfizikkamp:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f030314
com.tytfizikkamp:style/Base.Widget.AppCompat.ActionMode = 0x7f1100cd
com.tytfizikkamp:attr/itemTextAppearanceActiveBoldEnabled = 0x7f03024a
com.tytfizikkamp:attr/thumbWidth = 0x7f030453
com.tytfizikkamp:layout/mtrl_calendar_month_navigation = 0x7f0b0052
com.tytfizikkamp:attr/motionEasingDecelerated = 0x7f030312
com.tytfizikkamp:style/Widget.AppCompat.ActionButton.Overflow = 0x7f110302
com.tytfizikkamp:raw/keep = 0x7f0f0000
com.tytfizikkamp:dimen/abc_floating_window_z = 0x7f06002f
com.tytfizikkamp:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f060106
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral17 = 0x7f0500a3
com.tytfizikkamp:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.tytfizikkamp:attr/badgeGravity = 0x7f030057
com.tytfizikkamp:id/homeAsUp = 0x7f0800d5
com.tytfizikkamp:attr/motionEasingAccelerated = 0x7f030311
com.tytfizikkamp:style/Base.TextAppearance.AppCompat = 0x7f110019
com.tytfizikkamp:color/m3_sys_color_light_secondary = 0x7f0501e2
com.tytfizikkamp:attr/deltaPolarAngle = 0x7f030164
com.tytfizikkamp:color/m3_ref_palette_neutral_variant90 = 0x7f050125
com.tytfizikkamp:attr/motionDurationShort3 = 0x7f03030f
com.tytfizikkamp:attr/waveDecay = 0x7f03049e
com.tytfizikkamp:style/Base.Widget.MaterialComponents.Chip = 0x7f110116
com.tytfizikkamp:attr/motionDurationShort2 = 0x7f03030e
com.tytfizikkamp:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0c00c0
com.tytfizikkamp:attr/placeholderText = 0x7f030357
com.tytfizikkamp:styleable/RadialViewGroup = 0x7f120070
com.tytfizikkamp:attr/colorContainer = 0x7f0300e8
com.tytfizikkamp:attr/queryPatterns = 0x7f03036c
com.tytfizikkamp:dimen/mtrl_btn_disabled_elevation = 0x7f06025c
com.tytfizikkamp:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f060160
com.tytfizikkamp:attr/expandedTitleMarginStart = 0x7f0301ad
com.tytfizikkamp:attr/motionDurationMedium4 = 0x7f03030c
com.tytfizikkamp:attr/subMenuArrow = 0x7f0303db
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant4 = 0x7f0500c0
com.tytfizikkamp:attr/motionDurationMedium2 = 0x7f03030a
com.tytfizikkamp:dimen/mtrl_navigation_rail_icon_margin = 0x7f0602d0
com.tytfizikkamp:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f060293
com.tytfizikkamp:dimen/m3_card_elevated_dragged_z = 0x7f0600e8
com.tytfizikkamp:style/Widget.MaterialComponents.ActionMode = 0x7f110406
com.tytfizikkamp:id/dragRight = 0x7f0800a2
com.tytfizikkamp:attr/menuAlignmentMode = 0x7f0302f3
com.tytfizikkamp:attr/dynamicColorThemeOverlay = 0x7f030187
com.tytfizikkamp:attr/motionDurationLong2 = 0x7f030306
com.tytfizikkamp:attr/motionDurationLong1 = 0x7f030305
com.tytfizikkamp:attr/motionDurationExtraLong3 = 0x7f030303
com.tytfizikkamp:attr/trackColorActive = 0x7f03047e
com.tytfizikkamp:animator/m3_btn_state_list_anim = 0x7f02000b
com.tytfizikkamp:attr/lStar = 0x7f030251
com.tytfizikkamp:attr/mock_showLabel = 0x7f0302ff
com.tytfizikkamp:color/abc_search_url_text_pressed = 0x7f05000f
com.tytfizikkamp:attr/ttcIndex = 0x7f030492
com.tytfizikkamp:color/mtrl_switch_thumb_tint = 0x7f0502e1
com.tytfizikkamp:attr/mock_labelColor = 0x7f0302fd
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f1101e7
com.tytfizikkamp:id/masked = 0x7f0800f3
com.tytfizikkamp:attr/mock_labelBackgroundColor = 0x7f0302fc
com.tytfizikkamp:id/titleDividerNoCustom = 0x7f0801cf
com.tytfizikkamp:color/m3_sys_color_dynamic_light_outline = 0x7f0501b0
com.tytfizikkamp:attr/mock_label = 0x7f0302fb
com.tytfizikkamp:attr/minWidth = 0x7f0302f9
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f110260
com.tytfizikkamp:style/Theme.Material3.Light.Dialog = 0x7f110251
com.tytfizikkamp:attr/minSeparation = 0x7f0302f7
com.tytfizikkamp:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f110454
com.tytfizikkamp:id/transition_clip = 0x7f0801d9
com.tytfizikkamp:color/m3_ref_palette_error30 = 0x7f0500fa
com.tytfizikkamp:attr/minHideDelay = 0x7f0302f6
com.tytfizikkamp:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f070034
com.tytfizikkamp:color/m3_ref_palette_neutral_variant20 = 0x7f05011e
com.tytfizikkamp:string/m3_sys_motion_easing_legacy_decelerate = 0x7f100067
com.tytfizikkamp:color/design_dark_default_color_on_secondary = 0x7f050038
com.tytfizikkamp:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f0700cb
com.tytfizikkamp:attr/menuGravity = 0x7f0302f4
com.tytfizikkamp:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f11040a
com.tytfizikkamp:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1102b7
com.tytfizikkamp:dimen/m3_searchview_divider_size = 0x7f0601e3
com.tytfizikkamp:animator/fragment_close_exit = 0x7f020004
com.tytfizikkamp:attr/menu = 0x7f0302f2
com.tytfizikkamp:id/fill = 0x7f0800b7
com.tytfizikkamp:attr/measureWithLargestChild = 0x7f0302f1
com.tytfizikkamp:attr/maxNumber = 0x7f0302ee
com.tytfizikkamp:dimen/design_navigation_separator_vertical_padding = 0x7f06007e
com.tytfizikkamp:attr/materialIconButtonFilledTonalStyle = 0x7f0302da
com.tytfizikkamp:attr/maxLines = 0x7f0302ed
com.tytfizikkamp:id/transition_scene_layoutid_cache = 0x7f0801df
com.tytfizikkamp:dimen/m3_searchbar_outlined_stroke_width = 0x7f0601df
com.tytfizikkamp:attr/showDividers = 0x7f0303a9
com.tytfizikkamp:attr/actionBarTabBarStyle = 0x7f030006
com.tytfizikkamp:attr/maxCharacterCount = 0x7f0302ea
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f050197
com.tytfizikkamp:attr/maxButtonHeight = 0x7f0302e9
com.tytfizikkamp:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1102d1
com.tytfizikkamp:id/invalidate_transform = 0x7f0800df
com.tytfizikkamp:color/design_fab_shadow_mid_color = 0x7f05004f
com.tytfizikkamp:dimen/mtrl_tooltip_minHeight = 0x7f060309
com.tytfizikkamp:integer/react_native_dev_server_port = 0x7f090042
com.tytfizikkamp:attr/maxActionInlineWidth = 0x7f0302e8
com.tytfizikkamp:anim/rns_ios_from_left_foreground_close = 0x7f01003c
com.tytfizikkamp:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f110120
com.tytfizikkamp:attr/itemActiveIndicatorStyle = 0x7f030230
com.tytfizikkamp:attr/constraintSetEnd = 0x7f030124
com.tytfizikkamp:animator/mtrl_chip_state_list_anim = 0x7f020018
com.tytfizikkamp:attr/floatingActionButtonTertiaryStyle = 0x7f0301d9
com.tytfizikkamp:attr/alphabeticModifiers = 0x7f030030
com.tytfizikkamp:attr/navigationIconTint = 0x7f030329
com.tytfizikkamp:attr/checkMarkTint = 0x7f0300a8
com.tytfizikkamp:attr/layout_editor_absoluteY = 0x7f03028d
com.tytfizikkamp:id/standard = 0x7f0801a2
com.tytfizikkamp:attr/showText = 0x7f0303ad
com.tytfizikkamp:attr/chipCornerRadius = 0x7f0300b7
com.tytfizikkamp:attr/floatingActionButtonSmallStyle = 0x7f0301d4
com.tytfizikkamp:drawable/notification_bg = 0x7f0700dc
com.tytfizikkamp:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0c00b6
com.tytfizikkamp:attr/behavior_autoHide = 0x7f03006c
com.tytfizikkamp:dimen/design_navigation_item_icon_padding = 0x7f06007a
com.tytfizikkamp:attr/materialSearchViewToolbarHeight = 0x7f0302e0
com.tytfizikkamp:attr/materialSearchViewStyle = 0x7f0302df
com.tytfizikkamp:attr/tabPaddingBottom = 0x7f030400
com.tytfizikkamp:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0c001e
com.tytfizikkamp:attr/materialSearchViewPrefixStyle = 0x7f0302de
com.tytfizikkamp:attr/popupMenuBackground = 0x7f03035b
com.tytfizikkamp:attr/materialSearchBarStyle = 0x7f0302dd
com.tytfizikkamp:attr/attributeName = 0x7f03003b
com.tytfizikkamp:color/abc_primary_text_disable_only_material_dark = 0x7f050009
com.tytfizikkamp:dimen/material_emphasis_disabled = 0x7f060234
com.tytfizikkamp:anim/rns_standard_accelerate_interpolator = 0x7f01004c
com.tytfizikkamp:color/m3_sys_color_light_inverse_on_surface = 0x7f0501d0
com.tytfizikkamp:attr/materialDisplayDividerStyle = 0x7f0302d6
com.tytfizikkamp:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f110015
com.tytfizikkamp:attr/layout_constraintBottom_creator = 0x7f030265
com.tytfizikkamp:attr/textAppearanceListItemSmall = 0x7f03042b
com.tytfizikkamp:attr/materialClockStyle = 0x7f0302d5
com.tytfizikkamp:attr/badgeWithTextRadius = 0x7f030064
com.tytfizikkamp:attr/materialCardViewStyle = 0x7f0302d3
com.tytfizikkamp:drawable/ic_call_decline = 0x7f07008c
com.tytfizikkamp:id/title_template = 0x7f0801d0
com.tytfizikkamp:attr/logo = 0x7f0302b0
com.tytfizikkamp:attr/tabIndicatorAnimationDuration = 0x7f0303f5
com.tytfizikkamp:attr/materialCardViewFilledStyle = 0x7f0302d1
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f050182
com.tytfizikkamp:attr/customColorDrawableValue = 0x7f030153
com.tytfizikkamp:attr/materialCardViewElevatedStyle = 0x7f0302d0
com.tytfizikkamp:attr/materialCalendarStyle = 0x7f0302cd
com.tytfizikkamp:drawable/$m3_avd_show_password__1 = 0x7f07000a
com.tytfizikkamp:drawable/abc_cab_background_top_mtrl_alpha = 0x7f070039
com.tytfizikkamp:attr/materialCalendarHeaderTitle = 0x7f0302c9
com.tytfizikkamp:attr/editTextStyle = 0x7f03018a
com.tytfizikkamp:attr/horizontalOffset = 0x7f030214
com.tytfizikkamp:attr/materialCalendarHeaderLayout = 0x7f0302c7
com.tytfizikkamp:drawable/mtrl_popupmenu_background_overlay = 0x7f0700c7
com.tytfizikkamp:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f06015c
com.tytfizikkamp:macro/m3_comp_input_chip_label_text_type = 0x7f0c005c
com.tytfizikkamp:attr/materialCalendarHeaderDivider = 0x7f0302c6
com.tytfizikkamp:dimen/m3_btn_icon_only_default_size = 0x7f0600d5
com.tytfizikkamp:attr/chipStyle = 0x7f0300c8
com.tytfizikkamp:attr/materialCalendarHeaderCancelButton = 0x7f0302c4
com.tytfizikkamp:style/TextAppearance.Design.HelperText = 0x7f1101d6
com.tytfizikkamp:attr/trackDecorationTint = 0x7f030482
com.tytfizikkamp:attr/dialogCornerRadius = 0x7f030167
com.tytfizikkamp:attr/materialCalendarDayOfWeekLabel = 0x7f0302c2
com.tytfizikkamp:styleable/AnimatedStateListDrawableItem = 0x7f120008
com.tytfizikkamp:attr/tabMinWidth = 0x7f0303fd
com.tytfizikkamp:styleable/PropertySet = 0x7f12006f
com.tytfizikkamp:style/Widget.Material3.NavigationRailView.Badge = 0x7f1103d7
com.tytfizikkamp:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f0601fa
com.tytfizikkamp:macro/m3_comp_filter_chip_label_text_type = 0x7f0c0058
com.tytfizikkamp:attr/materialButtonOutlinedStyle = 0x7f0302be
com.tytfizikkamp:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0c0106
com.tytfizikkamp:attr/marginRightSystemWindowInsets = 0x7f0302b6
com.tytfizikkamp:attr/rangeFillColor = 0x7f03036e
com.tytfizikkamp:color/m3_sys_color_light_on_tertiary_container = 0x7f0501dd
com.tytfizikkamp:color/m3_ref_palette_dynamic_tertiary10 = 0x7f0500ea
com.tytfizikkamp:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f11041f
com.tytfizikkamp:attr/logoAdjustViewBounds = 0x7f0302b1
com.tytfizikkamp:attr/listPreferredItemHeightSmall = 0x7f0302ab
com.tytfizikkamp:style/Base.Widget.Material3.ActionMode = 0x7f110101
com.tytfizikkamp:attr/colorSurfaceContainer = 0x7f030114
com.tytfizikkamp:attr/cornerSizeBottomLeft = 0x7f030143
com.tytfizikkamp:attr/listPreferredItemHeightLarge = 0x7f0302aa
com.tytfizikkamp:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f060290
com.tytfizikkamp:attr/materialCalendarFullscreenTheme = 0x7f0302c3
com.tytfizikkamp:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
com.tytfizikkamp:attr/barrierAllowsGoneWidgets = 0x7f030069
com.tytfizikkamp:attr/listPreferredItemHeight = 0x7f0302a9
com.tytfizikkamp:attr/listDividerAlertDialog = 0x7f0302a4
com.tytfizikkamp:color/material_harmonized_color_on_error = 0x7f05026d
com.tytfizikkamp:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0c00a7
com.tytfizikkamp:attr/boxCornerRadiusBottomEnd = 0x7f030083
com.tytfizikkamp:attr/colorPrimaryFixed = 0x7f030108
com.tytfizikkamp:attr/listChoiceIndicatorMultipleAnimated = 0x7f0302a2
com.tytfizikkamp:attr/hideOnContentScroll = 0x7f03020c
com.tytfizikkamp:attr/fabSize = 0x7f0301c1
com.tytfizikkamp:attr/actionModePasteDrawable = 0x7f030017
com.tytfizikkamp:attr/linearProgressIndicatorStyle = 0x7f0302a0
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1103b6
com.tytfizikkamp:attr/layout_constraintTop_creator = 0x7f030281
com.tytfizikkamp:attr/lineHeight = 0x7f03029e
com.tytfizikkamp:attr/colorOnTertiaryFixedVariant = 0x7f030102
com.tytfizikkamp:attr/animate_relativeTo = 0x7f030034
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0500bf
com.tytfizikkamp:color/m3_sys_color_dynamic_light_on_background = 0x7f0501a5
com.tytfizikkamp:attr/pressedTranslationZ = 0x7f030364
com.tytfizikkamp:attr/layout_scrollInterpolator = 0x7f030299
com.tytfizikkamp:color/mtrl_scrim_color = 0x7f0502df
com.tytfizikkamp:attr/layout_scrollEffect = 0x7f030297
com.tytfizikkamp:color/m3_ref_palette_neutral12 = 0x7f050106
com.tytfizikkamp:anim/rns_no_animation_medium = 0x7f010045
com.tytfizikkamp:styleable/MaterialTimePicker = 0x7f12005c
com.tytfizikkamp:style/Theme.AppCompat.Dialog.Alert = 0x7f110220
com.tytfizikkamp:attr/layout_goneMarginStart = 0x7f030292
com.tytfizikkamp:attr/actionButtonStyle = 0x7f03000b
com.tytfizikkamp:attr/navigationViewStyle = 0x7f03032c
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialDivider = 0x7f110450
com.tytfizikkamp:dimen/m3_fab_corner_size = 0x7f0601b7
com.tytfizikkamp:attr/behavior_autoShrink = 0x7f03006d
com.tytfizikkamp:attr/defaultMarginsEnabled = 0x7f030160
com.tytfizikkamp:attr/subheaderTextAppearance = 0x7f0303df
com.tytfizikkamp:dimen/notification_top_pad_large_text = 0x7f06031b
com.tytfizikkamp:attr/layout_goneMarginBottom = 0x7f03028e
com.tytfizikkamp:dimen/m3_searchbar_height = 0x7f0601dc
com.tytfizikkamp:attr/layout_dodgeInsetEdges = 0x7f03028b
com.tytfizikkamp:attr/retryImage = 0x7f030378
com.tytfizikkamp:drawable/abc_list_longpressed_holo = 0x7f07004f
com.tytfizikkamp:string/mtrl_picker_range_header_title = 0x7f1000aa
com.tytfizikkamp:layout/design_navigation_item_separator = 0x7f0b0028
com.tytfizikkamp:id/button_text = 0x7f08006d
com.tytfizikkamp:attr/actionLayout = 0x7f03000d
com.tytfizikkamp:dimen/mtrl_slider_track_height = 0x7f0602ee
com.tytfizikkamp:attr/flow_firstVerticalStyle = 0x7f0301dd
com.tytfizikkamp:attr/counterEnabled = 0x7f030147
com.tytfizikkamp:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f110016
com.tytfizikkamp:integer/m3_sys_motion_duration_long1 = 0x7f090013
com.tytfizikkamp:attr/layout_constraintStart_toStartOf = 0x7f03027f
com.tytfizikkamp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f11014f
com.tytfizikkamp:attr/touchAnchorId = 0x7f030479
com.tytfizikkamp:attr/layout_constraintRight_toLeftOf = 0x7f03027c
com.tytfizikkamp:attr/motionEasingStandardInterpolator = 0x7f03031c
com.tytfizikkamp:attr/autofillInlineSuggestionStartIconStyle = 0x7f030047
com.tytfizikkamp:color/m3_slider_active_track_color_legacy = 0x7f050153
com.tytfizikkamp:attr/layout_constraintLeft_toLeftOf = 0x7f030279
com.tytfizikkamp:id/floating = 0x7f0800c4
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_tertiary = 0x7f05019d
com.tytfizikkamp:color/m3_ref_palette_dynamic_primary50 = 0x7f0500d5
com.tytfizikkamp:attr/layout_constraintHorizontal_weight = 0x7f030277
com.tytfizikkamp:attr/materialIconButtonStyle = 0x7f0302dc
com.tytfizikkamp:attr/actualImageResource = 0x7f030026
com.tytfizikkamp:color/m3_sys_color_dynamic_light_primary_container = 0x7f0501b3
com.tytfizikkamp:attr/curveFit = 0x7f030151
com.tytfizikkamp:attr/listItemLayout = 0x7f0302a5
com.tytfizikkamp:style/Widget.Material3.CollapsingToolbar = 0x7f110393
com.tytfizikkamp:color/material_personalized_color_outline = 0x7f05028d
com.tytfizikkamp:attr/layout_constraintHorizontal_chainStyle = 0x7f030276
com.tytfizikkamp:id/deltaRelative = 0x7f080092
com.tytfizikkamp:color/material_dynamic_neutral0 = 0x7f050223
com.tytfizikkamp:attr/layout_constraintHorizontal_bias = 0x7f030275
com.tytfizikkamp:id/confirm_button = 0x7f080081
com.tytfizikkamp:id/accessibility_custom_action_8 = 0x7f080031
com.tytfizikkamp:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0301b7
com.tytfizikkamp:anim/mtrl_bottom_sheet_slide_in = 0x7f01002f
com.tytfizikkamp:color/m3_sys_color_dark_surface_bright = 0x7f050173
com.tytfizikkamp:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0c0087
com.tytfizikkamp:attr/fontStyle = 0x7f0301f7
com.tytfizikkamp:attr/drawerArrowStyle = 0x7f030180
com.tytfizikkamp:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
com.tytfizikkamp:attr/toolbarSurfaceStyle = 0x7f030473
com.tytfizikkamp:styleable/Insets = 0x7f12003e
com.tytfizikkamp:dimen/m3_sys_elevation_level4 = 0x7f0601f5
com.tytfizikkamp:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f110475
com.tytfizikkamp:attr/motionEasingLinearInterpolator = 0x7f030318
com.tytfizikkamp:styleable/ActionBarLayout = 0x7f120001
com.tytfizikkamp:string/mtrl_switch_thumb_path_pressed = 0x7f1000bd
com.tytfizikkamp:layout/material_clock_period_toggle_land = 0x7f0b003b
com.tytfizikkamp:attr/layoutManager = 0x7f03025b
com.tytfizikkamp:attr/keylines = 0x7f030250
com.tytfizikkamp:attr/helperText = 0x7f030205
com.tytfizikkamp:dimen/abc_button_inset_horizontal_material = 0x7f060012
com.tytfizikkamp:attr/itemVerticalPadding = 0x7f03024d
com.tytfizikkamp:attr/itemPaddingTop = 0x7f03023c
com.tytfizikkamp:attr/itemStrokeColor = 0x7f030246
com.tytfizikkamp:attr/itemShapeFillColor = 0x7f030240
com.tytfizikkamp:attr/sideSheetDialogTheme = 0x7f0303b0
com.tytfizikkamp:attr/thumbIconTintMode = 0x7f03044b
com.tytfizikkamp:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f1103aa
com.tytfizikkamp:attr/itemShapeAppearanceOverlay = 0x7f03023f
com.tytfizikkamp:attr/motionDurationLong4 = 0x7f030308
com.tytfizikkamp:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0c0070
com.tytfizikkamp:attr/layout_constraintLeft_toRightOf = 0x7f03027a
com.tytfizikkamp:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f110107
com.tytfizikkamp:dimen/m3_snackbar_margin = 0x7f0601f0
com.tytfizikkamp:attr/cursorErrorColor = 0x7f030150
com.tytfizikkamp:attr/layout_constraintBaseline_creator = 0x7f030263
com.tytfizikkamp:attr/itemRippleColor = 0x7f03023d
com.tytfizikkamp:color/material_personalized_color_surface_container_lowest = 0x7f05029e
com.tytfizikkamp:attr/trackInsideCornerSize = 0x7f030485
com.tytfizikkamp:attr/dragDirection = 0x7f030173
com.tytfizikkamp:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f110402
com.tytfizikkamp:attr/layout_constraintEnd_toEndOf = 0x7f03026c
com.tytfizikkamp:attr/arcMode = 0x7f030038
com.tytfizikkamp:attr/itemMinHeight = 0x7f030239
com.tytfizikkamp:layout/mtrl_calendar_year = 0x7f0b0055
com.tytfizikkamp:attr/iconGravity = 0x7f030219
com.tytfizikkamp:styleable/MaterialCheckBoxStates = 0x7f120055
com.tytfizikkamp:style/Widget.MaterialComponents.Badge = 0x7f11040e
com.tytfizikkamp:drawable/btn_radio_off_mtrl = 0x7f07007e
com.tytfizikkamp:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0c00cd
com.tytfizikkamp:attr/itemMaxLines = 0x7f030238
com.tytfizikkamp:attr/colorPrimaryVariant = 0x7f03010c
com.tytfizikkamp:attr/swipeRefreshLayoutProgressSpinnerBackgroundColor = 0x7f0303ea
com.tytfizikkamp:string/m3_ref_typeface_plain_medium = 0x7f10005f
com.tytfizikkamp:id/rightToLeft = 0x7f080167
com.tytfizikkamp:color/material_personalized_color_surface_container_highest = 0x7f05029c
com.tytfizikkamp:dimen/m3_searchview_height = 0x7f0601e5
com.tytfizikkamp:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f06014a
com.tytfizikkamp:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f11031b
com.tytfizikkamp:interpolator/mtrl_linear_out_slow_in = 0x7f0a0011
com.tytfizikkamp:dimen/m3_badge_vertical_offset = 0x7f0600b7
com.tytfizikkamp:attr/errorTextAppearance = 0x7f0301a4
com.tytfizikkamp:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
com.tytfizikkamp:id/sin = 0x7f080191
com.tytfizikkamp:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f060210
com.tytfizikkamp:attr/searchViewStyle = 0x7f030393
com.tytfizikkamp:attr/itemIconPadding = 0x7f030235
com.tytfizikkamp:dimen/design_fab_size_mini = 0x7f060072
com.tytfizikkamp:attr/tintNavigationIcon = 0x7f03045f
com.tytfizikkamp:anim/m3_motion_fade_enter = 0x7f010029
com.tytfizikkamp:attr/actionOverflowMenuStyle = 0x7f030020
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Medium = 0x7f110028
com.tytfizikkamp:attr/flow_firstHorizontalBias = 0x7f0301da
com.tytfizikkamp:attr/homeAsUpIndicator = 0x7f030212
com.tytfizikkamp:attr/liftOnScrollTargetViewId = 0x7f03029c
com.tytfizikkamp:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f06015d
com.tytfizikkamp:attr/isMaterial3Theme = 0x7f03022e
com.tytfizikkamp:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
com.tytfizikkamp:attr/iconTint = 0x7f03021d
com.tytfizikkamp:style/TextAppearance.AppCompat.Display1 = 0x7f1101a2
com.tytfizikkamp:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f03043e
com.tytfizikkamp:attr/tabStyle = 0x7f030408
com.tytfizikkamp:attr/colorTertiaryFixed = 0x7f03011f
com.tytfizikkamp:attr/indicatorTrackGapSize = 0x7f030228
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f110267
com.tytfizikkamp:color/foreground_material_light = 0x7f05005e
com.tytfizikkamp:macro/m3_comp_filter_chip_container_shape = 0x7f0c0057
com.tytfizikkamp:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f0700b6
com.tytfizikkamp:attr/indicatorDirectionLinear = 0x7f030225
com.tytfizikkamp:dimen/m3_comp_progress_indicator_stop_indicator_size = 0x7f060164
com.tytfizikkamp:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
com.tytfizikkamp:attr/colorTertiaryFixedDim = 0x7f030120
com.tytfizikkamp:attr/font = 0x7f0301ed
com.tytfizikkamp:attr/indeterminateProgressStyle = 0x7f030222
com.tytfizikkamp:attr/boxStrokeWidthFocused = 0x7f03008a
com.tytfizikkamp:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f0600fc
com.tytfizikkamp:dimen/hint_pressed_alpha_material_dark = 0x7f060099
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1103b8
com.tytfizikkamp:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1100d1
com.tytfizikkamp:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1100ad
com.tytfizikkamp:id/center = 0x7f080070
com.tytfizikkamp:attr/passwordToggleEnabled = 0x7f03034a
com.tytfizikkamp:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f110435
com.tytfizikkamp:attr/clickAction = 0x7f0300cc
com.tytfizikkamp:anim/abc_fade_in = 0x7f010000
com.tytfizikkamp:dimen/m3_card_disabled_z = 0x7f0600e5
com.tytfizikkamp:attr/iconTintMode = 0x7f03021e
com.tytfizikkamp:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f11008e
com.tytfizikkamp:attr/content = 0x7f030128
com.tytfizikkamp:attr/showAsAction = 0x7f0303a7
com.tytfizikkamp:attr/cardBackgroundColor = 0x7f03009c
com.tytfizikkamp:layout/mtrl_navigation_rail_item = 0x7f0b0058
com.tytfizikkamp:attr/tickMarkTintMode = 0x7f030459
com.tytfizikkamp:attr/extendMotionSpec = 0x7f0301b1
com.tytfizikkamp:attr/extraMultilineHeightEnabled = 0x7f0301b8
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1103b9
com.tytfizikkamp:attr/layout_collapseParallaxMultiplier = 0x7f030260
com.tytfizikkamp:integer/m3_sys_motion_duration_short1 = 0x7f09001b
com.tytfizikkamp:attr/constraints = 0x7f030127
com.tytfizikkamp:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0c016e
com.tytfizikkamp:attr/iconStartPadding = 0x7f03021c
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f110041
com.tytfizikkamp:id/accessibility_custom_action_31 = 0x7f08002c
com.tytfizikkamp:attr/chipStrokeColor = 0x7f0300c6
com.tytfizikkamp:macro/m3_comp_text_button_label_text_color = 0x7f0c0144
com.tytfizikkamp:color/m3_selection_control_ripple_color_selector = 0x7f050150
com.tytfizikkamp:styleable/CollapsingToolbarLayout_Layout = 0x7f120024
com.tytfizikkamp:attr/chipStartPadding = 0x7f0300c5
com.tytfizikkamp:attr/iconSize = 0x7f03021b
com.tytfizikkamp:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f0501a4
com.tytfizikkamp:style/Theme.AppCompat.DayNight.Dialog = 0x7f11021a
com.tytfizikkamp:attr/textColorAlertDialogListItem = 0x7f030436
com.tytfizikkamp:dimen/m3_small_fab_max_image_size = 0x7f0601ed
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Headline1 = 0x7f110207
com.tytfizikkamp:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f110139
com.tytfizikkamp:attr/textAppearanceLabelSmall = 0x7f030426
com.tytfizikkamp:attr/floatingActionButtonSecondaryStyle = 0x7f0301d1
com.tytfizikkamp:macro/m3_comp_top_app_bar_small_container_color = 0x7f0c016f
com.tytfizikkamp:color/m3_dynamic_highlighted_text = 0x7f050084
com.tytfizikkamp:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f11039b
com.tytfizikkamp:attr/homeLayout = 0x7f030213
com.tytfizikkamp:id/material_clock_period_pm_button = 0x7f0800fb
com.tytfizikkamp:dimen/material_clock_period_toggle_height = 0x7f06022c
com.tytfizikkamp:color/m3_timepicker_display_text_color = 0x7f05020f
com.tytfizikkamp:color/m3_ref_palette_neutral_variant30 = 0x7f05011f
com.tytfizikkamp:dimen/m3_alert_dialog_elevation = 0x7f0600a1
com.tytfizikkamp:attr/extendStrategy = 0x7f0301b2
com.tytfizikkamp:attr/helperTextTextColor = 0x7f030208
com.tytfizikkamp:dimen/m3_comp_outlined_button_outline_width = 0x7f060150
com.tytfizikkamp:attr/cardCornerRadius = 0x7f03009d
com.tytfizikkamp:color/foreground_material_dark = 0x7f05005d
com.tytfizikkamp:attr/helperTextTextAppearance = 0x7f030207
com.tytfizikkamp:dimen/mtrl_calendar_text_input_padding_top = 0x7f060294
com.tytfizikkamp:color/m3_ref_palette_primary20 = 0x7f05012b
com.tytfizikkamp:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f1101e9
com.tytfizikkamp:color/mtrl_card_view_ripple = 0x7f0502c3
com.tytfizikkamp:attr/textAppearanceTitleMedium = 0x7f030434
com.tytfizikkamp:dimen/m3_comp_navigation_rail_icon_size = 0x7f06014c
com.tytfizikkamp:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f1103ab
com.tytfizikkamp:attr/materialAlertDialogTitleTextStyle = 0x7f0302bd
com.tytfizikkamp:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f070019
com.tytfizikkamp:attr/dividerVertical = 0x7f030172
com.tytfizikkamp:dimen/mtrl_navigation_rail_elevation = 0x7f0602cf
com.tytfizikkamp:dimen/mtrl_btn_stroke_size = 0x7f06026c
com.tytfizikkamp:color/m3_dynamic_dark_default_color_primary_text = 0x7f05007d
com.tytfizikkamp:attr/panelMenuListTheme = 0x7f030346
com.tytfizikkamp:attr/height = 0x7f030204
com.tytfizikkamp:style/Theme.Design.NoActionBar = 0x7f110235
com.tytfizikkamp:attr/headerLayout = 0x7f030203
com.tytfizikkamp:attr/recyclerViewStyle = 0x7f030372
com.tytfizikkamp:anim/catalyst_fade_out = 0x7f010019
com.tytfizikkamp:style/Theme.MaterialComponents.NoActionBar = 0x7f110284
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f11026a
com.tytfizikkamp:attr/colorPrimary = 0x7f030105
com.tytfizikkamp:interpolator/mtrl_fast_out_linear_in = 0x7f0a000e
com.tytfizikkamp:attr/elevationOverlayEnabled = 0x7f03018e
com.tytfizikkamp:attr/goIcon = 0x7f030200
com.tytfizikkamp:color/m3_ref_palette_error50 = 0x7f0500fc
com.tytfizikkamp:color/abc_btn_colored_borderless_text_material = 0x7f050002
com.tytfizikkamp:style/Widget.MaterialComponents.TimePicker = 0x7f110471
com.tytfizikkamp:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
com.tytfizikkamp:attr/gestureInsetBottomIgnored = 0x7f0301ff
com.tytfizikkamp:style/Theme.MaterialComponents.CompactMenu = 0x7f11025a
com.tytfizikkamp:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0602a9
com.tytfizikkamp:attr/closeIconTint = 0x7f0300d6
com.tytfizikkamp:dimen/material_clock_hand_center_dot_radius = 0x7f060228
com.tytfizikkamp:attr/gapBetweenBars = 0x7f0301fe
com.tytfizikkamp:attr/forceDefaultNavigationOnClickListener = 0x7f0301fb
com.tytfizikkamp:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f110248
com.tytfizikkamp:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f060141
com.tytfizikkamp:style/Base.V23.Theme.AppCompat = 0x7f1100b2
com.tytfizikkamp:attr/paddingLeftSystemWindowInsets = 0x7f03033f
com.tytfizikkamp:attr/shrinkMotionSpec = 0x7f0303af
com.tytfizikkamp:color/design_fab_stroke_top_inner_color = 0x7f050053
com.tytfizikkamp:drawable/abc_tab_indicator_material = 0x7f07006b
com.tytfizikkamp:dimen/m3_comp_elevated_button_container_elevation = 0x7f060109
com.tytfizikkamp:color/primary_material_light = 0x7f0502f4
com.tytfizikkamp:attr/fontProviderSystemFontFamily = 0x7f0301f6
com.tytfizikkamp:attr/fontProviderFetchStrategy = 0x7f0301f2
com.tytfizikkamp:attr/fontProviderAuthority = 0x7f0301ef
com.tytfizikkamp:color/m3_sys_color_dark_outline = 0x7f05016c
com.tytfizikkamp:string/material_slider_range_end = 0x7f100078
com.tytfizikkamp:attr/fontFamily = 0x7f0301ee
com.tytfizikkamp:id/open_search_view_search_prefix = 0x7f080147
com.tytfizikkamp:attr/progressBarPadding = 0x7f030368
com.tytfizikkamp:dimen/mtrl_calendar_title_baseline_to_top = 0x7f060295
com.tytfizikkamp:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f060217
com.tytfizikkamp:attr/actionTextColorAlpha = 0x7f030022
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f11043f
com.tytfizikkamp:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f11007d
com.tytfizikkamp:attr/checkedIconSize = 0x7f0300b1
com.tytfizikkamp:attr/checkedIconGravity = 0x7f0300af
com.tytfizikkamp:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f1103f7
com.tytfizikkamp:attr/motionEasingEmphasizedInterpolator = 0x7f030316
com.tytfizikkamp:string/bottomsheet_action_expand_halfway = 0x7f100022
com.tytfizikkamp:attr/flow_lastVerticalStyle = 0x7f0301e5
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral30 = 0x7f0500a7
com.tytfizikkamp:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0c00c8
com.tytfizikkamp:attr/isMaterialTheme = 0x7f03022f
com.tytfizikkamp:attr/cardUseCompatPadding = 0x7f0300a2
com.tytfizikkamp:attr/flow_horizontalBias = 0x7f0301df
com.tytfizikkamp:color/mtrl_btn_bg_color_selector = 0x7f0502b8
com.tytfizikkamp:attr/floatingActionButtonSurfaceStyle = 0x7f0301d8
com.tytfizikkamp:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0c015d
com.tytfizikkamp:attr/colorPrimaryDark = 0x7f030107
com.tytfizikkamp:attr/floatingActionButtonStyle = 0x7f0301d7
com.tytfizikkamp:style/Theme.Material3.Dark.SideSheetDialog = 0x7f110240
com.tytfizikkamp:attr/colorSurfaceContainerLowest = 0x7f030118
com.tytfizikkamp:attr/floatingActionButtonSmallSecondaryStyle = 0x7f0301d3
com.tytfizikkamp:id/navigation_bar_item_small_label_view = 0x7f080132
com.tytfizikkamp:anim/catalyst_push_up_out = 0x7f01001b
com.tytfizikkamp:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f1102a4
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0500c5
com.tytfizikkamp:attr/itemBackground = 0x7f030231
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f11043b
com.tytfizikkamp:dimen/tooltip_corner_radius = 0x7f06031c
com.tytfizikkamp:attr/contentInsetEnd = 0x7f03012a
com.tytfizikkamp:style/Widget.AppCompat.Button.Small = 0x7f11030b
com.tytfizikkamp:layout/mtrl_alert_dialog = 0x7f0b0045
com.tytfizikkamp:attr/icon = 0x7f030217
com.tytfizikkamp:color/material_dynamic_tertiary0 = 0x7f050257
com.tytfizikkamp:attr/boxBackgroundMode = 0x7f030081
com.tytfizikkamp:attr/backgroundSplit = 0x7f030053
com.tytfizikkamp:attr/keyboardIcon = 0x7f03024f
com.tytfizikkamp:attr/materialButtonToggleGroupStyle = 0x7f0302c0
com.tytfizikkamp:attr/layout_constraintRight_creator = 0x7f03027b
com.tytfizikkamp:attr/customDimension = 0x7f030155
com.tytfizikkamp:attr/layout_constraintHeight_percent = 0x7f030274
com.tytfizikkamp:dimen/mtrl_calendar_day_today_stroke = 0x7f06027a
com.tytfizikkamp:color/design_default_color_on_primary = 0x7f050044
com.tytfizikkamp:attr/itemShapeInsetStart = 0x7f030243
com.tytfizikkamp:id/info = 0x7f0800de
com.tytfizikkamp:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f06013e
com.tytfizikkamp:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
com.tytfizikkamp:color/m3_timepicker_button_background_color = 0x7f050209
com.tytfizikkamp:id/rn_redbox_reload_button = 0x7f08016f
com.tytfizikkamp:attr/titleMargin = 0x7f030464
com.tytfizikkamp:styleable/MaterialCalendarItem = 0x7f120052
com.tytfizikkamp:layout/notification_action_tombstone = 0x7f0b0066
com.tytfizikkamp:attr/fabAlignmentMode = 0x7f0301b9
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f1102f7
com.tytfizikkamp:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f060159
com.tytfizikkamp:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f11006a
com.tytfizikkamp:id/text2 = 0x7f0801be
com.tytfizikkamp:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0301b6
com.tytfizikkamp:attr/endIconTintMode = 0x7f030198
com.tytfizikkamp:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0301b4
com.tytfizikkamp:id/mtrl_calendar_text_input_frame = 0x7f08011c
com.tytfizikkamp:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0301b3
com.tytfizikkamp:animator/fragment_fade_exit = 0x7f020006
com.tytfizikkamp:color/m3_icon_button_icon_color_selector = 0x7f05008f
com.tytfizikkamp:attr/addElevationShadow = 0x7f030029
com.tytfizikkamp:layout/mtrl_calendar_day = 0x7f0b004c
com.tytfizikkamp:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
com.tytfizikkamp:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1100e1
com.tytfizikkamp:anim/catalyst_slide_up = 0x7f01001d
com.tytfizikkamp:style/TextAppearance.Compat.Notification = 0x7f1101cd
com.tytfizikkamp:attr/expandedTitleGravity = 0x7f0301a9
com.tytfizikkamp:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
com.tytfizikkamp:macro/m3_comp_search_view_docked_container_shape = 0x7f0c00f3
com.tytfizikkamp:color/m3_sys_color_dynamic_light_primary = 0x7f0501b2
com.tytfizikkamp:style/Theme.Catalyst.RedBox = 0x7f11022f
com.tytfizikkamp:attr/expandedHintEnabled = 0x7f0301a8
com.tytfizikkamp:drawable/btn_checkbox_unchecked_mtrl = 0x7f07007c
com.tytfizikkamp:attr/thumbTrackGapSize = 0x7f030452
com.tytfizikkamp:dimen/mtrl_progress_circular_radius = 0x7f0602d9
com.tytfizikkamp:anim/rns_slide_in_from_left = 0x7f010047
com.tytfizikkamp:attr/expanded = 0x7f0301a7
com.tytfizikkamp:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f060197
com.tytfizikkamp:attr/expandActivityOverflowButtonDrawable = 0x7f0301a6
com.tytfizikkamp:attr/layout_constraintBaseline_toBaselineOf = 0x7f030264
com.tytfizikkamp:styleable/AppCompatImageView = 0x7f12000e
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1102d8
com.tytfizikkamp:attr/expandedTitleTextAppearance = 0x7f0301af
com.tytfizikkamp:attr/errorTextColor = 0x7f0301a5
com.tytfizikkamp:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f110242
com.tytfizikkamp:color/material_dynamic_color_dark_error = 0x7f05021b
com.tytfizikkamp:string/status_bar_notification_info_overflow = 0x7f1000db
com.tytfizikkamp:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0600a6
com.tytfizikkamp:attr/textAppearanceLineHeightEnabled = 0x7f030428
com.tytfizikkamp:attr/behavior_overlapTop = 0x7f030073
com.tytfizikkamp:layout/material_clock_period_toggle = 0x7f0b003a
com.tytfizikkamp:dimen/m3_alert_dialog_icon_margin = 0x7f0600a2
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1103c0
com.tytfizikkamp:attr/switchMinWidth = 0x7f0303eb
com.tytfizikkamp:attr/itemTextAppearance = 0x7f030248
com.tytfizikkamp:attr/errorEnabled = 0x7f03019f
com.tytfizikkamp:attr/duration = 0x7f030186
com.tytfizikkamp:dimen/mtrl_progress_circular_inset_small = 0x7f0602d8
com.tytfizikkamp:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
com.tytfizikkamp:attr/boxStrokeErrorColor = 0x7f030088
com.tytfizikkamp:dimen/mtrl_calendar_header_divider_thickness = 0x7f060281
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f11006e
com.tytfizikkamp:dimen/m3_btn_elevation = 0x7f0600d1
com.tytfizikkamp:attr/textInputOutlinedDenseStyle = 0x7f03043d
com.tytfizikkamp:attr/errorAccessibilityLiveRegion = 0x7f03019d
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral24 = 0x7f0500a6
com.tytfizikkamp:attr/autoSizeTextType = 0x7f030043
com.tytfizikkamp:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f110318
com.tytfizikkamp:attr/layout_constraintWidth_percent = 0x7f03028a
com.tytfizikkamp:style/Widget.MaterialComponents.CheckedTextView = 0x7f110424
com.tytfizikkamp:dimen/m3_navigation_rail_item_padding_top = 0x7f0601d3
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f1102ef
com.tytfizikkamp:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
com.tytfizikkamp:attr/ensureMinTouchTargetSize = 0x7f03019b
com.tytfizikkamp:macro/m3_comp_slider_label_label_text_color = 0x7f0c0112
com.tytfizikkamp:attr/enforceTextAppearance = 0x7f03019a
com.tytfizikkamp:attr/thumbIconSize = 0x7f030449
com.tytfizikkamp:attr/enforceMaterialTheme = 0x7f030199
com.tytfizikkamp:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0602c4
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f1102ec
com.tytfizikkamp:attr/listMenuViewStyle = 0x7f0302a7
com.tytfizikkamp:styleable/KeyPosition = 0x7f120044
com.tytfizikkamp:style/Base.V28.Theme.AppCompat.Light = 0x7f1100bc
com.tytfizikkamp:attr/subtitle = 0x7f0303e1
com.tytfizikkamp:styleable/CoordinatorLayout_Layout = 0x7f12002c
com.tytfizikkamp:dimen/mtrl_fab_translation_z_pressed = 0x7f0602ba
com.tytfizikkamp:id/view_tree_view_model_store_owner = 0x7f0801f0
com.tytfizikkamp:dimen/m3_comp_filter_chip_container_height = 0x7f06012c
com.tytfizikkamp:color/m3_sys_color_light_outline_variant = 0x7f0501df
com.tytfizikkamp:anim/rns_ios_from_left_foreground_open = 0x7f01003d
com.tytfizikkamp:style/TextAppearance.AppCompat.Display3 = 0x7f1101a4
com.tytfizikkamp:attr/endIconMode = 0x7f030195
com.tytfizikkamp:dimen/abc_text_size_headline_material = 0x7f060047
com.tytfizikkamp:layout/autofill_inline_suggestion = 0x7f0b001d
com.tytfizikkamp:color/m3_assist_chip_stroke_color = 0x7f050063
com.tytfizikkamp:dimen/notification_small_icon_size_as_large = 0x7f060318
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral6 = 0x7f0500ab
com.tytfizikkamp:attr/endIconMinSize = 0x7f030194
com.tytfizikkamp:integer/m3_sys_motion_duration_long2 = 0x7f090014
com.tytfizikkamp:attr/colorOnPrimaryContainer = 0x7f0300f4
com.tytfizikkamp:attr/enableEdgeToEdge = 0x7f030190
com.tytfizikkamp:color/bright_foreground_material_light = 0x7f050026
com.tytfizikkamp:attr/buttonTint = 0x7f03009a
com.tytfizikkamp:styleable/Capability = 0x7f12001a
com.tytfizikkamp:attr/colorOnSurfaceInverse = 0x7f0300fd
com.tytfizikkamp:color/mtrl_navigation_item_text_color = 0x7f0502d9
com.tytfizikkamp:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0501b9
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0500b9
com.tytfizikkamp:attr/animateNavigationIcon = 0x7f030033
com.tytfizikkamp:color/material_dynamic_primary90 = 0x7f050247
com.tytfizikkamp:macro/m3_comp_text_button_label_text_type = 0x7f0c0145
com.tytfizikkamp:attr/hintAnimationEnabled = 0x7f03020e
com.tytfizikkamp:id/progress_circular = 0x7f08015e
com.tytfizikkamp:attr/arrowHeadLength = 0x7f030039
com.tytfizikkamp:attr/cardPreventCornerOverlap = 0x7f0300a1
com.tytfizikkamp:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f060238
com.tytfizikkamp:attr/dropDownBackgroundTint = 0x7f030183
com.tytfizikkamp:style/Widget.Autofill.InlineSuggestionSubtitle = 0x7f110348
com.tytfizikkamp:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f1102aa
com.tytfizikkamp:color/bright_foreground_material_dark = 0x7f050025
com.tytfizikkamp:attr/drawableTopCompat = 0x7f03017f
com.tytfizikkamp:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0600ad
com.tytfizikkamp:attr/behavior_saveFlags = 0x7f030075
com.tytfizikkamp:attr/layout_constraintCircle = 0x7f030268
com.tytfizikkamp:attr/clockHandColor = 0x7f0300ce
com.tytfizikkamp:animator/design_fab_hide_motion_spec = 0x7f020001
com.tytfizikkamp:attr/drawableTintMode = 0x7f03017e
com.tytfizikkamp:string/abc_capital_off = 0x7f100006
com.tytfizikkamp:attr/labelVisibilityMode = 0x7f030254
com.tytfizikkamp:color/m3_sys_color_dark_surface_container_highest = 0x7f050176
com.tytfizikkamp:style/TextAppearance.Material3.HeadlineMedium = 0x7f1101f5
com.tytfizikkamp:attr/maxVelocity = 0x7f0302ef
com.tytfizikkamp:dimen/mtrl_switch_track_height = 0x7f0602fb
com.tytfizikkamp:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f060115
com.tytfizikkamp:color/material_personalized_color_outline_variant = 0x7f05028e
com.tytfizikkamp:attr/arrowShaftLength = 0x7f03003a
com.tytfizikkamp:attr/dividerInsetStart = 0x7f03016f
com.tytfizikkamp:animator/design_appbar_state_list_animator = 0x7f020000
com.tytfizikkamp:attr/tabPadding = 0x7f0303ff
com.tytfizikkamp:id/icon = 0x7f0800d7
com.tytfizikkamp:attr/drawableSize = 0x7f03017b
com.tytfizikkamp:string/timer_description = 0x7f1000de
com.tytfizikkamp:color/m3_dynamic_dark_highlighted_text = 0x7f05007f
com.tytfizikkamp:attr/cardMaxElevation = 0x7f0300a0
com.tytfizikkamp:attr/drawableRightCompat = 0x7f03017a
com.tytfizikkamp:attr/isAutofillInlineSuggestionTheme = 0x7f03022b
com.tytfizikkamp:attr/hideOnScroll = 0x7f03020d
com.tytfizikkamp:attr/tintMode = 0x7f03045e
com.tytfizikkamp:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f11042c
com.tytfizikkamp:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0c00e2
com.tytfizikkamp:drawable/abc_text_select_handle_left_mtrl = 0x7f07006e
com.tytfizikkamp:style/TextAppearance.AppCompat.Body1 = 0x7f11019e
com.tytfizikkamp:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f11015a
com.tytfizikkamp:color/m3_sys_color_dark_surface_container_low = 0x7f050177
com.tytfizikkamp:attr/indicatorSize = 0x7f030227
com.tytfizikkamp:attr/chipMinHeight = 0x7f0300bf
com.tytfizikkamp:attr/dragThreshold = 0x7f030175
com.tytfizikkamp:dimen/mtrl_btn_z = 0x7f060271
com.tytfizikkamp:color/m3_textfield_stroke_color = 0x7f050208
com.tytfizikkamp:attr/flow_horizontalAlign = 0x7f0301de
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f110038
com.tytfizikkamp:anim/rns_fade_to_bottom = 0x7f010039
com.tytfizikkamp:attr/actionModeTheme = 0x7f03001d
com.tytfizikkamp:attr/badgeHeight = 0x7f030058
com.tytfizikkamp:attr/dividerInsetEnd = 0x7f03016e
com.tytfizikkamp:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f070042
com.tytfizikkamp:color/m3_ref_palette_primary99 = 0x7f050134
com.tytfizikkamp:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1100c6
com.tytfizikkamp:attr/textColorSearchUrl = 0x7f030437
com.tytfizikkamp:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f110193
com.tytfizikkamp:id/ifRoom = 0x7f0800d9
com.tytfizikkamp:attr/windowFixedHeightMajor = 0x7f0304a6
com.tytfizikkamp:attr/dividerColor = 0x7f03016c
com.tytfizikkamp:string/mtrl_checkbox_button_path_checked = 0x7f10008a
com.tytfizikkamp:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f0700ab
com.tytfizikkamp:attr/tickColor = 0x7f030454
com.tytfizikkamp:style/ThemeOverlay.AppCompat.Dialog = 0x7f11028f
com.tytfizikkamp:color/m3_ref_palette_secondary40 = 0x7f05013a
com.tytfizikkamp:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0602b5
com.tytfizikkamp:dimen/m3_badge_offset = 0x7f0600b5
com.tytfizikkamp:style/Theme.AppCompat.Dialog.MinWidth = 0x7f110221
com.tytfizikkamp:anim/rns_fade_from_bottom = 0x7f010036
com.tytfizikkamp:integer/m3_card_anim_delay_ms = 0x7f09000c
com.tytfizikkamp:attr/indicatorDirectionCircular = 0x7f030224
com.tytfizikkamp:styleable/AnimatedStateListDrawableTransition = 0x7f120009
com.tytfizikkamp:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f070020
com.tytfizikkamp:attr/deltaPolarRadius = 0x7f030165
com.tytfizikkamp:dimen/abc_text_size_display_3_material = 0x7f060045
com.tytfizikkamp:attr/alpha = 0x7f03002f
com.tytfizikkamp:attr/flow_firstHorizontalStyle = 0x7f0301db
com.tytfizikkamp:drawable/design_password_eye = 0x7f070085
com.tytfizikkamp:id/selection_type = 0x7f08018c
com.tytfizikkamp:attr/defaultScrollFlagsEnabled = 0x7f030162
com.tytfizikkamp:id/showCustom = 0x7f08018e
com.tytfizikkamp:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.tytfizikkamp:attr/defaultQueryHint = 0x7f030161
com.tytfizikkamp:style/Base.Widget.AppCompat.ActionButton = 0x7f1100ca
com.tytfizikkamp:anim/design_bottom_sheet_slide_out = 0x7f01001f
com.tytfizikkamp:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1102d0
com.tytfizikkamp:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f110268
com.tytfizikkamp:attr/dayTodayStyle = 0x7f03015e
com.tytfizikkamp:styleable/MenuItem = 0x7f12005f
com.tytfizikkamp:id/alert_title = 0x7f080053
com.tytfizikkamp:dimen/mtrl_calendar_day_width = 0x7f06027c
com.tytfizikkamp:attr/showTitle = 0x7f0303ae
com.tytfizikkamp:color/highlighted_text_material_dark = 0x7f05005f
com.tytfizikkamp:attr/daySelectedStyle = 0x7f03015c
com.tytfizikkamp:color/m3_sys_color_light_inverse_surface = 0x7f0501d2
com.tytfizikkamp:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
com.tytfizikkamp:color/m3_ref_palette_dynamic_primary90 = 0x7f0500d9
com.tytfizikkamp:attr/elevationOverlayAccentColor = 0x7f03018c
com.tytfizikkamp:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f060287
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f05019c
com.tytfizikkamp:string/summary_description = 0x7f1000dc
com.tytfizikkamp:attr/textAppearanceButton = 0x7f030416
com.tytfizikkamp:color/m3_card_foreground_color = 0x7f05006c
com.tytfizikkamp:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f06020b
com.tytfizikkamp:color/material_dynamic_neutral_variant40 = 0x7f050235
com.tytfizikkamp:attr/layout_constraintVertical_chainStyle = 0x7f030285
com.tytfizikkamp:color/abc_tint_switch_track = 0x7f050018
com.tytfizikkamp:attr/customPixelDimension = 0x7f030159
com.tytfizikkamp:string/abc_toolbar_collapse_description = 0x7f10001a
com.tytfizikkamp:attr/toggleCheckedStateOnClick = 0x7f03046f
com.tytfizikkamp:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f110227
com.tytfizikkamp:attr/colorSurfaceContainerHigh = 0x7f030115
com.tytfizikkamp:attr/customNavigationLayout = 0x7f030158
com.tytfizikkamp:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f11011b
com.tytfizikkamp:attr/badgeWidth = 0x7f030062
com.tytfizikkamp:attr/customFloatValue = 0x7f030156
com.tytfizikkamp:style/ThemeOverlay.Material3.Chip = 0x7f1102a6
com.tytfizikkamp:attr/verticalOffsetWithText = 0x7f030498
com.tytfizikkamp:attr/textEndPadding = 0x7f030438
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f110036
com.tytfizikkamp:attr/listPopupWindowStyle = 0x7f0302a8
com.tytfizikkamp:attr/cornerFamilyTopLeft = 0x7f03013f
com.tytfizikkamp:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f060105
com.tytfizikkamp:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f110051
com.tytfizikkamp:attr/hideAnimationBehavior = 0x7f030209
com.tytfizikkamp:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1103bb
com.tytfizikkamp:attr/alertDialogButtonGroupStyle = 0x7f03002a
com.tytfizikkamp:drawable/navigation_empty_icon = 0x7f0700d5
com.tytfizikkamp:attr/customColorValue = 0x7f030154
com.tytfizikkamp:attr/customBoolean = 0x7f030152
com.tytfizikkamp:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1103d4
com.tytfizikkamp:id/right_icon = 0x7f080168
com.tytfizikkamp:drawable/material_ic_calendar_black_24dp = 0x7f0700a7
com.tytfizikkamp:id/accessibility_collection = 0x7f080011
com.tytfizikkamp:attr/colorOutline = 0x7f030103
com.tytfizikkamp:attr/errorIconDrawable = 0x7f0301a0
com.tytfizikkamp:styleable/MaterialRadioButton = 0x7f120057
com.tytfizikkamp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f110153
com.tytfizikkamp:drawable/design_fab_background = 0x7f070082
com.tytfizikkamp:attr/counterTextColor = 0x7f03014c
com.tytfizikkamp:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f050189
com.tytfizikkamp:attr/transitionFlags = 0x7f03048c
com.tytfizikkamp:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010026
com.tytfizikkamp:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0c00cf
com.tytfizikkamp:attr/counterOverflowTextColor = 0x7f03014a
com.tytfizikkamp:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0501bb
com.tytfizikkamp:style/TextAppearance.Design.Suffix = 0x7f1101db
com.tytfizikkamp:attr/titleMarginBottom = 0x7f030465
com.tytfizikkamp:string/item_view_role_description = 0x7f10005a
com.tytfizikkamp:attr/layout_constraintHeight_min = 0x7f030273
com.tytfizikkamp:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.tytfizikkamp:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0c007e
com.tytfizikkamp:color/m3_sys_color_on_secondary_fixed = 0x7f0501f1
com.tytfizikkamp:attr/bottomSheetDialogTheme = 0x7f03007d
com.tytfizikkamp:attr/hideNavigationIcon = 0x7f03020b
com.tytfizikkamp:attr/layout_constraintDimensionRatio = 0x7f03026b
com.tytfizikkamp:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f0502a6
com.tytfizikkamp:dimen/notification_right_side_padding_top = 0x7f060316
com.tytfizikkamp:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1102b1
com.tytfizikkamp:attr/actionModeSplitBackground = 0x7f03001b
com.tytfizikkamp:attr/layout_constraintGuide_end = 0x7f03026f
com.tytfizikkamp:color/m3_switch_thumb_tint = 0x7f050159
com.tytfizikkamp:interpolator/fast_out_slow_in = 0x7f0a0006
com.tytfizikkamp:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f06015e
com.tytfizikkamp:style/Widget.MaterialComponents.Button.Icon = 0x7f110418
com.tytfizikkamp:color/material_dynamic_secondary90 = 0x7f050254
com.tytfizikkamp:attr/textAppearanceOverline = 0x7f03042c
com.tytfizikkamp:attr/cornerFamilyBottomRight = 0x7f03013e
com.tytfizikkamp:attr/titleMargins = 0x7f030469
com.tytfizikkamp:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f060206
com.tytfizikkamp:style/Widget.Material3.Chip.Assist = 0x7f110380
com.tytfizikkamp:attr/cornerFamilyBottomLeft = 0x7f03013d
com.tytfizikkamp:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f07007d
com.tytfizikkamp:id/accessibility_custom_action_9 = 0x7f080032
com.tytfizikkamp:attr/framePosition = 0x7f0301fd
com.tytfizikkamp:id/action_divider = 0x7f080047
com.tytfizikkamp:dimen/mtrl_badge_with_text_size = 0x7f060253
com.tytfizikkamp:attr/bottomSheetDragHandleStyle = 0x7f03007e
com.tytfizikkamp:style/redboxButton = 0x7f110481
com.tytfizikkamp:attr/coordinatorLayoutStyle = 0x7f03013a
com.tytfizikkamp:attr/actualImageScaleType = 0x7f030027
com.tytfizikkamp:dimen/design_bottom_navigation_active_text_size = 0x7f060062
com.tytfizikkamp:style/Widget.Material3.SideSheet.Detached = 0x7f1103e5
com.tytfizikkamp:attr/collapsedTitleTextAppearance = 0x7f0300dd
com.tytfizikkamp:color/m3_ref_palette_dynamic_secondary10 = 0x7f0500dd
com.tytfizikkamp:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f1103a8
com.tytfizikkamp:dimen/notification_small_icon_background_padding = 0x7f060317
com.tytfizikkamp:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f110430
com.tytfizikkamp:attr/actionDropDownStyle = 0x7f03000c
com.tytfizikkamp:styleable/PopupWindow = 0x7f12006d
com.tytfizikkamp:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f0600fe
com.tytfizikkamp:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0c00a1
com.tytfizikkamp:color/mtrl_error = 0x7f0502cb
com.tytfizikkamp:attr/errorShown = 0x7f0301a3
com.tytfizikkamp:attr/contentScrim = 0x7f030137
com.tytfizikkamp:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0600b3
com.tytfizikkamp:color/material_dynamic_color_dark_error_container = 0x7f05021c
com.tytfizikkamp:attr/contentPaddingStart = 0x7f030135
com.tytfizikkamp:attr/startIconScaleType = 0x7f0303ca
com.tytfizikkamp:attr/badgeShapeAppearanceOverlay = 0x7f03005b
com.tytfizikkamp:dimen/mtrl_calendar_action_height = 0x7f060273
com.tytfizikkamp:attr/contentPaddingBottom = 0x7f030131
com.tytfizikkamp:dimen/design_bottom_navigation_text_size = 0x7f06006b
com.tytfizikkamp:style/SpinnerDatePickerStyle = 0x7f11019c
com.tytfizikkamp:attr/contentPadding = 0x7f030130
com.tytfizikkamp:attr/colorOnSurfaceVariant = 0x7f0300fe
com.tytfizikkamp:attr/voiceIcon = 0x7f03049c
com.tytfizikkamp:attr/tabPaddingEnd = 0x7f030401
com.tytfizikkamp:attr/boxCollapsedPaddingTop = 0x7f030082
com.tytfizikkamp:attr/contentInsetEndWithActions = 0x7f03012b
com.tytfizikkamp:attr/transitionDisable = 0x7f03048a
com.tytfizikkamp:attr/actionModeCloseButtonStyle = 0x7f030011
com.tytfizikkamp:attr/materialAlertDialogTitleIconStyle = 0x7f0302bb
com.tytfizikkamp:attr/backgroundInsetBottom = 0x7f03004e
com.tytfizikkamp:attr/colorSurfaceContainerLow = 0x7f030117
com.tytfizikkamp:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f11030d
com.tytfizikkamp:attr/constraintSet = 0x7f030123
com.tytfizikkamp:style/Widget.AppCompat.Button = 0x7f110306
com.tytfizikkamp:drawable/paused_in_debugger_dialog_background = 0x7f0700e9
com.tytfizikkamp:attr/colorSurfaceInverse = 0x7f03011a
com.tytfizikkamp:attr/chipEndPadding = 0x7f0300b8
com.tytfizikkamp:attr/flow_verticalGap = 0x7f0301ea
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f110044
com.tytfizikkamp:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f06024b
com.tytfizikkamp:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0c0149
com.tytfizikkamp:attr/endIconScaleType = 0x7f030196
com.tytfizikkamp:attr/clockIcon = 0x7f0300cf
com.tytfizikkamp:attr/chipIconVisible = 0x7f0300be
com.tytfizikkamp:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1100ee
com.tytfizikkamp:attr/itemShapeInsetBottom = 0x7f030241
com.tytfizikkamp:attr/colorTertiary = 0x7f03011d
com.tytfizikkamp:dimen/m3_btn_inset = 0x7f0600d8
com.tytfizikkamp:attr/ratingBarStyle = 0x7f03036f
com.tytfizikkamp:attr/placeholderTextAppearance = 0x7f030358
com.tytfizikkamp:string/path_password_strike_through = 0x7f1000c7
com.tytfizikkamp:dimen/mtrl_navigation_rail_margin = 0x7f0602d2
com.tytfizikkamp:dimen/mtrl_calendar_dialog_background_inset = 0x7f06027e
com.tytfizikkamp:attr/colorPrimaryContainer = 0x7f030106
com.tytfizikkamp:style/Theme.Material3.Light.NoActionBar = 0x7f110255
com.tytfizikkamp:attr/colorOutlineVariant = 0x7f030104
com.tytfizikkamp:color/m3_sys_color_light_tertiary_container = 0x7f0501ee
com.tytfizikkamp:attr/listChoiceBackgroundIndicator = 0x7f0302a1
com.tytfizikkamp:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1103db
com.tytfizikkamp:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f11014c
com.tytfizikkamp:attr/badgeWithTextShapeAppearanceOverlay = 0x7f030066
com.tytfizikkamp:styleable/ActionBar = 0x7f120000
com.tytfizikkamp:string/call_notification_incoming_text = 0x7f100029
com.tytfizikkamp:attr/colorOnTertiary = 0x7f0300ff
com.tytfizikkamp:attr/colorOnSecondary = 0x7f0300f8
com.tytfizikkamp:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f11040d
com.tytfizikkamp:attr/buttonBarButtonStyle = 0x7f03008c
com.tytfizikkamp:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.tytfizikkamp:layout/mtrl_calendar_days_of_week = 0x7f0b004e
com.tytfizikkamp:attr/layout_constraintGuide_percent = 0x7f030270
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0500b8
com.tytfizikkamp:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f11046d
com.tytfizikkamp:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0c00e0
com.tytfizikkamp:dimen/abc_action_bar_elevation_material = 0x7f060005
com.tytfizikkamp:attr/navigationRailStyle = 0x7f03032b
com.tytfizikkamp:attr/panelMenuListWidth = 0x7f030347
com.tytfizikkamp:attr/layout_constraintVertical_bias = 0x7f030284
com.tytfizikkamp:id/snap = 0x7f080196
com.tytfizikkamp:color/m3_navigation_item_text_color = 0x7f050096
com.tytfizikkamp:color/material_personalized_primary_text_disable_only = 0x7f0502ac
com.tytfizikkamp:id/transition_image_transform = 0x7f0801db
com.tytfizikkamp:attr/thumbTintMode = 0x7f030451
com.tytfizikkamp:macro/m3_comp_dialog_container_color = 0x7f0c0022
com.tytfizikkamp:attr/colorOnPrimaryFixed = 0x7f0300f5
com.tytfizikkamp:color/m3_ref_palette_secondary0 = 0x7f050135
com.tytfizikkamp:string/mtrl_switch_thumb_path_unchecked = 0x7f1000be
com.tytfizikkamp:id/easeOut = 0x7f0800a8
com.tytfizikkamp:dimen/m3_divider_heavy_thickness = 0x7f0601af
com.tytfizikkamp:style/Theme.AppCompat.Empty = 0x7f110223
com.tytfizikkamp:color/material_dynamic_secondary50 = 0x7f050250
com.tytfizikkamp:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0501c2
com.tytfizikkamp:attr/singleSelection = 0x7f0303b8
com.tytfizikkamp:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f06019c
com.tytfizikkamp:attr/actionMenuTextColor = 0x7f03000f
com.tytfizikkamp:color/mtrl_textinput_focused_box_stroke_color = 0x7f0502ed
com.tytfizikkamp:attr/colorErrorContainer = 0x7f0300ed
com.tytfizikkamp:attr/floatingActionButtonSmallPrimaryStyle = 0x7f0301d2
com.tytfizikkamp:anim/rns_ios_from_right_foreground_close = 0x7f010040
com.tytfizikkamp:attr/colorError = 0x7f0300ec
com.tytfizikkamp:id/open_search_view_scrim = 0x7f080146
com.tytfizikkamp:drawable/mtrl_switch_thumb_checked_pressed = 0x7f0700ca
com.tytfizikkamp:drawable/abc_list_selector_disabled_holo_dark = 0x7f070054
com.tytfizikkamp:dimen/mtrl_shape_corner_size_small_component = 0x7f0602e5
com.tytfizikkamp:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0c009a
com.tytfizikkamp:attr/isMaterial3DynamicColorApplied = 0x7f03022d
com.tytfizikkamp:attr/cornerRadius = 0x7f030141
com.tytfizikkamp:dimen/m3_comp_snackbar_container_elevation = 0x7f06018a
com.tytfizikkamp:attr/windowMinWidthMajor = 0x7f0304aa
com.tytfizikkamp:attr/pathMotionArc = 0x7f03034d
com.tytfizikkamp:attr/allowStacking = 0x7f03002e
com.tytfizikkamp:color/m3_sys_color_light_error_container = 0x7f0501cf
com.tytfizikkamp:layout/m3_alert_dialog_actions = 0x7f0b0033
com.tytfizikkamp:attr/badgeWithTextShapeAppearance = 0x7f030065
com.tytfizikkamp:attr/itemIconSize = 0x7f030236
com.tytfizikkamp:attr/collapsingToolbarLayoutLargeSize = 0x7f0300df
com.tytfizikkamp:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f110308
com.tytfizikkamp:attr/fastScrollVerticalTrackDrawable = 0x7f0301c9
com.tytfizikkamp:dimen/compat_notification_large_icon_max_height = 0x7f06005c
com.tytfizikkamp:attr/collapsedSize = 0x7f0300db
com.tytfizikkamp:color/m3_ref_palette_primary10 = 0x7f050129
com.tytfizikkamp:color/m3_ref_palette_neutral_variant50 = 0x7f050121
com.tytfizikkamp:attr/cornerFamily = 0x7f03013c
com.tytfizikkamp:dimen/mtrl_progress_circular_size = 0x7f0602da
com.tytfizikkamp:attr/colorOnBackground = 0x7f0300ee
com.tytfizikkamp:attr/bottomInsetScrimEnabled = 0x7f03007b
com.tytfizikkamp:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1101b4
com.tytfizikkamp:style/Base.Widget.Material3.ActionBar.Solid = 0x7f110100
com.tytfizikkamp:color/mtrl_navigation_bar_ripple_color = 0x7f0502d6
com.tytfizikkamp:attr/reverseLayout = 0x7f03037a
com.tytfizikkamp:style/Theme.ReactNative.AppCompat.Light = 0x7f110286
com.tytfizikkamp:attr/fontProviderFallbackQuery = 0x7f0301f1
com.tytfizikkamp:attr/fabCradleMargin = 0x7f0301bd
com.tytfizikkamp:attr/roundWithOverlayColor = 0x7f030387
com.tytfizikkamp:attr/itemTextAppearanceInactive = 0x7f03024b
com.tytfizikkamp:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f1103dd
com.tytfizikkamp:dimen/m3_comp_sheet_side_docked_container_width = 0x7f06017f
com.tytfizikkamp:attr/searchHintIcon = 0x7f030390
com.tytfizikkamp:attr/chipSpacingHorizontal = 0x7f0300c2
com.tytfizikkamp:attr/chipIconTint = 0x7f0300bd
com.tytfizikkamp:styleable/SwitchMaterial = 0x7f120086
com.tytfizikkamp:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.tytfizikkamp:attr/mock_diagonalsColor = 0x7f0302fa
com.tytfizikkamp:attr/textAppearanceLabelLarge = 0x7f030424
com.tytfizikkamp:layout/material_timepicker = 0x7f0b0042
com.tytfizikkamp:attr/contentPaddingEnd = 0x7f030132
com.tytfizikkamp:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f11006d
com.tytfizikkamp:dimen/m3_card_elevated_hovered_z = 0x7f0600ea
com.tytfizikkamp:color/material_slider_active_track_color = 0x7f0502ae
com.tytfizikkamp:attr/behavior_skipCollapsed = 0x7f030077
com.tytfizikkamp:color/material_personalized_color_surface_container = 0x7f05029a
com.tytfizikkamp:attr/flow_lastHorizontalStyle = 0x7f0301e3
com.tytfizikkamp:id/startToEnd = 0x7f0801a5
com.tytfizikkamp:anim/abc_popup_enter = 0x7f010003
com.tytfizikkamp:color/m3_ref_palette_error10 = 0x7f0500f7
com.tytfizikkamp:drawable/mtrl_ic_cancel = 0x7f0700bf
com.tytfizikkamp:attr/constraintSetStart = 0x7f030125
com.tytfizikkamp:integer/m3_sys_shape_corner_small_corner_family = 0x7f090025
com.tytfizikkamp:id/material_minute_tv = 0x7f080101
com.tytfizikkamp:attr/flow_wrapMode = 0x7f0301ec
com.tytfizikkamp:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0600bd
com.tytfizikkamp:attr/checkedTextViewStyle = 0x7f0300b5
com.tytfizikkamp:attr/toolbarStyle = 0x7f030472
com.tytfizikkamp:string/catalyst_hot_reloading_stop = 0x7f10003e
com.tytfizikkamp:attr/checkedIconVisible = 0x7f0300b3
com.tytfizikkamp:string/material_timepicker_hour = 0x7f10007d
com.tytfizikkamp:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0c00ad
com.tytfizikkamp:dimen/design_tab_scrollable_min_width = 0x7f06008b
com.tytfizikkamp:attr/actionOverflowButtonStyle = 0x7f03001f
com.tytfizikkamp:attr/checkedIconTint = 0x7f0300b2
com.tytfizikkamp:color/m3_calendar_item_stroke_color = 0x7f05006b
com.tytfizikkamp:attr/layout_constraintBottom_toBottomOf = 0x7f030266
com.tytfizikkamp:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f110058
com.tytfizikkamp:attr/hideMotionSpec = 0x7f03020a
com.tytfizikkamp:color/m3_sys_color_dark_surface_variant = 0x7f05017a
com.tytfizikkamp:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0c0031
com.tytfizikkamp:attr/checkedIconMargin = 0x7f0300b0
com.tytfizikkamp:attr/isLightTheme = 0x7f03022c
com.tytfizikkamp:attr/bottomSheetStyle = 0x7f03007f
com.tytfizikkamp:style/CalendarDatePickerStyle = 0x7f110123
com.tytfizikkamp:attr/checkedIconEnabled = 0x7f0300ae
com.tytfizikkamp:id/chains = 0x7f080076
com.tytfizikkamp:attr/layout_optimizationLevel = 0x7f030296
com.tytfizikkamp:attr/checkedIcon = 0x7f0300ad
com.tytfizikkamp:id/material_timepicker_mode_button = 0x7f080105
com.tytfizikkamp:attr/colorOnContainerUnchecked = 0x7f0300f0
com.tytfizikkamp:attr/popupTheme = 0x7f03035d
com.tytfizikkamp:attr/checkedChip = 0x7f0300ac
com.tytfizikkamp:attr/motionPath = 0x7f03031e
com.tytfizikkamp:styleable/CustomAttribute = 0x7f12002d
com.tytfizikkamp:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f11035a
com.tytfizikkamp:attr/materialIconButtonOutlinedStyle = 0x7f0302db
com.tytfizikkamp:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f06020d
com.tytfizikkamp:anim/catalyst_fade_in = 0x7f010018
com.tytfizikkamp:attr/fabAnchorMode = 0x7f0301bb
com.tytfizikkamp:attr/haloColor = 0x7f030201
com.tytfizikkamp:color/material_deep_teal_500 = 0x7f050219
com.tytfizikkamp:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f11007f
com.tytfizikkamp:attr/checkedButton = 0x7f0300ab
com.tytfizikkamp:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f110320
com.tytfizikkamp:drawable/notification_bg_low_pressed = 0x7f0700df
com.tytfizikkamp:id/disableHome = 0x7f08009b
com.tytfizikkamp:id/animateToEnd = 0x7f080057
com.tytfizikkamp:dimen/m3_alert_dialog_corner_size = 0x7f0600a0
com.tytfizikkamp:attr/colorSurfaceVariant = 0x7f03011b
com.tytfizikkamp:attr/layout_constrainedWidth = 0x7f030262
com.tytfizikkamp:id/accessibility_custom_action_15 = 0x7f08001a
com.tytfizikkamp:id/open_search_view_toolbar = 0x7f080149
com.tytfizikkamp:color/m3_ref_palette_neutral80 = 0x7f050112
com.tytfizikkamp:attr/centerIfNoTextEnabled = 0x7f0300a5
com.tytfizikkamp:dimen/material_time_picker_minimum_screen_width = 0x7f060246
com.tytfizikkamp:anim/abc_slide_out_top = 0x7f010009
com.tytfizikkamp:attr/chipSpacing = 0x7f0300c1
com.tytfizikkamp:color/abc_search_url_text_normal = 0x7f05000e
com.tytfizikkamp:attr/bottomNavigationStyle = 0x7f03007c
com.tytfizikkamp:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f110437
com.tytfizikkamp:string/abc_menu_alt_shortcut_label = 0x7f100008
com.tytfizikkamp:dimen/design_snackbar_min_width = 0x7f060085
com.tytfizikkamp:style/ThemeOverlay.MaterialComponents = 0x7f1102d3
com.tytfizikkamp:attr/materialCalendarDay = 0x7f0302c1
com.tytfizikkamp:layout/support_simple_spinner_dropdown_item = 0x7f0b0072
com.tytfizikkamp:attr/color = 0x7f0300e4
com.tytfizikkamp:anim/m3_side_sheet_exit_to_right = 0x7f01002e
com.tytfizikkamp:styleable/MockView = 0x7f120061
com.tytfizikkamp:animator/mtrl_fab_show_motion_spec = 0x7f02001f
com.tytfizikkamp:attr/autoTransition = 0x7f030044
com.tytfizikkamp:color/m3_assist_chip_icon_tint_color = 0x7f050062
com.tytfizikkamp:attr/badgeTextColor = 0x7f03005f
com.tytfizikkamp:attr/textAppearanceHeadlineLarge = 0x7f030421
com.tytfizikkamp:style/Widget.Material3.ActionBar.Solid = 0x7f110357
com.tytfizikkamp:attr/buttonStyle = 0x7f030098
com.tytfizikkamp:styleable/MotionTelltales = 0x7f120066
com.tytfizikkamp:attr/stackFromEnd = 0x7f0303c4
com.tytfizikkamp:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0c0007
com.tytfizikkamp:color/call_notification_answer_color = 0x7f050029
com.tytfizikkamp:id/accessibility_links = 0x7f080035
com.tytfizikkamp:style/Theme.Material3.Light = 0x7f11024f
com.tytfizikkamp:color/m3_slider_active_track_color = 0x7f050152
com.tytfizikkamp:id/end = 0x7f0800ae
com.tytfizikkamp:attr/behavior_peekHeight = 0x7f030074
com.tytfizikkamp:attr/colorControlNormal = 0x7f0300eb
com.tytfizikkamp:attr/itemShapeInsetEnd = 0x7f030242
com.tytfizikkamp:attr/startIconTintMode = 0x7f0303cc
com.tytfizikkamp:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f06021c
com.tytfizikkamp:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070060
com.tytfizikkamp:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
com.tytfizikkamp:attr/buttonIconTint = 0x7f030095
com.tytfizikkamp:attr/customIntegerValue = 0x7f030157
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f11002b
com.tytfizikkamp:animator/mtrl_btn_state_list_anim = 0x7f020015
com.tytfizikkamp:attr/roundTopEnd = 0x7f030383
com.tytfizikkamp:attr/buttonGravity = 0x7f030092
com.tytfizikkamp:id/accessibility_label = 0x7f080034
com.tytfizikkamp:dimen/material_clock_hand_stroke_width = 0x7f06022a
com.tytfizikkamp:attr/round = 0x7f03037c
com.tytfizikkamp:attr/thumbHeight = 0x7f030447
com.tytfizikkamp:dimen/notification_right_icon_size = 0x7f060315
com.tytfizikkamp:attr/backgroundInsetEnd = 0x7f03004f
com.tytfizikkamp:color/material_dynamic_primary99 = 0x7f050249
com.tytfizikkamp:color/catalyst_redbox_background = 0x7f050030
com.tytfizikkamp:dimen/material_clock_period_toggle_width = 0x7f06022f
com.tytfizikkamp:attr/layout_constraintWidth_min = 0x7f030289
com.tytfizikkamp:attr/trackStopIndicatorSize = 0x7f030486
com.tytfizikkamp:styleable/TextInputLayout = 0x7f12008b
com.tytfizikkamp:attr/thumbStrokeWidth = 0x7f03044e
com.tytfizikkamp:dimen/mtrl_calendar_day_height = 0x7f060278
com.tytfizikkamp:attr/singleLine = 0x7f0303b7
com.tytfizikkamp:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f1103f6
com.tytfizikkamp:drawable/mtrl_ic_checkbox_unchecked = 0x7f0700c2
com.tytfizikkamp:attr/deriveConstraintsFrom = 0x7f030166
com.tytfizikkamp:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0c0159
com.tytfizikkamp:anim/rns_ios_from_left_background_close = 0x7f01003a
com.tytfizikkamp:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f1103a6
com.tytfizikkamp:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f060191
com.tytfizikkamp:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.tytfizikkamp:string/app_name = 0x7f10001d
com.tytfizikkamp:attr/colorSurface = 0x7f030112
com.tytfizikkamp:color/material_dynamic_neutral90 = 0x7f05022d
com.tytfizikkamp:attr/collapsedTitleGravity = 0x7f0300dc
com.tytfizikkamp:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0600a9
com.tytfizikkamp:attr/actualImageUri = 0x7f030028
com.tytfizikkamp:dimen/material_textinput_default_width = 0x7f060242
com.tytfizikkamp:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f030315
com.tytfizikkamp:attr/boxStrokeWidth = 0x7f030089
com.tytfizikkamp:attr/boxCornerRadiusTopStart = 0x7f030086
com.tytfizikkamp:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1101ac
com.tytfizikkamp:attr/colorPrimarySurface = 0x7f03010b
com.tytfizikkamp:dimen/material_clock_display_height = 0x7f060223
com.tytfizikkamp:attr/boxCornerRadiusBottomStart = 0x7f030084
com.tytfizikkamp:attr/actionModeSelectAllDrawable = 0x7f030019
com.tytfizikkamp:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
com.tytfizikkamp:attr/collapsingToolbarLayoutLargeStyle = 0x7f0300e0
com.tytfizikkamp:drawable/redbox_top_border_background = 0x7f0700ea
com.tytfizikkamp:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f060205
com.tytfizikkamp:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f110109
com.tytfizikkamp:attr/actionModeShareDrawable = 0x7f03001a
com.tytfizikkamp:color/material_personalized_color_tertiary = 0x7f0502a2
com.tytfizikkamp:attr/barrierMargin = 0x7f03006b
com.tytfizikkamp:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f110467
com.tytfizikkamp:attr/layout_constraintVertical_weight = 0x7f030286
com.tytfizikkamp:attr/colorOnTertiaryContainer = 0x7f030100
com.tytfizikkamp:string/mtrl_timepicker_confirm = 0x7f1000c2
com.tytfizikkamp:id/transition_position = 0x7f0801de
com.tytfizikkamp:color/button_material_light = 0x7f050028
com.tytfizikkamp:style/TextAppearance.Material3.BodyMedium = 0x7f1101ef
com.tytfizikkamp:attr/borderlessButtonStyle = 0x7f030079
com.tytfizikkamp:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f07001e
com.tytfizikkamp:style/TextAppearance.Material3.TitleMedium = 0x7f1101ff
com.tytfizikkamp:dimen/mtrl_badge_horizontal_edge_offset = 0x7f06024c
com.tytfizikkamp:color/material_grey_850 = 0x7f050269
com.tytfizikkamp:attr/collapsingToolbarLayoutMediumStyle = 0x7f0300e2
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral100 = 0x7f0500a1
com.tytfizikkamp:color/m3_ref_palette_error80 = 0x7f0500ff
com.tytfizikkamp:attr/behavior_hideable = 0x7f030072
com.tytfizikkamp:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0c0062
com.tytfizikkamp:drawable/abc_textfield_activated_mtrl_alpha = 0x7f070071
com.tytfizikkamp:id/save_non_transition_alpha = 0x7f080176
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral92 = 0x7f0500b1
com.tytfizikkamp:attr/colorControlActivated = 0x7f0300e9
com.tytfizikkamp:attr/behavior_draggable = 0x7f03006e
com.tytfizikkamp:color/material_personalized_color_surface_dim = 0x7f05029f
com.tytfizikkamp:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1100d8
com.tytfizikkamp:attr/itemShapeInsetTop = 0x7f030244
com.tytfizikkamp:attr/colorPrimaryFixedDim = 0x7f030109
com.tytfizikkamp:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0c0063
com.tytfizikkamp:attr/layout_constraintCircleAngle = 0x7f030269
com.tytfizikkamp:attr/suggestionRowLayout = 0x7f0303e9
com.tytfizikkamp:attr/autoCompleteTextViewStyle = 0x7f03003d
com.tytfizikkamp:id/accessibility_custom_action_13 = 0x7f080018
com.tytfizikkamp:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f110411
com.tytfizikkamp:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f110408
com.tytfizikkamp:attr/motionDurationExtraLong4 = 0x7f030304
com.tytfizikkamp:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0c00d8
com.tytfizikkamp:color/accent_material_dark = 0x7f050019
com.tytfizikkamp:attr/closeIconEnabled = 0x7f0300d2
com.tytfizikkamp:id/customPanel = 0x7f08008b
com.tytfizikkamp:dimen/mtrl_progress_circular_size_medium = 0x7f0602dc
com.tytfizikkamp:attr/marginTopSystemWindowInsets = 0x7f0302b7
com.tytfizikkamp:attr/materialAlertDialogBodyTextStyle = 0x7f0302b8
com.tytfizikkamp:attr/animationMode = 0x7f030035
com.tytfizikkamp:attr/badgeTextAppearance = 0x7f03005e
com.tytfizikkamp:anim/catalyst_slide_down = 0x7f01001c
com.tytfizikkamp:attr/materialThemeOverlay = 0x7f0302e3
com.tytfizikkamp:id/cut = 0x7f08008c
com.tytfizikkamp:attr/boxBackgroundColor = 0x7f030080
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Display2 = 0x7f11001f
com.tytfizikkamp:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f06018f
com.tytfizikkamp:attr/chipStrokeWidth = 0x7f0300c7
com.tytfizikkamp:attr/paddingStart = 0x7f030341
com.tytfizikkamp:dimen/m3_simple_item_color_selected_alpha = 0x7f0601eb
com.tytfizikkamp:dimen/m3_comp_elevated_card_container_elevation = 0x7f06010b
com.tytfizikkamp:color/material_personalized_color_error_container = 0x7f050280
com.tytfizikkamp:attr/actionModeBackground = 0x7f030010
com.tytfizikkamp:attr/badgeRadius = 0x7f030059
com.tytfizikkamp:attr/errorIconTint = 0x7f0301a1
com.tytfizikkamp:attr/colorSecondaryFixed = 0x7f03010f
com.tytfizikkamp:attr/badgeWidePadding = 0x7f030061
com.tytfizikkamp:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0c013b
com.tytfizikkamp:attr/actionBarPopupTheme = 0x7f030002
com.tytfizikkamp:attr/retryImageScaleType = 0x7f030379
com.tytfizikkamp:string/mtrl_checkbox_button_path_unchecked = 0x7f10008d
com.tytfizikkamp:attr/itemPaddingBottom = 0x7f03023b
com.tytfizikkamp:attr/layoutDescription = 0x7f030259
com.tytfizikkamp:color/material_dynamic_secondary70 = 0x7f050252
com.tytfizikkamp:attr/flow_horizontalStyle = 0x7f0301e1
com.tytfizikkamp:attr/tint = 0x7f03045d
com.tytfizikkamp:styleable/KeyAttribute = 0x7f12003f
com.tytfizikkamp:attr/colorOnErrorContainer = 0x7f0300f2
com.tytfizikkamp:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f110460
com.tytfizikkamp:attr/offsetAlignmentMode = 0x7f030332
com.tytfizikkamp:style/Widget.Material3.CompoundButton.RadioButton = 0x7f110398
com.tytfizikkamp:color/m3_ref_palette_dynamic_tertiary95 = 0x7f0500f4
com.tytfizikkamp:attr/backgroundInsetStart = 0x7f030050
com.tytfizikkamp:styleable/ButtonBarLayout = 0x7f120019
com.tytfizikkamp:dimen/m3_card_stroke_width = 0x7f0600ed
com.tytfizikkamp:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f0501a9
com.tytfizikkamp:anim/design_snackbar_out = 0x7f010021
com.tytfizikkamp:dimen/notification_media_narrow_margin = 0x7f060314
com.tytfizikkamp:color/m3_sys_color_dynamic_light_surface = 0x7f0501b6
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f110037
com.tytfizikkamp:attr/colorOnSecondaryContainer = 0x7f0300f9
com.tytfizikkamp:drawable/ripple_effect = 0x7f0700eb
com.tytfizikkamp:attr/actionModeStyle = 0x7f03001c
com.tytfizikkamp:attr/backgroundOverlayColorAlpha = 0x7f030052
com.tytfizikkamp:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0c011c
com.tytfizikkamp:color/material_personalized_color_secondary_text_inverse = 0x7f050297
com.tytfizikkamp:attr/dayStyle = 0x7f03015d
com.tytfizikkamp:color/m3_ref_palette_dynamic_secondary70 = 0x7f0500e4
com.tytfizikkamp:styleable/AppBarLayoutStates = 0x7f12000b
com.tytfizikkamp:layout/abc_action_bar_up_container = 0x7f0b0001
com.tytfizikkamp:dimen/abc_text_size_caption_material = 0x7f060042
com.tytfizikkamp:string/search_menu_title = 0x7f1000cd
com.tytfizikkamp:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f050097
com.tytfizikkamp:id/mtrl_calendar_selection_frame = 0x7f08011b
com.tytfizikkamp:attr/drawableBottomCompat = 0x7f030177
com.tytfizikkamp:layout/abc_action_mode_close_item_material = 0x7f0b0005
com.tytfizikkamp:attr/background = 0x7f03004b
com.tytfizikkamp:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0601c0
com.tytfizikkamp:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0302b9
com.tytfizikkamp:attr/itemHorizontalTranslationEnabled = 0x7f030234
com.tytfizikkamp:color/m3_filled_icon_button_container_color_selector = 0x7f05008c
com.tytfizikkamp:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0602b2
com.tytfizikkamp:color/secondary_text_disabled_material_light = 0x7f0502fe
com.tytfizikkamp:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f11004c
com.tytfizikkamp:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.tytfizikkamp:string/abc_shareactionprovider_share_with_application = 0x7f100019
com.tytfizikkamp:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0c0010
com.tytfizikkamp:attr/textAppearanceSubtitle2 = 0x7f030432
com.tytfizikkamp:color/m3_ref_palette_neutral_variant0 = 0x7f05011b
com.tytfizikkamp:drawable/notification_icon_background = 0x7f0700e2
com.tytfizikkamp:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.tytfizikkamp:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0c0051
com.tytfizikkamp:attr/colorOnSurface = 0x7f0300fc
com.tytfizikkamp:style/Theme.MaterialComponents.Dialog = 0x7f11026b
com.tytfizikkamp:attr/layout_behavior = 0x7f03025e
com.tytfizikkamp:color/m3_tabs_ripple_color = 0x7f0501fd
com.tytfizikkamp:attr/colorSecondaryVariant = 0x7f030111
com.tytfizikkamp:dimen/abc_dialog_padding_top_material = 0x7f060025
com.tytfizikkamp:style/ThemeOverlay.Design.TextInputEditText = 0x7f110292
com.tytfizikkamp:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f0501a3
com.tytfizikkamp:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0601e1
com.tytfizikkamp:attr/listLayout = 0x7f0302a6
com.tytfizikkamp:attr/visibilityMode = 0x7f03049b
com.tytfizikkamp:attr/layout_constraintTop_toTopOf = 0x7f030283
com.tytfizikkamp:attr/collapsedTitleTextColor = 0x7f0300de
com.tytfizikkamp:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f060134
com.tytfizikkamp:attr/backHandlingEnabled = 0x7f03004a
com.tytfizikkamp:attr/endIconDrawable = 0x7f030193
com.tytfizikkamp:dimen/design_navigation_item_vertical_padding = 0x7f06007b
com.tytfizikkamp:string/path_password_eye_mask_strike_through = 0x7f1000c5
com.tytfizikkamp:attr/carousel_alignment = 0x7f0300a4
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f11002d
com.tytfizikkamp:dimen/material_helper_text_default_padding_top = 0x7f06023e
com.tytfizikkamp:layout/custom_dialog = 0x7f0b001e
com.tytfizikkamp:anim/rns_ios_from_right_background_open = 0x7f01003f
com.tytfizikkamp:style/TextAppearance.Material3.HeadlineSmall = 0x7f1101f6
com.tytfizikkamp:attr/trackTintMode = 0x7f030489
com.tytfizikkamp:style/Widget.AppCompat.Spinner = 0x7f11033c
com.tytfizikkamp:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f060212
com.tytfizikkamp:attr/layoutDuringTransition = 0x7f03025a
com.tytfizikkamp:anim/rns_ios_from_left_background_open = 0x7f01003b
com.tytfizikkamp:styleable/KeyTrigger = 0x7f120046
com.tytfizikkamp:string/searchbar_scrolling_view_behavior = 0x7f1000ce
com.tytfizikkamp:attr/animateMenuItems = 0x7f030032
com.tytfizikkamp:attr/autoSizeStepGranularity = 0x7f030042
com.tytfizikkamp:attr/altSrc = 0x7f030031
com.tytfizikkamp:id/ignore = 0x7f0800da
com.tytfizikkamp:dimen/design_fab_image_size = 0x7f060071
com.tytfizikkamp:attr/percentHeight = 0x7f03034f
com.tytfizikkamp:attr/itemTextColor = 0x7f03024c
com.tytfizikkamp:attr/drawableEndCompat = 0x7f030178
com.tytfizikkamp:attr/minTouchTargetSize = 0x7f0302f8
com.tytfizikkamp:attr/actionBarTabTextStyle = 0x7f030008
com.tytfizikkamp:attr/alertDialogTheme = 0x7f03002d
com.tytfizikkamp:style/Base.Widget.MaterialComponents.Snackbar = 0x7f11011e
com.tytfizikkamp:dimen/m3_extended_fab_top_padding = 0x7f0601b5
com.tytfizikkamp:attr/chipStandaloneStyle = 0x7f0300c4
com.tytfizikkamp:styleable/ViewStubCompat = 0x7f120095
com.tytfizikkamp:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f060179
com.tytfizikkamp:string/mtrl_checkbox_button_path_group_name = 0x7f10008b
com.tytfizikkamp:attr/spinnerDropDownItemStyle = 0x7f0303c0
com.tytfizikkamp:styleable/ConstraintSet = 0x7f12002a
com.tytfizikkamp:id/CTRL = 0x7f080003
com.tytfizikkamp:color/background_material_light = 0x7f050020
com.tytfizikkamp:attr/applyMotionScene = 0x7f030037
com.tytfizikkamp:mipmap/ic_launcher_round = 0x7f0d0001
com.tytfizikkamp:attr/closeIconSize = 0x7f0300d4
com.tytfizikkamp:dimen/highlight_alpha_material_dark = 0x7f060095
com.tytfizikkamp:id/multiply = 0x7f08012c
com.tytfizikkamp:attr/motionEasingEmphasized = 0x7f030313
com.tytfizikkamp:style/Widget.AppCompat.ActionBar = 0x7f1102fb
com.tytfizikkamp:attr/hintTextAppearance = 0x7f030210
com.tytfizikkamp:color/error_color_material_dark = 0x7f05005b
com.tytfizikkamp:dimen/material_helper_text_font_1_3_padding_top = 0x7f060240
com.tytfizikkamp:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f060166
com.tytfizikkamp:attr/activeIndicatorLabelPadding = 0x7f030024
com.tytfizikkamp:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f070043
com.tytfizikkamp:style/Animation.AppCompat.DropDownUp = 0x7f110003
com.tytfizikkamp:attr/chipBackgroundColor = 0x7f0300b6
com.tytfizikkamp:attr/colorButtonNormal = 0x7f0300e7
com.tytfizikkamp:style/Widget.Material3.Toolbar = 0x7f1103fe
com.tytfizikkamp:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0c0102
com.tytfizikkamp:anim/rns_default_enter_out = 0x7f010033
com.tytfizikkamp:attr/iconPadding = 0x7f03021a
com.tytfizikkamp:string/material_clock_toggle_content_description = 0x7f10006d
com.tytfizikkamp:layout/mtrl_alert_select_dialog_item = 0x7f0b0048
com.tytfizikkamp:color/abc_hint_foreground_material_light = 0x7f050008
com.tytfizikkamp:style/Base.Widget.Material3.FloatingActionButton = 0x7f11010b
com.tytfizikkamp:color/m3_ref_palette_secondary95 = 0x7f050140
com.tytfizikkamp:attr/itemStrokeWidth = 0x7f030247
com.tytfizikkamp:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.tytfizikkamp:attr/autoAdjustToWithinGrandparentBounds = 0x7f03003c
com.tytfizikkamp:attr/onNegativeCross = 0x7f030335
com.tytfizikkamp:style/Base.Animation.AppCompat.Tooltip = 0x7f110012
com.tytfizikkamp:attr/badgeShapeAppearance = 0x7f03005a
com.tytfizikkamp:id/tag_accessibility_clickable_spans = 0x7f0801af
com.tytfizikkamp:dimen/mtrl_switch_track_width = 0x7f0602fc
com.tytfizikkamp:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f060114
com.tytfizikkamp:attr/actionModePopupWindowStyle = 0x7f030018
com.tytfizikkamp:style/ShapeAppearance.MaterialComponents.Badge = 0x7f110183
com.tytfizikkamp:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f070027
com.tytfizikkamp:id/clear_text = 0x7f08007b
com.tytfizikkamp:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f060178
com.tytfizikkamp:layout/abc_screen_content_include = 0x7f0b0014
com.tytfizikkamp:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f0501f4
com.tytfizikkamp:color/m3_ref_palette_dynamic_neutral80 = 0x7f0500ae
com.tytfizikkamp:dimen/mtrl_textinput_start_icon_margin_end = 0x7f060305
com.tytfizikkamp:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f110270
com.tytfizikkamp:attr/elevation = 0x7f03018b
com.tytfizikkamp:id/accessibility_custom_action_23 = 0x7f080023
com.tytfizikkamp:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f060252
com.tytfizikkamp:attr/actionMenuTextAppearance = 0x7f03000e
com.tytfizikkamp:attr/motionDurationMedium3 = 0x7f03030b
com.tytfizikkamp:string/catalyst_reload = 0x7f100044
com.tytfizikkamp:attr/buttonStyleSmall = 0x7f030099
com.tytfizikkamp:attr/titleCollapseMode = 0x7f030462
com.tytfizikkamp:style/Base.TextAppearance.MaterialComponents.Button = 0x7f110048
com.tytfizikkamp:dimen/m3_btn_text_btn_padding_left = 0x7f0600e1
com.tytfizikkamp:style/TextAppearance.AppCompat.Menu = 0x7f1101b0
com.tytfizikkamp:integer/mtrl_view_invisible = 0x7f090040
com.tytfizikkamp:anim/design_bottom_sheet_slide_in = 0x7f01001e
com.tytfizikkamp:attr/backgroundImage = 0x7f03004d
com.tytfizikkamp:attr/hintEnabled = 0x7f03020f
com.tytfizikkamp:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f11047d
com.tytfizikkamp:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0501bc
com.tytfizikkamp:animator/fragment_fade_enter = 0x7f020005
com.tytfizikkamp:style/Theme.AppCompat.Light.Dialog = 0x7f110226
com.tytfizikkamp:string/catalyst_dev_menu_header = 0x7f100037
com.tytfizikkamp:attr/actionBarTheme = 0x7f030009
com.tytfizikkamp:attr/counterMaxLength = 0x7f030148
com.tytfizikkamp:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.tytfizikkamp:style/Widget.Material3.CircularProgressIndicator = 0x7f11038b
com.tytfizikkamp:attr/layout_constrainedHeight = 0x7f030261
com.tytfizikkamp:color/m3_button_ripple_color = 0x7f050068
com.tytfizikkamp:attr/colorOnTertiaryFixed = 0x7f030101
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Button = 0x7f11001c
com.tytfizikkamp:dimen/mtrl_slider_tick_radius = 0x7f0602ed
com.tytfizikkamp:string/abc_action_bar_home_description = 0x7f100000
com.tytfizikkamp:dimen/mtrl_calendar_day_corner = 0x7f060277
com.tytfizikkamp:color/m3_sys_color_dark_primary = 0x7f05016e
com.tytfizikkamp:attr/motionDurationExtraLong2 = 0x7f030302
com.tytfizikkamp:style/Widget.AppCompat.ListPopupWindow = 0x7f11032c
com.tytfizikkamp:layout/abc_action_menu_layout = 0x7f0b0003
com.tytfizikkamp:id/listMode = 0x7f0800ef
com.tytfizikkamp:attr/ratingBarStyleSmall = 0x7f030371
com.tytfizikkamp:attr/actionModeWebSearchDrawable = 0x7f03001e
com.tytfizikkamp:attr/motionDurationExtraLong1 = 0x7f030301
com.tytfizikkamp:style/Widget.AppCompat.TextView = 0x7f110340
com.tytfizikkamp:anim/rns_default_enter_in = 0x7f010032
com.tytfizikkamp:attr/largeFontVerticalOffsetAdjustment = 0x7f030255
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Headline = 0x7f110022
com.tytfizikkamp:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0c00c2
com.tytfizikkamp:drawable/notification_bg_low = 0x7f0700dd
com.tytfizikkamp:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f11012f
com.tytfizikkamp:animator/fragment_open_exit = 0x7f020008
com.tytfizikkamp:color/m3_tabs_text_color = 0x7f0501ff
com.tytfizikkamp:color/dim_foreground_material_dark = 0x7f050059
com.tytfizikkamp:color/material_dynamic_secondary10 = 0x7f05024b
com.tytfizikkamp:styleable/AppCompatEmojiHelper = 0x7f12000d
com.tytfizikkamp:id/save_overlay_view = 0x7f080177
com.tytfizikkamp:attr/motionDurationLong3 = 0x7f030307
com.tytfizikkamp:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0c015c
com.tytfizikkamp:attr/liftOnScrollColor = 0x7f03029b
com.tytfizikkamp:dimen/design_navigation_item_horizontal_padding = 0x7f060079
com.tytfizikkamp:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f11005f
com.tytfizikkamp:attr/itemPadding = 0x7f03023a
com.tytfizikkamp:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0a000c
com.tytfizikkamp:id/view_clipped = 0x7f0801e8
com.tytfizikkamp:dimen/abc_list_item_height_material = 0x7f060031
com.tytfizikkamp:dimen/mtrl_extended_fab_min_height = 0x7f0602af
com.tytfizikkamp:attr/maxWidth = 0x7f0302f0
com.tytfizikkamp:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f06016a
com.tytfizikkamp:attr/textAppearanceTitleLarge = 0x7f030433
com.tytfizikkamp:color/material_personalized_color_primary_text_inverse = 0x7f050293
com.tytfizikkamp:attr/placeholderImageScaleType = 0x7f030356
com.tytfizikkamp:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1100ce
com.tytfizikkamp:attr/layout_constraintBottom_toTopOf = 0x7f030267
com.tytfizikkamp:layout/mtrl_picker_header_dialog = 0x7f0b005c
com.tytfizikkamp:attr/listPreferredItemPaddingStart = 0x7f0302af
com.tytfizikkamp:color/design_dark_default_color_on_background = 0x7f050035
com.tytfizikkamp:id/accessibility_custom_action_5 = 0x7f08002e
com.tytfizikkamp:dimen/abc_text_size_display_2_material = 0x7f060044
com.tytfizikkamp:attr/maxImageSize = 0x7f0302ec
com.tytfizikkamp:attr/colorPrimaryInverse = 0x7f03010a
com.tytfizikkamp:attr/layout_constraintTop_toBottomOf = 0x7f030282
com.tytfizikkamp:color/mtrl_tabs_icon_color_selector = 0x7f0502e5
com.tytfizikkamp:attr/badgeWithTextWidth = 0x7f030067
com.tytfizikkamp:color/m3_calendar_item_disabled_text = 0x7f05006a
com.tytfizikkamp:anim/rns_no_animation_20 = 0x7f010042
com.tytfizikkamp:style/ShapeAppearance.Material3.Corner.Large = 0x7f110179
com.tytfizikkamp:attr/layout_anchor = 0x7f03025c
com.tytfizikkamp:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0c0035
com.tytfizikkamp:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
com.tytfizikkamp:style/Platform.ThemeOverlay.AppCompat = 0x7f110144
com.tytfizikkamp:attr/textAppearanceLabelMedium = 0x7f030425
com.tytfizikkamp:style/TextAppearance.MaterialComponents.Headline5 = 0x7f11020b
com.tytfizikkamp:attr/behavior_significantVelocityThreshold = 0x7f030076
com.tytfizikkamp:style/Theme.ReactNative.TextInput.DefaultBackground = 0x7f110288
com.tytfizikkamp:animator/design_fab_show_motion_spec = 0x7f020002
com.tytfizikkamp:attr/paddingEnd = 0x7f03033e
com.tytfizikkamp:style/Widget.Material3.BottomSheet.Modal = 0x7f110368
com.tytfizikkamp:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f0501ab
com.tytfizikkamp:layout/m3_alert_dialog_title = 0x7f0b0034
com.tytfizikkamp:anim/rns_slide_in_from_bottom = 0x7f010046
com.tytfizikkamp:attr/alertDialogCenterButtons = 0x7f03002b
com.tytfizikkamp:style/Widget.Material3.Button.Icon = 0x7f11036c
com.tytfizikkamp:attr/materialCalendarHeaderConfirmButton = 0x7f0302c5
com.tytfizikkamp:dimen/abc_list_item_height_small_material = 0x7f060032
com.tytfizikkamp:attr/contentPaddingTop = 0x7f030136
com.tytfizikkamp:macro/m3_comp_slider_disabled_active_track_color = 0x7f0c010c
com.tytfizikkamp:id/action_bar_activity_content = 0x7f08003f
com.tytfizikkamp:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f0502a7
com.tytfizikkamp:drawable/$avd_hide_password__2 = 0x7f070002
com.tytfizikkamp:anim/rns_no_animation_250 = 0x7f010043
com.tytfizikkamp:attr/fontProviderQuery = 0x7f0301f5
com.tytfizikkamp:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0c0156
com.tytfizikkamp:attr/cornerSize = 0x7f030142
com.tytfizikkamp:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0601c8
com.tytfizikkamp:anim/rns_fade_out = 0x7f010038
com.tytfizikkamp:attr/alertDialogStyle = 0x7f03002c
com.tytfizikkamp:attr/materialCalendarHeaderSelection = 0x7f0302c8
com.tytfizikkamp:anim/rns_default_exit_in = 0x7f010034
com.tytfizikkamp:attr/colorOnSecondaryFixedVariant = 0x7f0300fb
com.tytfizikkamp:color/mtrl_calendar_item_stroke_color = 0x7f0502c0
com.tytfizikkamp:layout/mtrl_picker_text_input_date = 0x7f0b0061
com.tytfizikkamp:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.tytfizikkamp:attr/buttonIcon = 0x7f030093
com.tytfizikkamp:color/design_dark_default_color_surface = 0x7f05003f
com.tytfizikkamp:style/Base.V21.Theme.MaterialComponents = 0x7f1100a8
com.tytfizikkamp:color/m3_button_outline_color_selector = 0x7f050067
com.tytfizikkamp:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f06020f
com.tytfizikkamp:id/spread = 0x7f08019c
com.tytfizikkamp:anim/rns_no_animation_350 = 0x7f010044
com.tytfizikkamp:attr/textAppearanceBody2 = 0x7f030412
com.tytfizikkamp:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f110322
com.tytfizikkamp:dimen/abc_text_size_medium_material = 0x7f060049
com.tytfizikkamp:color/m3_fab_ripple_color_selector = 0x7f05008b
com.tytfizikkamp:color/m3_switch_track_tint = 0x7f05015a
com.tytfizikkamp:attr/expandedTitleMarginTop = 0x7f0301ae
com.tytfizikkamp:dimen/cardview_default_elevation = 0x7f060054
com.tytfizikkamp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f110035
com.tytfizikkamp:attr/currentState = 0x7f03014e
com.tytfizikkamp:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f060172
com.tytfizikkamp:id/open_search_view_dummy_toolbar = 0x7f080142
com.tytfizikkamp:attr/flow_lastVerticalBias = 0x7f0301e4
com.tytfizikkamp:attr/endIconCheckable = 0x7f030191
com.tytfizikkamp:style/Widget.AppCompat.ActivityChooserView = 0x7f110304
com.tytfizikkamp:attr/colorSecondaryContainer = 0x7f03010e
com.tytfizikkamp:color/m3_ref_palette_dynamic_tertiary40 = 0x7f0500ee
com.tytfizikkamp:attr/collapseIcon = 0x7f0300da
com.tytfizikkamp:color/m3_ref_palette_neutral24 = 0x7f05010a
com.tytfizikkamp:style/Widget.Autofill = 0x7f110344
com.tytfizikkamp:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0601c3
com.tytfizikkamp:attr/actionBarItemBackground = 0x7f030001
com.tytfizikkamp:dimen/m3_btn_icon_only_min_width = 0x7f0600d7
com.tytfizikkamp:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0c00da
com.tytfizikkamp:attr/floatingActionButtonLargePrimaryStyle = 0x7f0301cb
com.tytfizikkamp:attr/badgeWithTextHeight = 0x7f030063
com.tytfizikkamp:dimen/m3_nav_badge_with_text_vertical_offset = 0x7f0601be
com.tytfizikkamp:style/Platform.V25.AppCompat.Light = 0x7f11014a
com.tytfizikkamp:id/coordinator = 0x7f080086
com.tytfizikkamp:attr/iconEndPadding = 0x7f030218
com.tytfizikkamp:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f11015f
com.tytfizikkamp:attr/materialCalendarMonthNavigationButton = 0x7f0302cc
com.tytfizikkamp:anim/abc_slide_in_bottom = 0x7f010006
com.tytfizikkamp:anim/abc_tooltip_exit = 0x7f01000b
com.tytfizikkamp:drawable/$m3_avd_show_password__2 = 0x7f07000b
com.tytfizikkamp:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f110416
com.tytfizikkamp:id/filter = 0x7f0800bb
com.tytfizikkamp:attr/expandedTitleMargin = 0x7f0301aa
com.tytfizikkamp:attr/colorOnSecondaryFixed = 0x7f0300fa
com.tytfizikkamp:attr/constraint_referenced_ids = 0x7f030126
com.tytfizikkamp:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f06016d
com.tytfizikkamp:style/TextAppearance.Material3.HeadlineLarge = 0x7f1101f4
com.tytfizikkamp:attr/actionModeCloseDrawable = 0x7f030013
com.tytfizikkamp:style/TextAppearance.Design.Snackbar.Message = 0x7f1101da
com.tytfizikkamp:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.tytfizikkamp:dimen/material_emphasis_disabled_background = 0x7f060235
com.tytfizikkamp:styleable/AppCompatTextView = 0x7f120011
com.tytfizikkamp:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0501c8
com.tytfizikkamp:attr/onCross = 0x7f030333
com.tytfizikkamp:attr/endIconTint = 0x7f030197
