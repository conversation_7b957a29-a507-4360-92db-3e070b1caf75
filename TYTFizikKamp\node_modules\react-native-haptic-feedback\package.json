{"name": "react-native-haptic-feedback", "version": "2.3.3", "description": "Basic haptic feedback for iOS and android", "license": "MIT", "source": "src/index.ts", "main": "./lib/commonjs/index.js", "react-native": "src/index.ts", "module": "./lib/module/index.js", "types": "lib/typescript/index.d.ts", "scripts": {"typecheck": "tsc --noEmit --project tsconfig.test.json", "test": "jest", "build": "bob build", "lint": "eslint \"**/*.{js,ts,tsx}\""}, "keywords": ["react-native", "haptic", "haptic-feedback", "android", "ios", "native", "feedback"], "files": ["src", "lib", "android", "ios", "cpp", "*.podsp<PERSON>", "!ios/build", "!android/build", "!android/gradle", "!android/gradlew", "!android/gradlew.bat", "!android/local.properties", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__", "!**/.*"], "workspaces": ["example"], "author": "<PERSON>", "peerDependencies": {"react-native": ">=0.60.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "repository": {"type": "git", "url": "https://github.com/mkuczera/react-native-haptic-feedback.git"}, "homepage": "https://github.com/mkuczera/react-native-haptic-feedback", "readme": "https://github.com/mkuczera/react-native-haptic-feedback#readme", "bugs": {"url": "https://github.com/mkuczera/react-native-haptic-feedback/issues"}, "codegenConfig": {"name": "RNHapticFeedbackSpec", "type": "modules", "jsSrcsDir": "./src/codegenSpec", "android": {"javaPackageName": "com.mkuczera"}}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/preset-react": "^7.24.7", "@babel/runtime": "^7.21.0", "@eslint/js": "^9.8.0", "@react-native/eslint-config": "^0.74.86", "@types/jest": "^29.5.12", "@types/node": "^22.1.0", "@types/react": "^18.0.28", "@types/react-native": "^0.71.3", "babel-jest": "^29.7.0", "eslint": "9.x", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "globals": "^15.9.0", "jest": "^29.7.0", "prettier": "^2.8.5", "react-native": "^0.74.4", "react-native-builder-bob": "^0.29.0", "ts-jest": "^29.2.4", "tslib": "^2.6.3", "turbo": "^2.0.11", "typescript": "^5.5.4", "typescript-eslint": "^8.0.0"}, "packageManager": "yarn@3.6.4", "resolutions": {"@types/react": "^18.2.44"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", "typescript"]}}