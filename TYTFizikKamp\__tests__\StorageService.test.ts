import AsyncStorage from '@react-native-async-storage/async-storage';
import StorageService from '../src/services/StorageService';
import { UserProgress } from '../src/types';

// AsyncStorage mock
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}));

describe('StorageService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getUserProgress', () => {
    it('should return default progress when no data exists', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);

      const progress = await StorageService.getUserProgress();

      expect(progress).toEqual({
        completed_days: [],
        current_streak: 0,
        best_streak: 0,
        total_questions_answered: 0,
        total_correct_answers: 0,
        total_points: 0,
        last_activity: expect.any(Date),
        day_progress: {},
      });
    });

    it('should return stored progress when data exists', async () => {
      const mockProgress: UserProgress = {
        completed_days: [1, 2],
        current_streak: 2,
        best_streak: 3,
        total_questions_answered: 20,
        total_correct_answers: 15,
        total_points: 150,
        last_activity: new Date('2024-01-01'),
        day_progress: {
          1: {
            dayId: 1,
            isCompleted: true,
            completedAt: new Date('2024-01-01'),
            questionsAnswered: [],
            score: 80,
            timeSpent: 300,
          },
        },
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockProgress));

      const progress = await StorageService.getUserProgress();

      expect(progress.completed_days).toEqual([1, 2]);
      expect(progress.current_streak).toBe(2);
      expect(progress.total_questions_answered).toBe(20);
    });
  });

  describe('isDayUnlocked', () => {
    it('should unlock day 1 by default', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);

      const isUnlocked = await StorageService.isDayUnlocked(1);

      expect(isUnlocked).toBe(true);
    });

    it('should unlock next day if previous day is completed and 6 hours passed', async () => {
      const mockProgress: UserProgress = {
        completed_days: [1],
        current_streak: 1,
        best_streak: 1,
        total_questions_answered: 10,
        total_correct_answers: 8,
        total_points: 80,
        last_activity: new Date(Date.now() - 7 * 60 * 60 * 1000), // 7 hours ago
        day_progress: {
          1: {
            dayId: 1,
            isCompleted: true,
            completedAt: new Date(Date.now() - 7 * 60 * 60 * 1000),
            questionsAnswered: [],
            score: 80,
            timeSpent: 300,
          },
        },
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockProgress));

      const isUnlocked = await StorageService.isDayUnlocked(2);

      expect(isUnlocked).toBe(true);
    });

    it('should not unlock next day if 6 hours have not passed', async () => {
      const mockProgress: UserProgress = {
        completed_days: [1],
        current_streak: 1,
        best_streak: 1,
        total_questions_answered: 10,
        total_correct_answers: 8,
        total_points: 80,
        last_activity: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
        day_progress: {
          1: {
            dayId: 1,
            isCompleted: true,
            completedAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
            questionsAnswered: [],
            score: 80,
            timeSpent: 300,
          },
        },
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockProgress));

      const isUnlocked = await StorageService.isDayUnlocked(2);

      expect(isUnlocked).toBe(false);
    });
  });

  describe('completeDay', () => {
    it('should complete a day and update progress', async () => {
      const mockProgress: UserProgress = {
        completed_days: [],
        current_streak: 0,
        best_streak: 0,
        total_questions_answered: 0,
        total_correct_answers: 0,
        total_points: 0,
        last_activity: new Date(),
        day_progress: {},
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockProgress));
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      await StorageService.completeDay(1, 85, 300);

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'user_progress',
        expect.stringContaining('"completed_days":[1]')
      );
    });
  });

  describe('answerQuestion', () => {
    it('should record question answer', async () => {
      const mockProgress: UserProgress = {
        completed_days: [],
        current_streak: 0,
        best_streak: 0,
        total_questions_answered: 0,
        total_correct_answers: 0,
        total_points: 0,
        last_activity: new Date(),
        day_progress: {},
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockProgress));
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      await StorageService.answerQuestion(1, 1, true, 'A');

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'user_progress',
        expect.stringContaining('"total_questions_answered":1')
      );
    });
  });

  describe('resetProgress', () => {
    it('should clear all progress data', async () => {
      (AsyncStorage.removeItem as jest.Mock).mockResolvedValue(undefined);

      await StorageService.resetProgress();

      expect(AsyncStorage.removeItem).toHaveBeenCalledWith('user_progress');
    });
  });

  describe('getStreakInfo', () => {
    it('should return correct streak information', async () => {
      const mockProgress: UserProgress = {
        completed_days: [1, 2, 3],
        current_streak: 3,
        best_streak: 5,
        total_questions_answered: 30,
        total_correct_answers: 25,
        total_points: 250,
        last_activity: new Date(),
        day_progress: {},
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockProgress));

      const streakInfo = await StorageService.getStreakInfo();

      expect(streakInfo).toEqual({
        current: 3,
        best: 5,
        daysCompleted: [1, 2, 3],
      });
    });
  });

  describe('getStatistics', () => {
    it('should return correct statistics', async () => {
      const mockProgress: UserProgress = {
        completed_days: [1, 2],
        current_streak: 2,
        best_streak: 3,
        total_questions_answered: 20,
        total_correct_answers: 16,
        total_points: 160,
        last_activity: new Date(),
        day_progress: {
          1: {
            dayId: 1,
            isCompleted: true,
            completedAt: new Date(),
            questionsAnswered: [],
            score: 80,
            timeSpent: 300,
          },
          2: {
            dayId: 2,
            isCompleted: true,
            completedAt: new Date(),
            questionsAnswered: [],
            score: 85,
            timeSpent: 280,
          },
        },
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockProgress));

      const stats = await StorageService.getStatistics();

      expect(stats).toEqual({
        totalDaysCompleted: 2,
        totalQuestionsAnswered: 20,
        totalCorrectAnswers: 16,
        averageScore: 82.5,
        currentStreak: 2,
        bestStreak: 3,
        totalTimeSpent: 580,
        successRate: 80,
      });
    });
  });
});
