CMake Warning in C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/./

  has 188 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Desktop/30days15mobileapp/TYTFizikKamp/android/app/.cxx/Debug/5o2b6o6q/x86/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/./

  has 180 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/RNHapticFeedbackSpec/RNHapticFeedbackSpecJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


